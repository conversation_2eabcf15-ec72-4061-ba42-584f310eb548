package com.datayes.integration

import io.restassured.RestAssured.given
import io.restassured.http.ContentType
import io.restassured.path.json.JsonPath
import org.assertj.core.api.Assertions.assertThat
import org.hamcrest.Matchers.*
import org.hamcrest.collection.IsCollectionWithSize.hasSize
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.MethodOrderer
import org.junit.jupiter.api.Order
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestMethodOrder
import java.io.File
import java.time.LocalDate

/**
 * 脚本影响分析控制器端到端测试 (Script Impact Analysis Controller End-to-End Test)
 *
 * 测试 ScriptImpactAnalysisController 的所有 REST API 端点功能，包括：
 * - UC-1: 脚本文件上传 (Script File Upload)
 * - UC-2: 脚本列表查询 (Script List Query)
 * - UC-4: 脚本删除 (Script Deletion)
 * - UC-5: 脚本文件下载 (Script File Download)
 * - 脚本详情查询 (Script Details Query)
 *
 * 前提条件 (Prerequisites):
 * - 应用程序必须已经启动并运行在配置的端口上 (Application must be running on configured port)
 * - 数据库连接正常 (Database connection is available)
 * - 文件存储系统可用 (File storage system is available)
 *
 * 测试策略 (Test Strategy):
 * - 使用有序测试方法以支持端到端流程 (Ordered test methods for end-to-end flow)
 * - 覆盖正面和负面测试场景 (Cover positive and negative test scenarios)
 * - 验证数据格式和业务逻辑 (Validate data format and business logic)
 */
@DisplayName("脚本影响分析控制器端到端测试")
@TestMethodOrder(MethodOrderer.OrderAnnotation::class)
class ScriptImpactAnalysisControllerIT : RestApiIntegrationTestBase() {

    companion object {
        // 测试数据 (Test Data)
        private const val TEST_UPLOAD_USER = "integration-test-user"
        private const val TEST_SQL_SCRIPT_NAME = "test_sample.sql"
        private const val TEST_SHELL_SCRIPT_NAME = "test_sample.sh"

        private val TEST_SQL_CONTENT = """
            -- 测试SQL脚本 (Test SQL Script)
            SELECT * FROM test_table;
            INSERT INTO output_table SELECT col1, col2 FROM input_table WHERE condition = 'test';
        """.trimIndent()

        private val TEST_SHELL_CONTENT = """
            #!/bin/bash
            # 测试Shell脚本 (Test Shell Script)
            echo "Starting data processing..."
            hdfs dfs -ls /input/path
            spark-submit --class TestApp test.jar
        """.trimIndent()

        // 存储测试中创建的脚本ID，用于后续测试 (Store created script IDs for subsequent tests)
        private var createdSqlScriptId: Long? = null
        private var createdShellScriptId: Long? = null
        private var actualSqlScriptName: String? = null
        private var actualShellScriptName: String? = null
    }

    @Test
    @Order(1)
    @DisplayName("UC-1: 应该成功上传SQL脚本文件")
    fun `should successfully upload SQL script file`() {
        // 创建临时测试文件 (Create temporary test file)
        val tempFile = File.createTempFile("test", ".sql")
        tempFile.writeText(TEST_SQL_CONTENT)
        tempFile.deleteOnExit()

        val response = given()
            .contentType(ContentType.MULTIPART)
            .multiPart("file", tempFile, "application/octet-stream")
            .multiPart("uploadUser", TEST_UPLOAD_USER)
            .`when`()
            .post("/v1/script-impact/upload")
            .then()
            .statusCode(200)
            .contentType("application/json")
            .body("success", equalTo(true))
            .body("message", notNullValue())
            .body("data.scriptId", notNullValue())
            .body("data.scriptName", containsString(".sql"))
            .body("data.scriptType", equalTo("SQL"))
            .body("data.uploadUser", equalTo(TEST_UPLOAD_USER))
            .body("data.analysisStatus", equalTo("PENDING"))
            .body("data.fileSize", greaterThan(0))
            .body("data.createdAt", notNullValue())
            .extract()
            .response()

        // 保存脚本ID和实际脚本名称用于后续测试 (Save script ID and actual script name for subsequent tests)
        val jsonPath = JsonPath.from(response.asString())
        createdSqlScriptId = jsonPath.getLong("data.scriptId")
        actualSqlScriptName = jsonPath.getString("data.scriptName")

        println("a1b2c3d4 | SQL脚本上传成功: scriptId=$createdSqlScriptId, scriptName=$actualSqlScriptName")
    }

    @Test
    @Order(2)
    @DisplayName("UC-1: 应该成功上传Shell脚本文件")
    fun `should successfully upload Shell script file`() {
        val tempFile = File.createTempFile("test", ".sh")
        tempFile.writeText(TEST_SHELL_CONTENT)
        tempFile.deleteOnExit()

        val response = given()
            .contentType(ContentType.MULTIPART)
            .multiPart("file", tempFile, "application/octet-stream")
            .multiPart("uploadUser", TEST_UPLOAD_USER)
            .`when`()
            .post("/v1/script-impact/upload")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data.scriptType", equalTo("SHELL"))
            .extract()
            .response()

        val jsonPath = JsonPath.from(response.asString())
        createdShellScriptId = jsonPath.getLong("data.scriptId")
        actualShellScriptName = jsonPath.getString("data.scriptName")

        println("e5f6g7h8 | Shell脚本上传成功: scriptId=$createdShellScriptId, scriptName=$actualShellScriptName")
    }

    @Test
    @Order(3)
    @DisplayName("UC-1: 应该拒绝重复文件上传")
    fun `should reject duplicate file upload`() {
        val tempFile = File.createTempFile("test", ".sql")
        tempFile.writeText(TEST_SQL_CONTENT)
        tempFile.deleteOnExit()

        given()
            .contentType(ContentType.MULTIPART)
            .multiPart("file", tempFile, "application/octet-stream")
            .multiPart("uploadUser", TEST_UPLOAD_USER)
            .`when`()
            .post("/v1/script-impact/upload")
            .then()
            .statusCode(200) // Now returns 200 for all responses including conflicts
            .body("success", equalTo(false))
            .body("message", containsString("已存在"))
            .body("data.scriptId", equalTo(createdSqlScriptId!!.toInt()))

        println("i9j0k1l2 | 重复文件上传正确被拒绝")
    }

    @Test
    @Order(4)
    @DisplayName("UC-1: 应该验证上传参数")
    fun `should validate upload parameters`() {
        // 测试空文件名 (Test empty filename)
        val tempFile = File.createTempFile("test", ".sql")
        tempFile.writeText("SELECT 1;")
        tempFile.deleteOnExit()

        given()
            .contentType(ContentType.MULTIPART)
            .multiPart("file", tempFile, "application/octet-stream")
            .multiPart("uploadUser", "")
            .`when`()
            .post("/v1/script-impact/upload")
            .then()
            .statusCode(400)
            .body("success", equalTo(false))
            .body("message", containsString("不能为空"))

        println("m3n4o5p6 | 参数验证测试完成")
    }

    @Test
    @Order(5)
    @DisplayName("UC-2: 应该成功查询脚本列表")
    fun `should successfully query script list`() {
        val response = given()
            .`when`()
            .get("/v1/script-impact/scripts")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data.content", notNullValue())
            .body("data.content", hasSize<Any>(greaterThanOrEqualTo(2)))
            .body("data.page", equalTo(0))
            .body("data.size", equalTo(20))
            .body("data.totalElements", greaterThanOrEqualTo(2))
            .body("data.totalPages", greaterThanOrEqualTo(1))
            .extract()
            .response()

        // 验证返回的脚本包含我们创建的脚本 (Verify returned scripts include our created scripts)
        val jsonPath = JsonPath.from(response.asString())
        val scriptIds = jsonPath.getList<Int>("data.content.id")
        assertThat(scriptIds).contains(createdSqlScriptId!!.toInt(), createdShellScriptId!!.toInt())

        println("q7r8s9t0 | 脚本列表查询成功: 共${scriptIds.size}个脚本")
    }

    @Test
    @Order(6)
    @DisplayName("UC-2: 应该支持脚本名称过滤查询")
    fun `should support script name filtering`() {
        given()
            .queryParam("scriptName", "test_sample")
            .`when`()
            .get("/v1/script-impact/scripts")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data.content", hasSize<Any>(greaterThanOrEqualTo(2)))
            .body("data.content[0].scriptName", containsString("test_sample"))

        println("u1v2w3x4 | 脚本名称过滤查询测试完成")
    }

    @Test
    @Order(7)
    @DisplayName("UC-2: 应该支持脚本类型过滤查询")
    fun `should support script type filtering`() {
        given()
            .queryParam("scriptType", "SQL")
            .`when`()
            .get("/v1/script-impact/scripts")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data.content", hasSize<Any>(greaterThanOrEqualTo(1)))
            .body("data.content[0].scriptType", equalTo("SQL"))

        println("y5z6a7b8 | 脚本类型过滤查询测试完成")
    }

    @Test
    @Order(8)
    @DisplayName("UC-2: 应该支持用户过滤查询")
    fun `should support user filtering`() {
        given()
            .queryParam("uploadUser", TEST_UPLOAD_USER)
            .`when`()
            .get("/v1/script-impact/scripts")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data.content", hasSize<Any>(greaterThanOrEqualTo(2)))
            .body("data.content[0].uploadUser", equalTo(TEST_UPLOAD_USER))

        println("c9d0e1f2 | 用户过滤查询测试完成")
    }

    @Test
    @Order(9)
    @DisplayName("UC-2: 应该支持分页查询")
    fun `should support pagination`() {
        given()
            .queryParam("page", 0)
            .queryParam("size", 1)
            .`when`()
            .get("/v1/script-impact/scripts")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data.content", hasSize<Any>(1))
            .body("data.page", equalTo(0))
            .body("data.size", equalTo(1))

        println("g3h4i5j6 | 分页查询测试完成")
    }

    @Test
    @Order(10)
    @DisplayName("应该成功获取脚本详情")
    fun `should successfully get script details`() {
        given()
            .pathParam("scriptId", createdSqlScriptId!!)
            .`when`()
            .get("/v1/script-impact/scripts/{scriptId}")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data.id", equalTo(createdSqlScriptId!!.toInt()))
            .body("data.scriptName", equalTo(actualSqlScriptName))
            .body("data.scriptType", equalTo("SQL"))
            .body("data.scriptContent", containsString("SELECT"))
            .body("data.uploadUser", equalTo(TEST_UPLOAD_USER))
            .body("data.analysisStatus", notNullValue())
            .body("data.createdAt", notNullValue())
            .body("data.updatedAt", notNullValue())

        println("k7l8m9n0 | 脚本详情查询成功: scriptId=$createdSqlScriptId")
    }

    @Test
    @Order(11)
    @DisplayName("应该返回404当脚本不存在时")
    fun `should return 404 when script not found`() {
        val nonExistentId = 999999L

        given()
            .pathParam("scriptId", nonExistentId)
            .`when`()
            .get("/v1/script-impact/scripts/{scriptId}")
            .then()
            .statusCode(404)
            .body("success", equalTo(false))
            .body("message", containsString("不存在"))

        println("o1p2q3r4 | 脚本不存在测试完成")
    }

    @Test
    @Order(12)
    @DisplayName("UC-5: 应该成功下载脚本文件")
    fun `should successfully download script file`() {
        val response = given()
            .pathParam("scriptId", createdSqlScriptId!!)
            .`when`()
            .get("/v1/script-impact/scripts/{scriptId}/download")
            .then()
            .statusCode(200)
            .header("Content-Disposition", containsString("attachment"))
            .header("Content-Disposition", containsString(actualSqlScriptName!!))
            .header("Content-Type", anyOf(equalTo("application/sql"), containsString("application"), containsString("octet-stream")))
            .extract()
            .response()

        val downloadedContent = response.asString()
        assertThat(downloadedContent).contains("SELECT")
        assertThat(downloadedContent).contains("test_table")

        println("s5t6u7v8 | 脚本文件下载成功: 内容长度=${downloadedContent.length}")
    }

    @Test
    @Order(13)
    @DisplayName("UC-5: 下载不存在的脚本应返回404")
    fun `should return 404 when downloading non-existent script`() {
        val nonExistentId = 999999L

        given()
            .pathParam("scriptId", nonExistentId)
            .`when`()
            .get("/v1/script-impact/scripts/{scriptId}/download")
            .then()
            .statusCode(404)

        println("w9x0y1z2 | 下载不存在脚本测试完成")
    }

    @Test
    @Order(14)
    @DisplayName("UC-4: 应该成功删除脚本")
    fun `should successfully delete script`() {
        // 删除Shell脚本 (Delete Shell script)
        given()
            .pathParam("scriptId", createdShellScriptId!!)
            .`when`()
            .delete("/v1/script-impact/scripts/{scriptId}")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("message", containsString("删除成功"))

        // 验证脚本已被删除 (Verify script has been deleted)
        given()
            .pathParam("scriptId", createdShellScriptId!!)
            .`when`()
            .get("/v1/script-impact/scripts/{scriptId}")
            .then()
            .statusCode(404)

        println("a3b4c5d6 | Shell脚本删除成功: scriptId=$createdShellScriptId")
    }

    @Test
    @Order(15)
    @DisplayName("UC-4: 删除不存在的脚本应返回404")
    fun `should return 404 when deleting non-existent script`() {
        val nonExistentId = 999999L

        given()
            .pathParam("scriptId", nonExistentId)
            .`when`()
            .delete("/v1/script-impact/scripts/{scriptId}")
            .then()
            .statusCode(404)
            .body("success", equalTo(false))
            .body("message", containsString("不存在"))

        println("e7f8g9h0 | 删除不存在脚本测试完成")
    }

    @Test
    @Order(16)
    @DisplayName("应该验证查询参数格式")
    fun `should validate query parameter formats`() {
        // 测试无效的日期格式 (Test invalid date format)
        given()
            .queryParam("startDate", "invalid-date")
            .`when`()
            .get("/v1/script-impact/scripts")
            .then()
            .statusCode(400)

        // 测试无效的页码 (Test invalid page number)
        given()
            .queryParam("page", -1)
            .`when`()
            .get("/v1/script-impact/scripts")
            .then()
            .statusCode(anyOf(equalTo(400), equalTo(500)))

        println("i1j2k3l4 | 查询参数格式验证测试完成")
    }

    @Test
    @Order(17)
    @DisplayName("应该支持日期范围过滤")
    fun `should support date range filtering`() {
        val today = LocalDate.now()
        val yesterday = today.minusDays(1)
        val tomorrow = today.plusDays(1)

        given()
            .queryParam("startDate", yesterday.toString())
            .queryParam("endDate", tomorrow.toString())
            .`when`()
            .get("/v1/script-impact/scripts")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data.content", hasSize<Any>(greaterThanOrEqualTo(1)))

        println("m5n6o7p8 | 日期范围过滤测试完成")
    }

    @Test
    @Order(18)
    @DisplayName("清理测试: 删除剩余的测试脚本")
    fun `cleanup test delete remaining test scripts`() {
        // 删除剩余的SQL脚本 (Delete remaining SQL script)
        if (createdSqlScriptId != null) {
            given()
                .pathParam("scriptId", createdSqlScriptId!!)
                .`when`()
                .delete("/v1/script-impact/scripts/{scriptId}")
                .then()
                .statusCode(anyOf(equalTo(200), equalTo(404))) // 可能已被删除

            println("q9r0s1t2 | 清理SQL脚本: scriptId=$createdSqlScriptId")
        }

        // 验证所有测试脚本都已清理 (Verify all test scripts are cleaned up)
        given()
            .queryParam("uploadUser", TEST_UPLOAD_USER)
            .`when`()
            .get("/v1/script-impact/scripts")
            .then()
            .statusCode(200)
            .body("data.content", hasSize<Any>(0))

        println("u3v4w5x6 | 测试数据清理完成")
    }
} 