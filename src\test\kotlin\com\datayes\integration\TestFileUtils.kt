package com.datayes.integration

import java.io.File
import java.nio.file.Files
import java.nio.file.StandardOpenOption

/**
 * 测试文件工具类 (Test File Utilities)
 * 
 * 提供用于创建和管理测试文件的工具方法，主要用于脚本上传测试
 */
object TestFileUtils {

    /**
     * 创建临时SQL脚本文件 (Create temporary SQL script file)
     * 
     * @param filename 文件名
     * @param content SQL脚本内容
     * @return 临时文件对象
     */
    fun createTempSqlFile(filename: String, content: String): File {
        val tempFile = File.createTempFile("test_", ".sql")
        tempFile.writeText(content, Charsets.UTF_8)
        tempFile.deleteOnExit()
        return tempFile
    }

    /**
     * 创建临时Shell脚本文件 (Create temporary Shell script file)
     * 
     * @param filename 文件名
     * @param content Shell脚本内容
     * @return 临时文件对象
     */
    fun createTempShellFile(filename: String, content: String): File {
        val tempFile = File.createTempFile("test_", ".sh")
        tempFile.writeText(content, Charsets.UTF_8)
        tempFile.deleteOnExit()
        return tempFile
    }

    /**
     * 创建空文件 (Create empty file)
     * 
     * @param extension 文件扩展名
     * @return 空的临时文件对象
     */
    fun createEmptyFile(extension: String): File {
        val tempFile = File.createTempFile("empty_", extension)
        tempFile.deleteOnExit()
        return tempFile
    }

    /**
     * 创建大文件 (Create large file for testing file size limits)
     * 
     * @param sizeInBytes 文件大小（字节）
     * @param extension 文件扩展名
     * @return 临时文件对象
     */
    fun createLargeFile(sizeInBytes: Long, extension: String): File {
        val tempFile = File.createTempFile("large_", extension)
        val content = "-- Large test file\n".repeat((sizeInBytes / 20).toInt())
        tempFile.writeText(content, Charsets.UTF_8)
        tempFile.deleteOnExit()
        return tempFile
    }

    /**
     * 标准测试SQL脚本内容 (Standard test SQL script content)
     */
    val SAMPLE_SQL_CONTENT = """
        -- 示例数据处理SQL脚本 (Sample Data Processing SQL Script)
        -- 用于测试血缘分析功能 (For testing lineage analysis functionality)
        
        -- 数据清理 (Data Cleaning)
        DELETE FROM staging_table WHERE created_date < '2023-01-01';
        
        -- 数据转换 (Data Transformation)
        INSERT INTO fact_sales (
            sales_id,
            product_id, 
            customer_id,
            sales_amount,
            sales_date
        )
        SELECT 
            s.id,
            p.product_id,
            c.customer_id,
            s.amount,
            s.transaction_date
        FROM staging_sales s
        JOIN dim_product p ON s.product_code = p.product_code
        JOIN dim_customer c ON s.customer_code = c.customer_code
        WHERE s.status = 'APPROVED'
          AND s.transaction_date >= CURRENT_DATE - INTERVAL 30 DAY;
        
        -- 聚合统计 (Aggregation Statistics)
        INSERT INTO monthly_sales_summary (
            year_month,
            total_sales,
            transaction_count,
            avg_sales_amount
        )
        SELECT 
            DATE_FORMAT(sales_date, '%Y-%m') as year_month,
            SUM(sales_amount) as total_sales,
            COUNT(*) as transaction_count,
            AVG(sales_amount) as avg_sales_amount
        FROM fact_sales
        WHERE sales_date >= DATE_SUB(CURRENT_DATE, INTERVAL 1 MONTH)
        GROUP BY DATE_FORMAT(sales_date, '%Y-%m');
    """.trimIndent()

    /**
     * 标准测试Shell脚本内容 (Standard test Shell script content)
     */
    val SAMPLE_SHELL_CONTENT = """
        #!/bin/bash
        
        # 数据处理Shell脚本示例 (Sample Data Processing Shell Script)
        # 用于测试HDFS文件操作和Spark作业调度 (For testing HDFS operations and Spark job scheduling)
        
        set -e  # 遇到错误立即退出 (Exit on error)
        
        # 配置变量 (Configuration Variables)
        HDFS_INPUT_PATH="/data/input/sales"
        HDFS_OUTPUT_PATH="/data/output/processed_sales"
        SPARK_APP_JAR="/apps/spark/sales-processor.jar"
        SPARK_MAIN_CLASS="com.datayes.processor.SalesDataProcessor"
        
        echo "开始数据处理流程 (Starting data processing pipeline)..."
        echo "输入路径 (Input path): ${'$'}{HDFS_INPUT_PATH}"
        echo "输出路径 (Output path): ${'$'}{HDFS_OUTPUT_PATH}"
        
        # 检查输入数据是否存在 (Check if input data exists)
        if ! hdfs dfs -test -d "${'$'}{HDFS_INPUT_PATH}"; then
            echo "错误: 输入路径不存在 (Error: Input path does not exist): ${'$'}{HDFS_INPUT_PATH}"
            exit 1
        fi
        
        # 清理输出目录 (Clean output directory)
        echo "清理输出目录 (Cleaning output directory)..."
        hdfs dfs -rm -r -f "${'$'}{HDFS_OUTPUT_PATH}"
        
        # 获取输入文件列表 (Get input file list)
        echo "输入文件列表 (Input file list):"
        hdfs dfs -ls "${'$'}{HDFS_INPUT_PATH}"
        
        # 提交Spark作业 (Submit Spark job)
        echo "提交Spark数据处理作业 (Submitting Spark data processing job)..."
        spark-submit \
            --class "${'$'}{SPARK_MAIN_CLASS}" \
            --master yarn \
            --deploy-mode cluster \
            --driver-memory 2g \
            --executor-memory 4g \
            --executor-cores 2 \
            --num-executors 10 \
            --conf spark.sql.adaptive.enabled=true \
            --conf spark.sql.adaptive.coalescePartitions.enabled=true \
            "${'$'}{SPARK_APP_JAR}" \
            "${'$'}{HDFS_INPUT_PATH}" \
            "${'$'}{HDFS_OUTPUT_PATH}"
        
        # 验证输出结果 (Verify output results)
        echo "验证输出结果 (Verifying output results)..."
        if hdfs dfs -test -d "${'$'}{HDFS_OUTPUT_PATH}"; then
            echo "数据处理成功完成 (Data processing completed successfully)"
            echo "输出文件列表 (Output file list):"
            hdfs dfs -ls -R "${'$'}{HDFS_OUTPUT_PATH}"
            
            # 显示处理统计信息 (Show processing statistics)
            echo "处理统计信息 (Processing statistics):"
            hdfs dfs -du -s -h "${'$'}{HDFS_OUTPUT_PATH}"
        else
            echo "错误: 数据处理失败 (Error: Data processing failed)"
            exit 1
        fi
        
        echo "数据处理流程完成 (Data processing pipeline completed)"
    """.trimIndent()

    /**
     * 无效的SQL脚本内容 (Invalid SQL script content)
     */
    val INVALID_SQL_CONTENT = """
        -- 这是一个包含语法错误的SQL脚本 (This is a SQL script with syntax errors)
        SELCT * FORM invalid_table; -- 拼写错误 (Spelling errors)
        INSERT INTO WHERE VALUES; -- 语法错误 (Syntax errors)
    """.trimIndent()

    /**
     * 无效的Shell脚本内容 (Invalid Shell script content)
     */
    val INVALID_SHELL_CONTENT = """
        #!/bin/bash
        # 这是一个包含语法错误的Shell脚本 (This is a Shell script with syntax errors)
        if [ condition; then  # 缺少闭合括号 (Missing closing bracket)
            echo "This will cause syntax error"
        # 缺少fi (Missing fi)
    """.trimIndent()
} 