# 血缘任务管理系统部署指南
## Lineage Task Management System Deployment Guide

### 📋 系统概述 (System Overview)

血缘任务管理系统（Lineage Task Management System）为数据血缘采集提供了统一的任务管理能力，包括：

1. **批量血缘处理** - 一键处理所有活跃的数据交换作业
2. **任务查询管理** - 分页查询和多维度过滤血缘任务
3. **任务重跑机制** - 支持重新执行失败或需要更新的血缘任务
4. **状态跟踪监控** - 完整的任务执行历史和状态变化跟踪

---

## 🚀 部署步骤 (Deployment Steps)

### 第1步：数据库表结构更新 (Database Schema Update)

执行数据库更新脚本，为 `lineage_tasks` 表添加任务管理所需的新字段：

```bash
# 连接到MySQL数据库并执行更新脚本
mysql -h your_host -u your_username -p your_database < src/main/resources/db/add_lineage_task_fields.sql
```

**新增字段说明**：
- `job_key`: 作业唯一标识符，格式为 `{readerJobId}_{writeJobId}`
- `processing_time_ms`: 处理耗时（毫秒）
- `has_changes`: 是否检测到血缘变更
- `batch_id`: 批处理标识，用于关联同一批次的任务
- `execution_count`: 执行次数统计
- `last_execution_id`: 最后一次执行的唯一标识

### 第2步：应用程序部署 (Application Deployment)

```bash
# 编译应用
mvn clean package -DskipTests

# 运行应用
java -jar target/dgp-lineage-collector-0.0.1-SNAPSHOT.jar
```

### 第3步：验证部署 (Deployment Verification)

检查应用启动日志，确认血缘任务管理相关的组件正常加载：

```bash
# 检查应用健康状态
curl http://localhost:8080/actuator/health

# 验证API接口可用性
curl http://localhost:8080/api/v1/lineage/tasks?page=0&size=5
```

---

## 🔧 API 使用指南 (API Usage Guide)

### 1. 批量处理所有活跃作业 (Process All Active Jobs)

**接口**: `POST /api/v1/lineage/tasks/process-all`

```bash
curl -X POST http://localhost:8080/api/v1/lineage/tasks/process-all \
  -H "Content-Type: application/json" \
  -d '{
    "executedBy": "admin",
    "batchId": "manual_batch_001"
  }'
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "batchId": "batch_1734621600000",
    "totalJobs": 25,
    "processedTasks": [...],
    "summary": {
      "successful": 23,
      "failed": 2,
      "unchanged": 18,
      "updated": 5
    },
    "processingTimeMs": 15230
  },
  "message": "批量处理完成"
}
```

### 2. 查询血缘任务 (Query Lineage Tasks)

**接口**: `GET /api/v1/lineage/tasks`

```bash
# 基础查询
curl "http://localhost:8080/api/v1/lineage/tasks?page=0&size=20"

# 按状态过滤
curl "http://localhost:8080/api/v1/lineage/tasks?status=SUCCESS&page=0&size=10"

# 按时间范围查询
curl "http://localhost:8080/api/v1/lineage/tasks?dateFrom=2024-12-01&dateTo=2024-12-19&sort=createdAt,desc"

# 模糊搜索任务名称
curl "http://localhost:8080/api/v1/lineage/tasks?taskName=数据同步&page=0&size=10"
```

**查询参数说明**:
- `page`: 页码（从0开始）
- `size`: 页大小（最大100）
- `status`: 任务状态（PENDING, RUNNING, SUCCESS, FAILED, CANCELLED）
- `taskType`: 任务类型（DATA_EXCHANGE_PLATFORM, BASH_SCRIPT, etc.）
- `taskName`: 任务名称模糊搜索
- `createdBy`: 创建人过滤
- `dateFrom/dateTo`: 创建时间范围
- `sort`: 排序（格式：字段名,方向）

### 3. 重跑血缘任务 (Rerun Lineage Task)

**接口**: `POST /api/v1/lineage/tasks/{taskId}/rerun`

```bash
curl -X POST http://localhost:8080/api/v1/lineage/tasks/1001/rerun \
  -H "Content-Type: application/json" \
  -d '{
    "executedBy": "admin",
    "reason": "数据源结构变更，需重新分析血缘"
  }'
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "taskId": 1001,
    "executionId": "exec_1734621600001",
    "status": "RUNNING",
    "message": "任务重跑已启动"
  }
}
```

---

## 📊 监控和维护 (Monitoring and Maintenance)

### 任务状态监控 (Task Status Monitoring)

```bash
# 查看最近的任务执行情况
curl "http://localhost:8080/api/v1/lineage/tasks?sort=executedAt,desc&size=10"

# 查看失败的任务
curl "http://localhost:8080/api/v1/lineage/tasks?status=FAILED"

# 查看指定批次的任务
curl "http://localhost:8080/api/v1/lineage/tasks?batchId=batch_1734621600000"
```

### 性能指标监控 (Performance Metrics)

关键性能指标（KPIs）：
- **任务处理成功率**: 成功任务数 / 总任务数
- **平均处理时间**: 所有任务的平均 `processingTimeMs`
- **批量处理效率**: 单批次处理时间和任务数量的比率
- **重跑频率**: 重跑任务数 / 总任务数

### 数据库维护 (Database Maintenance)

```sql
-- 清理历史执行记录（保留最近30天）
DELETE FROM lineage_execution_logs 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 查看任务统计信息
SELECT 
    task_status,
    COUNT(*) as task_count,
    AVG(processing_time_ms) as avg_processing_time,
    MAX(processing_time_ms) as max_processing_time
FROM lineage_tasks 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY task_status;

-- 查看批处理统计
SELECT 
    batch_id,
    COUNT(*) as total_tasks,
    SUM(CASE WHEN task_status = 'SUCCESS' THEN 1 ELSE 0 END) as successful_tasks,
    AVG(processing_time_ms) as avg_processing_time
FROM lineage_tasks 
WHERE batch_id IS NOT NULL
GROUP BY batch_id 
ORDER BY MAX(created_at) DESC 
LIMIT 10;
```

---

## 🛠️ 故障排查 (Troubleshooting)

### 常见问题和解决方案

#### 1. 批量处理失败
**症状**: 批量处理返回大量失败任务
**排查步骤**:
```bash
# 检查数据交换平台连接
curl "http://localhost:8080/api/v1/lineage/tasks?status=FAILED&size=1"

# 查看错误日志
tail -f logs/application.log | grep "LineageTaskService"
```

**解决方案**:
- 检查数据交换平台数据库连接配置
- 验证 SQL 解析器的兼容性
- 确认血缘变更检测服务正常工作

#### 2. 任务重跑失败
**症状**: 重跑任务时返回状态冲突错误
**排查步骤**:
```sql
-- 检查任务当前状态
SELECT id, task_name, task_status, executed_at, completed_at 
FROM lineage_tasks 
WHERE id = 任务ID;
```

**解决方案**:
- 确保任务不在 RUNNING 状态
- 检查任务对应的数据交换作业是否仍然活跃
- 验证作业键（job_key）格式是否正确

#### 3. 查询性能问题
**症状**: 任务查询响应时间过长
**排查步骤**:
```sql
-- 检查索引使用情况
EXPLAIN SELECT * FROM lineage_tasks 
WHERE task_status = 'SUCCESS' 
ORDER BY created_at DESC 
LIMIT 20;
```

**解决方案**:
- 确认数据库索引已正确创建
- 优化查询条件的组合
- 考虑增加数据分区策略

---

## 📈 最佳实践 (Best Practices)

### 1. 批量处理调度建议
- **定时执行**: 建议在业务低峰期（如夜间）执行批量处理
- **分批处理**: 对于大量作业，考虑分批处理以降低系统负载
- **监控告警**: 设置处理失败率告警阈值（如 > 10%）

### 2. 任务管理策略
- **命名规范**: 使用清晰的任务命名规范，便于查询和管理
- **状态跟踪**: 定期检查长时间处于 RUNNING 状态的任务
- **历史清理**: 定期清理历史执行记录，保持系统性能

### 3. 错误处理原则
- **优雅降级**: 单个任务失败不应影响整个批次的处理
- **重试机制**: 对于临时性错误，实现自动重试机制
- **详细日志**: 记录详细的错误信息，便于问题定位

---

## 🔄 版本更新 (Version Updates)

### v1.0 新特性
- ✅ 批量血缘处理 API
- ✅ 分页查询和多维度过滤
- ✅ 任务重跑机制
- ✅ 状态跟踪和历史记录
- ✅ 统一 API 响应格式

### 下一版本规划
- 🔄 定时调度功能
- 🔄 任务优先级管理
- 🔄 邮件和 Webhook 通知
- 🔄 可视化管理界面
- 🔄 性能监控仪表板

---

*如有问题或建议，请联系开发团队或查看项目文档。* 