package com.datayes.lineage

import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.jdbc.core.RowMapper
import org.springframework.jdbc.support.GeneratedKeyHolder
import org.springframework.stereotype.Repository
import java.sql.Statement

/**
 * 作业处理历史仓库 (Job Processing History Repository)
 */
@Repository
class JobProcessingHistoryRepository(private val jdbcTemplate: JdbcTemplate) {
    
    /**
     * 查找作业的最新处理记录
     */
    fun findLatestByJobKey(jobKey: String): JobProcessingHistory? {
        val sql = """
            SELECT * FROM lineage_job_processing_history 
            WHERE job_key = ? 
            ORDER BY processed_at DESC 
            LIMIT 1
        """.trimIndent()

        return jdbcTemplate.query(sql, jobProcessingHistoryRowMapper, jobKey).firstOrNull()
    }
    
    /**
     * 保存处理历史记录
     */
    fun save(history: JobProcessingHistory): Long {
        val keyHolder = GeneratedKeyHolder()
        jdbcTemplate.update({ connection ->
            val ps = connection.prepareStatement(
                """
                INSERT INTO lineage_job_processing_history 
                (job_key, job_type, reader_job_id, write_job_id, processing_result, changes_detected, 
                 processing_duration_ms, lineage_hash, error_message)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """.trimIndent(),
                Statement.RETURN_GENERATED_KEYS
            )
            ps.setString(1, history.jobKey)
            ps.setString(2, history.jobType.name)
            ps.setString(3, history.readerJobId)
            ps.setString(4, history.writeJobId)
            ps.setString(5, history.processingResult.name)
            ps.setBoolean(6, history.changesDetected)
            ps.setLong(7, history.processingDurationMs)
            ps.setString(8, history.lineageHash)
            ps.setString(9, history.errorMessage)
            ps
        }, keyHolder)
        
        return keyHolder.key!!.toLong()
    }
    
    private val jobProcessingHistoryRowMapper = RowMapper<JobProcessingHistory> { rs, _ ->
        JobProcessingHistory(
            id = rs.getLong("id"),
            jobKey = rs.getString("job_key"),
            jobType = JobType.valueOf(rs.getString("job_type")),
            readerJobId = rs.getString("reader_job_id"),
            writeJobId = rs.getString("write_job_id"),
            processedAt = rs.getTimestamp("processed_at").toLocalDateTime(),
            processingResult = ProcessingResult.valueOf(rs.getString("processing_result")),
            changesDetected = rs.getBoolean("changes_detected"),
            processingDurationMs = rs.getLong("processing_duration_ms"),
            lineageHash = rs.getString("lineage_hash"),
            errorMessage = rs.getString("error_message")
        )
    }
}