package com.datayes.dataexchange

import com.datayes.lineage.*
import com.datayes.sql.SqlParser
import com.datayes.sql.SqlParsingException
import com.datayes.util.TableNameNormalizer

/**
 * 血缘转换器 (Lineage Converter)
 *
 * 提供将数据交互平台作业数据转换为血缘信息的功能
 */
object DataExchangeJobLineageConverter {

    private val log = org.slf4j.LoggerFactory.getLogger(this.javaClass)

    /**
     * 将数据交互平台作业转换为数据血缘信息
     *
     * @param dataExchangeJob 数据交互平台作业数据
     * @return 血缘构建结果
     */
    fun convertToLineage(dataExchangeJob: DataExchangeJob): LineageResult {
        val warnings = mutableListOf<String>()
        val errors = mutableListOf<String>()

        try {
            // 解析源数据库信息
            val sourceDatabase = DatabaseInfo.parseFromJdbcUrl(dataExchangeJob.dbReader)
            if (sourceDatabase == null) {
                errors.add("无法解析源数据库连接字符串: ${dataExchangeJob.dbReader}")
                return LineageResult(null, warnings, errors, false)
            }

            // 解析目标数据库信息
            val targetDatabase = DatabaseInfo.parseFromJdbcUrl(dataExchangeJob.dbWriter)
            if (targetDatabase == null) {
                errors.add("无法解析目标数据库连接字符串: ${dataExchangeJob.dbWriter}")
                return LineageResult(null, warnings, errors, false)
            }

            // 从SQL中解析源表信息
            val sourceTables = parseSourceTablesFromSql(dataExchangeJob.readerSql, sourceDatabase, warnings)

            // 降级方案：如果SQL解析失败，使用配置中的表名
            val finalSourceTables = sourceTables.ifEmpty {
                warnings.add("使用配置中的表名作为降级方案")
                listOf(
                    TableInfo(
                        schema = null,
                        tableName = TableNameNormalizer.normalizeTableName(dataExchangeJob.readerTableName) ?: dataExchangeJob.readerTableName,
                        database = sourceDatabase
                    )
                )
            }

            // 创建目标表信息
            val targetTable = TableInfo(
                schema = null,
                tableName = TableNameNormalizer.normalizeTableName(dataExchangeJob.writerTableName) ?: dataExchangeJob.writerTableName,
                database = targetDatabase
            )

            // 创建表级血缘关系
            val tableLineage = TableLineage(
                sourceTables = finalSourceTables,
                targetTable = targetTable,
                lineageType = determineLineageType(dataExchangeJob.readerSql)
            )

            // 创建列级血缘关系
            val columnLineages = dataExchangeJob.columns.map { mapping ->
                // 尝试确定源列所属的表
                val sourceTable = if (finalSourceTables.size == 1) {
                    // 只有一个源表，直接使用
                    finalSourceTables[0]
                } else {
                    // 多个源表情况下，尝试从列名前缀匹配表名
                    // 这是一个简化的匹配策略，实际应用中可能需要更复杂的逻辑
                    val columnName = mapping.srcColumnName
                    val matchedTable = finalSourceTables.find { table ->
                        columnName.startsWith("${table.tableName}.")
                    }
                    matchedTable ?: finalSourceTables[0] // 默认使用第一个表
                }

                val sourceColumn = ColumnInfo(
                    columnName = mapping.srcColumnName,
                    dataType = mapping.srcColumnType,
                    comment = mapping.columnRemark,
                    table = sourceTable
                )

                val targetColumn = ColumnInfo(
                    columnName = mapping.dstColumnName,
                    dataType = mapping.dstColumnType,
                    comment = mapping.columnRemark,
                    table = targetTable
                )

                val transformation = createTransformation(mapping)

                ColumnLineage(
                    sourceColumn = sourceColumn,
                    targetColumn = targetColumn,
                    transformation = transformation,
                    columnIndex = mapping.columnIndex
                )
            }

            // log warn if dataExchangeJob.columns is empty
            if (dataExchangeJob.columns.isEmpty()) {
                log.warn("69a4f167 | 没有列映射信息，将使用默认的列映射")
            }

            // 验证列映射的完整性
            validateColumnMappings(dataExchangeJob.columns, warnings)

            // 检查所有源表是否来自同一数据库
            val uniqueSourceDatabases = finalSourceTables.map { it.database }.toSet()
            if (uniqueSourceDatabases.size > 1) {
                warnings.add("检测到源表来自不同的数据库: ${uniqueSourceDatabases.map { it.databaseName }}")
            }

            // 创建完整的血缘信息
            val dataLineage = DataLineage(
                jobId = "${dataExchangeJob.readerJobId}-${dataExchangeJob.writeJobId}",
                jobName = "${dataExchangeJob.readerJobName} -> ${dataExchangeJob.writeJobName}",
                tableLineage = tableLineage,
                columnLineages = columnLineages,
                sourceDatabase = sourceDatabase,
                targetDatabase = targetDatabase,
                originalSql = dataExchangeJob.readerSql
            )

            return LineageResult(dataLineage, warnings, errors, true)

        } catch (e: Exception) {
            errors.add("转换过程中发生异常: ${e.message}")
            return LineageResult(null, warnings, errors, false)
        }
    }

    /**
     * 从SQL语句中解析源表信息
     */
    private fun parseSourceTablesFromSql(
        sql: String,
        sourceDatabase: DatabaseInfo,
        warnings: MutableList<String>,
    ): List<TableInfo> {
        return try {
            val parseResult = SqlParser.parse(sql)

            // 将解析出的表引用转换为TableInfo对象
            parseResult.tables.map { tableRef ->
                TableInfo(
                    schema = tableRef.schema,
                    tableName = tableRef.name,
                    database = sourceDatabase
                )
            }.also { tables ->
                if (tables.isEmpty()) {
                    warnings.add("从SQL中未能解析出任何源表")
                }
            }
        } catch (e: SqlParsingException) {
            warnings.add("SQL解析失败: ${e.message}")
            // 降级方案：使用原有的表名
            emptyList()
        }
    }

    /**
     * 根据SQL语句确定血缘类型
     */
    private fun determineLineageType(sql: String): LineageType {
        val normalizedSql = sql.trim().lowercase()

        return when {
            normalizedSql.contains("join") -> LineageType.JOIN
            normalizedSql.contains("group by") ||
                    normalizedSql.contains("count(") ||
                    normalizedSql.contains("sum(") ||
                    normalizedSql.contains("avg(") -> LineageType.AGGREGATION

            normalizedSql.contains("where") -> LineageType.FILTER
            normalizedSql.contains("case when") ||
                    normalizedSql.contains("substring") ||
                    normalizedSql.contains("concat") -> LineageType.COMPLEX_TRANSFORMATION

            normalizedSql.startsWith("select") &&
                    normalizedSql.contains("from") -> LineageType.SQL_QUERY

            else -> LineageType.DIRECT_COPY
        }
    }

    /**
     * 创建数据转换信息
     */
    private fun createTransformation(mapping: DataExchangeColumnMapping): DataTransformation? {
        // 检查是否存在类型转换
        if (mapping.srcColumnType != mapping.dstColumnType) {
            return DataTransformation(
                transformationType = TransformationType.TYPE_CAST,
                description = "类型转换: ${mapping.srcColumnType} -> ${mapping.dstColumnType}",
                expression = null
            )
        }

        // 检查是否存在列名变化
        if (mapping.srcColumnName != mapping.dstColumnName) {
            return DataTransformation(
                transformationType = TransformationType.NONE,
                description = "列名映射: ${mapping.srcColumnName} -> ${mapping.dstColumnName}",
                expression = null
            )
        }

        // 无转换
        return null
    }

    /**
     * 验证列映射的完整性并添加警告
     */
    private fun validateColumnMappings(columns: List<DataExchangeColumnMapping>, warnings: MutableList<String>) {
        // 检查列索引是否连续
        val indices = columns.map { it.columnIndex }.sorted()
        val expectedIndices = columns.indices.toList()

        if (indices != expectedIndices) {
            warnings.add("列索引不连续或不从0开始: $indices")
        }

        // 检查是否有重复的源列名
        val duplicateSourceColumns = columns.groupBy { it.srcColumnName }
            .filterValues { it.size > 1 }
            .keys

        if (duplicateSourceColumns.isNotEmpty()) {
            warnings.add("发现重复的源列名: $duplicateSourceColumns")
        }

        // 检查是否有重复的目标列名
        val duplicateTargetColumns = columns.groupBy { it.dstColumnName }
            .filterValues { it.size > 1 }
            .keys

        if (duplicateTargetColumns.isNotEmpty()) {
            warnings.add("发现重复的目标列名: $duplicateTargetColumns")
        }
    }
} 