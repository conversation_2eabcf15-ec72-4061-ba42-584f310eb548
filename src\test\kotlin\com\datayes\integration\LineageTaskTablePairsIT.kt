package com.datayes.integration

import io.restassured.RestAssured.given
import io.restassured.path.json.JsonPath
import org.assertj.core.api.Assertions.assertThat
import org.hamcrest.Matchers.*
import org.hamcrest.collection.IsCollectionWithSize.hasSize
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test

/**
 * 血缘任务表对关系 REST API 集成测试 (Lineage Task Table Pairs REST API Integration Test)
 *
 * 测试 /api/v1/lineage/tasks/table-pairs 端点的功能。
 *
 * 前提条件:
 * - 应用程序必须已经启动并运行在配置的端口上
 * - 数据库中应该存在 lineage_systems, lineage_datasources, lineage_tables, lineage_relationships 表和测试数据
 * - 测试遵循只读操作原则，不修改数据库数据
 */
@DisplayName("血缘任务表对关系 REST API 集成测试")
class LineageTaskTablePairsIT : RestApiIntegrationTestBase() {

    @Test
    @DisplayName("应该返回正确的API响应结构")
    fun `should return correct API response structure`() {
        val response = given()
            .queryParam("sourceSystemId", 1)
            .queryParam("targetSystemId", 2)
            .`when`()
            .get("/v1/lineage/tasks/table-pairs")
            .then()
            .statusCode(200)
            .contentType("application/json")
            .body("success", notNullValue())
            .body("data", notNullValue())
            .body("message", notNullValue())
            .body("timestamp", notNullValue())
            .extract()
            .response()

        println("f8e2a9c1 | 表对关系查询响应: ${response.asString()}")

        // 验证响应结构
        val jsonPath = JsonPath.from(response.asString())
        assertThat(jsonPath.getBoolean("success")).isNotNull()
        assertThat(jsonPath.get<Any>("data")).isNotNull()
        assertThat(jsonPath.getString("message")).isNotNull()
        assertThat(jsonPath.getLong("timestamp")).isGreaterThan(0)
    }

    @Test
    @DisplayName("成功案例 - 应该返回空列表当没有关系时")
    fun `should return empty list when no relationships exist`() {
        val response = given()
            .queryParam("sourceSystemId", 1)
            .queryParam("targetSystemId", 2)
            .`when`()
            .get("/v1/lineage/tasks/table-pairs")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("message", equalTo("查询成功"))
            .body("data", notNullValue())
            .body("data", notNullValue())
            .extract()
            .response()

        val jsonPath = JsonPath.from(response.asString())
        val tablePairs = jsonPath.getList<Map<String, Any>>("data")
        
        // 空列表是正常的，因为当前数据库中没有关联系统ID的关系
        assertThat(tablePairs).isNotNull()
        
        println("2c7f4a8d | 表对关系查询结果: 共${tablePairs.size}个表对关系")
    }

    @Test
    @DisplayName("应该验证TablePairDto数据结构")
    fun `should validate TablePairDto data structure when data exists`() {
        // 使用不同的系统ID组合进行测试
        val response = given()
            .queryParam("sourceSystemId", 1)
            .queryParam("targetSystemId", 3)
            .`when`()
            .get("/v1/lineage/tasks/table-pairs")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .extract()
            .response()

        val jsonPath = JsonPath.from(response.asString())
        val tablePairs = jsonPath.getList<Map<String, Any>>("data")
        
        // 如果有数据，验证数据结构
        if (tablePairs.isNotEmpty()) {
            val firstPair = tablePairs[0]
            
            // 验证必需字段存在
            val expectedFields = listOf(
                "sourceTable", "sourceSchema", "sourceDatasource", "sourceSystemId", "sourceSystemName",
                "targetTable", "targetSchema", "targetDatasource", "targetSystemId", "targetSystemName",
                "lineageType", "confidenceScore", "relationshipCount", "createdAt"
            )
            
            expectedFields.forEach { field ->
                assertThat(firstPair).containsKey(field)
            }
            
            // 验证数据类型
            assertThat(firstPair["sourceTable"]).isInstanceOf(String::class.java)
            assertThat(firstPair["targetTable"]).isInstanceOf(String::class.java)
            assertThat(firstPair["sourceDatasource"]).isInstanceOf(String::class.java)
            assertThat(firstPair["targetDatasource"]).isInstanceOf(String::class.java)
            assertThat(firstPair["lineageType"]).isInstanceOf(String::class.java)
            assertThat(firstPair["relationshipCount"]).isInstanceOf(Number::class.java)
            
            // relationshipCount 应该大于0
            val relationshipCount = (firstPair["relationshipCount"] as Number).toInt()
            assertThat(relationshipCount).isGreaterThan(0)
            
            println("9b3d8e7f | 验证表对数据结构完成: ${firstPair}")
        } else {
            println("6a4c2e9d | 当前测试数据中没有表对关系，跳过数据结构验证")
        }
    }

    @Test
    @DisplayName("错误案例 - 缺少源系统ID参数")
    fun `should return error when sourceSystemId is missing`() {
        given()
            .queryParam("targetSystemId", 2)
            .`when`()
            .get("/v1/lineage/tasks/table-pairs")
            .then()
            .statusCode(400) // 期望Bad Request
    }

    @Test
    @DisplayName("错误案例 - 缺少目标系统ID参数")
    fun `should return error when targetSystemId is missing`() {
        given()
            .queryParam("sourceSystemId", 1)
            .`when`()
            .get("/v1/lineage/tasks/table-pairs")
            .then()
            .statusCode(400) // 期望Bad Request
    }

    @Test
    @DisplayName("错误案例 - 缺少所有必需参数")
    fun `should return error when all required parameters are missing`() {
        given()
            .`when`()
            .get("/v1/lineage/tasks/table-pairs")
            .then()
            .statusCode(400) // 期望Bad Request
    }

    @Test
    @DisplayName("边界案例 - 使用无效的系统ID")
    fun `should handle invalid system IDs gracefully`() {
        val response = given()
            .queryParam("sourceSystemId", 999999)
            .queryParam("targetSystemId", 888888)
            .`when`()
            .get("/v1/lineage/tasks/table-pairs")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data", notNullValue())
            .extract()
            .response()

        val jsonPath = JsonPath.from(response.asString())
        val tablePairs = jsonPath.getList<Map<String, Any>>("data")
        
        // 无效的系统ID应该返回空列表
        assertThat(tablePairs).isEmpty()
        
        println("e7a5c4b2 | 无效系统ID测试完成: 返回空列表")
    }

    @Test
    @DisplayName("边界案例 - 使用相同的源和目标系统ID")
    fun `should handle same source and target system IDs`() {
        val response = given()
            .queryParam("sourceSystemId", 1)
            .queryParam("targetSystemId", 1)
            .`when`()
            .get("/v1/lineage/tasks/table-pairs")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data", notNullValue())
            .extract()
            .response()

        val jsonPath = JsonPath.from(response.asString())
        val tablePairs = jsonPath.getList<Map<String, Any>>("data")
        
        // 相同系统ID可能返回空列表或内部系统关系
        assertThat(tablePairs).isNotNull()
        
        println("d8f6b3a9 | 相同系统ID测试完成: 共${tablePairs.size}个表对关系")
    }

    @Test
    @DisplayName("性能测试 - 查询响应时间应该合理")
    fun `should return response within reasonable time`() {
        val startTime = System.currentTimeMillis()
        
        given()
            .queryParam("sourceSystemId", 1)
            .queryParam("targetSystemId", 2)
            .`when`()
            .get("/v1/lineage/tasks/table-pairs")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
        
        val responseTime = System.currentTimeMillis() - startTime
        
        // 响应时间应该在5秒内
        assertThat(responseTime).isLessThan(5000)
        
        println("a9e4f7c6 | 查询响应时间: ${responseTime}ms")
    }

    @Test
    @DisplayName("日志验证 - 应该包含正确的日志格式")
    fun `should contain correct log format`() {
        val response = given()
            .queryParam("sourceSystemId", 1)
            .queryParam("targetSystemId", 2)
            .`when`()
            .get("/v1/lineage/tasks/table-pairs")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .extract()
            .response()

        // 验证响应时间戳格式
        val jsonPath = JsonPath.from(response.asString())
        val timestamp = jsonPath.getLong("timestamp")
        
        // 时间戳应该是近期的（在过去1分钟内）
        val currentTime = System.currentTimeMillis()
        val timeDiff = currentTime - timestamp
        assertThat(timeDiff).isLessThan(60000) // 1分钟内
        
        println("5f2a8d1c | 时间戳验证完成: timestamp=$timestamp, diff=${timeDiff}ms")
    }

    @Test
    @DisplayName("多种系统ID组合测试")
    fun `should test multiple system ID combinations`() {
        val systemIdCombinations = listOf(
            Pair(1, 2),
            Pair(1, 3),
            Pair(1, 4),
            Pair(2, 3),
            Pair(2, 4),
            Pair(3, 4)
        )
        
        var totalRelationships = 0
        
        systemIdCombinations.forEach { (sourceId, targetId) ->
            val response = given()
                .queryParam("sourceSystemId", sourceId)
                .queryParam("targetSystemId", targetId)
                .`when`()
                .get("/v1/lineage/tasks/table-pairs")
                .then()
                .statusCode(200)
                .body("success", equalTo(true))
                .extract()
                .response()

            val jsonPath = JsonPath.from(response.asString())
            val tablePairs = jsonPath.getList<Map<String, Any>>("data")
            totalRelationships += tablePairs.size
            
            println("c4e9a7f3 | 系统组合 $sourceId->$targetId: ${tablePairs.size}个表对关系")
        }
        
        println("b7f5d2e8 | 多系统组合测试完成: 总计${totalRelationships}个表对关系")
    }
}