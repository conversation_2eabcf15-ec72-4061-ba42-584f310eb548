# Development Guidelines for DGP Lineage Collector

This document provides essential information for developers working on the DGP Lineage Collector project.

## Build/Configuration Instructions

### Prerequisites
- Java 17
- Maven 3.6+
- Kotlin 1.9.25

### Building the Project
1. Clone the repository
2. Build the project using Maven:
   ```
   mvn clean install
   ```

### Configuration
The application uses Spring Boot configuration properties. Key configuration files:

- `src/main/resources/application.properties` - Main configuration file

#### Database Configuration
The application connects to a MySQL database for data exchange operations. Configure the database connection in `application.properties`:

```properties
# Data Exchange Platform DataSource Configuration
dataexchange.datasource.url               = ******************************************************************************************************************************
dataexchange.datasource.username          = your-username
dataexchange.datasource.password          = your-password
dataexchange.datasource.driver-class-name = com.mysql.cj.jdbc.Driver
```

## Testing Information

### Running Tests
Run all tests using Maven:
```
mvn test
```

Run specific test classes:
```
mvn test -Dtest=com.datayes.util.StringUtilsTest
```

Run specific test methods:
```
mvn test -Dtest=com.datayes.util.StringUtilsTest#testReverse
```

### Adding New Tests
1. Create test classes in the `src/test/kotlin` directory following the same package structure as the main code
2. Use JUnit 5 for testing (the project uses JUnit Jupiter)
3. Use AssertJ for assertions (`assertThat()` style)
4. Follow the Given-When-Then pattern for test structure

### Test Example
Here's a simple test example:

```kotlin
package com.datayes.util

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test

class StringUtilsTest {

    @Test
    @DisplayName("Should reverse a string correctly")
    fun testReverse() {
        // Given
        val input = "Hello, World!"
        
        // When
        val result = StringUtils.reverse(input)
        
        // Then
        assertThat(result).isEqualTo("!dlroW ,olleH")
    }
}
```

### Spring Boot Tests
For tests that require the Spring context:

1. Use the `@SpringBootTest` annotation
2. Autowire required dependencies

Example:
```kotlin
@SpringBootTest
class DataExchangeServiceTest {

    @Autowired
    private lateinit var dataExchangeService: DataExchangeService
    
    @Test
    fun testServiceOperation() {
        // Test implementation
    }
}
```

## Additional Development Information

### Code Style
- Follow Kotlin coding conventions
- Use meaningful names for classes, methods, and variables
- Add KDoc comments for public classes and methods
- Use nullable types (`String?`) only when necessary

### Project Structure
- Main source code: `src/main/kotlin/com/datayes`
- Test code: `src/test/kotlin/com/datayes`
- Resources: `src/main/resources`

### Key Components
- `com.datayes.parser` - SQL parsing and lineage extraction
- `com.datayes.task` - Data exchange platform integration

### Debugging
- The application uses standard Spring Boot logging
- Configure logging using `src/main/resources/logback-spring.xml`:
  ```xml
  <configuration>
    <springProfile name="default">
      <logger name="com.datayes" level="DEBUG"/>
    </springProfile>
  </configuration>
  ```

### Common Issues
- Database connection issues: Verify database credentials and network connectivity
- SQL parsing errors: Check the SQL syntax in the input queries