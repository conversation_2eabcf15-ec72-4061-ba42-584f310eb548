package com.datayes.task

import com.datayes.dataexchange.DataExchangeJobService
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import java.util.*

/**
 * 手动触发血缘任务服务 (Manual Lineage Trigger Task Service)
 *
 * 实现手动触发血缘任务的核心业务逻辑，包括任务创建、执行和查询功能
 * 遵循函数式核心，命令式外壳的架构原则
 */
@Service
@Transactional
class ManualLineageTriggerTaskService(
    private val manualTaskRepository: ManualLineageTriggerTaskRepository,
    private val manualTaskCustomRepository: ManualLineageTriggerTaskCustomRepository,
    private val dataExchangeJobService: DataExchangeJobService
) {

    private val logger = LoggerFactory.getLogger(ManualLineageTriggerTaskService::class.java)

    /**
     * 手动触发血缘任务 (Trigger manual lineage task)
     */
    fun triggerManualLineageTask(request: ManualLineageTriggerRequest): ManualTaskSummary {
        val taskUuid = UUID.randomUUID().toString()

        logger.info("7a8b9c | 创建手动触发血缘任务，数据源ID: ${request.datasourceId}, 任务UUID: $taskUuid")

        // 1. 验证数据源是否存在
        validateDatasourceExists(request.datasourceId)

        // 2. 创建任务记录
        val task = ManualLineageTriggerTask(
            taskUuid = taskUuid,
            datasourceId = request.datasourceId,
            triggerUser = request.triggerUser,
            taskStatus = ManualTaskStatus.PENDING
        )

        val savedTask = manualTaskRepository.save(task)
        logger.info("d4e5f6 | 成功创建手动触发任务，任务ID: ${savedTask.id}")

        // 3. 同步执行血缘任务
        executeLineageTask(taskUuid)

        // 4. 获取执行完成后的任务信息
        val completedTask = manualTaskRepository.findByTaskUuid(taskUuid)
            ?: throw IllegalStateException("任务执行后未找到: $taskUuid")

        // 5. 获取数据源名称（简化实现）
        val datasourceName = getDatasourceName(request.datasourceId)

        return ManualTaskSummary(
            id = completedTask.id,
            taskUuid = completedTask.taskUuid,
            datasourceId = completedTask.datasourceId,
            datasourceName = datasourceName,
            triggerUser = completedTask.triggerUser,
            taskStatus = completedTask.taskStatus,
            successCount = completedTask.successCount,
            failureCount = completedTask.failureCount,
            totalCount = completedTask.totalCount,
            executionTimeMs = completedTask.executionTimeMs,
            createdAt = completedTask.createdAt,
            completedAt = completedTask.completedAt
        )
    }

    /**
     * 执行血缘任务 (Execute lineage task)
     */
    private fun executeLineageTask(taskUuid: String) {
        val startTime = System.currentTimeMillis()

        try {
            logger.info("123abc | 开始执行手动血缘任务，UUID: $taskUuid")

            // 1. 更新任务状态为运行中
            manualTaskCustomRepository.updateTaskStatus(
                taskUuid = taskUuid,
                status = ManualTaskStatus.RUNNING,
                startedAt = LocalDateTime.now()
            )

            // 2. 获取任务信息
            val task = manualTaskRepository.findByTaskUuid(taskUuid)
                ?: throw IllegalStateException("任务不存在: $taskUuid")

            // 3. 获取该数据源相关的所有作业
            val dataExchangeJobs = dataExchangeJobService.findDataExchangeJobsByDatasourceId(task.datasourceId)
            logger.info("456def | 找到 ${dataExchangeJobs.size} 个相关的数据交换作业")

            val hdfsShellScriptJobs = findHdfsShellScriptJobsByDatasourceId(task.datasourceId)
            logger.info("789abc | 找到 ${hdfsShellScriptJobs.size} 个相关的HDFS Shell脚本作业")

            val successResults = mutableListOf<TaskResultItem>()
            val failureResults = mutableListOf<TaskResultItem>()
            var successCount = 0
            var failureCount = 0

            // 4. 处理数据交换作业的血缘
            for (job in dataExchangeJobs) {
                try {
                    val jobKey = "${job.readerJobId}_${job.writeJobId}"
                    logger.info("789ghi | 处理作业: $jobKey")

                    // 执行血缘收集
                    val result = dataExchangeJobService.processJobLineageWithChangeDetection(job)

                    successResults.add(
                        TaskResultItem(
                            itemType = "DATA_EXCHANGE_JOB",
                            itemId = jobKey,
                            itemName = job.readerJobName,
                            message = "血缘处理成功: ${result.processingResult}"
                        )
                    )
                    successCount++

                    logger.info("abc123 | 作业 $jobKey 处理成功")

                } catch (e: Exception) {
                    logger.error("def456 | 作业处理失败: ${job.readerJobId}_${job.writeJobId}", e)

                    failureResults.add(
                        TaskResultItem(
                            itemType = "DATA_EXCHANGE_JOB",
                            itemId = "${job.readerJobId}_${job.writeJobId}",
                            itemName = job.readerJobName,
                            message = "血缘处理失败: ${e.message}"
                        )
                    )
                    failureCount++
                }
            }

            // 5. 处理HDFS Shell脚本作业的血缘
            for (scriptJob in hdfsShellScriptJobs) {
                try {
                    logger.info("def012 | 处理HDFS Shell脚本作业: ${scriptJob.jobId}")

                    // 执行HDFS Shell脚本血缘收集（占位符实现）
                    val result = processHdfsShellScriptLineage(scriptJob)

                    successResults.add(
                        TaskResultItem(
                            itemType = "HDFS_SHELL_SCRIPT_JOB",
                            itemId = scriptJob.jobId,
                            itemName = scriptJob.jobName,
                            message = "HDFS Shell脚本血缘处理成功: ${result.processingResult}"
                        )
                    )
                    successCount++

                    logger.info("ghi345 | HDFS Shell脚本作业 ${scriptJob.jobId} 处理成功")

                } catch (e: Exception) {
                    logger.error("jkl678 | HDFS Shell脚本作业处理失败: ${scriptJob.jobId}", e)

                    failureResults.add(
                        TaskResultItem(
                            itemType = "HDFS_SHELL_SCRIPT_JOB",
                            itemId = scriptJob.jobId,
                            itemName = scriptJob.jobName,
                            message = "HDFS Shell脚本血缘处理失败: ${e.message}"
                        )
                    )
                    failureCount++
                }
            }

            val totalCount = successCount + failureCount
            val executionTime = System.currentTimeMillis() - startTime

            // 6. 更新任务结果
            manualTaskCustomRepository.updateTaskResults(
                taskUuid = taskUuid,
                successCount = successCount,
                failureCount = failureCount,
                totalCount = totalCount,
                successResults = successResults,
                failureResults = failureResults
            )

            // 7. 更新任务状态为完成
            val finalStatus = if (failureCount == 0) ManualTaskStatus.SUCCESS else ManualTaskStatus.SUCCESS
            manualTaskCustomRepository.updateTaskStatus(
                taskUuid = taskUuid,
                status = finalStatus,
                completedAt = LocalDateTime.now(),
                executionTimeMs = executionTime
            )

            logger.info("ghi789 | 手动血缘任务执行完成，UUID: $taskUuid, 成功: $successCount, 失败: $failureCount")

        } catch (e: Exception) {
            logger.error("jkl012 | 手动血缘任务执行失败，UUID: $taskUuid", e)

            // 更新任务状态为失败
            manualTaskCustomRepository.updateTaskStatus(
                taskUuid = taskUuid,
                status = ManualTaskStatus.FAILED,
                completedAt = LocalDateTime.now(),
                executionTimeMs = System.currentTimeMillis() - startTime,
                errorMessage = e.message
            )
        }
    }

    /**
     * 查询手动任务列表 (Query manual task list)
     */
    @Transactional(readOnly = true)
    fun queryManualTasks(criteria: ManualTaskQueryCriteria, pageable: Pageable): ManualTaskListResponse {
        logger.info("mno345 | 查询手动任务列表，条件: $criteria")

        val page = manualTaskCustomRepository.findTasksWithCriteria(criteria, pageable)

        val summaries = page.content.map { detail ->
            ManualTaskSummary(
                id = detail.id,
                taskUuid = detail.taskUuid,
                datasourceId = detail.datasourceId,
                datasourceName = detail.datasourceName,
                triggerUser = detail.triggerUser,
                taskStatus = detail.taskStatus,
                successCount = detail.successCount,
                failureCount = detail.failureCount,
                totalCount = detail.totalCount,
                executionTimeMs = detail.executionTimeMs,
                createdAt = detail.createdAt,
                completedAt = detail.completedAt
            )
        }

        return ManualTaskListResponse(
            tasks = summaries,
            totalElements = page.totalElements,
            totalPages = page.totalPages,
            currentPage = page.number,
            size = page.size
        )
    }

    /**
     * 根据UUID获取任务详情 (Get task detail by UUID)
     */
    @Transactional(readOnly = true)
    fun getTaskDetail(taskUuid: String): ManualTaskDetailResponse? {
        logger.info("pqr678 | 获取任务详情，UUID: $taskUuid")
        return manualTaskCustomRepository.findTaskDetailByUuid(taskUuid)
    }

    /**
     * 取消手动任务 (Cancel manual task)
     */
    fun cancelManualTask(taskUuid: String): Boolean {
        logger.info("stu901 | 取消手动任务，UUID: $taskUuid")

        val task = manualTaskRepository.findByTaskUuid(taskUuid)
            ?: throw IllegalArgumentException("任务不存在: $taskUuid")

        // 只有PENDING或RUNNING状态的任务可以取消
        if (task.taskStatus != ManualTaskStatus.PENDING && task.taskStatus != ManualTaskStatus.RUNNING) {
            throw IllegalStateException("任务状态不允许取消: ${task.taskStatus}")
        }

        val updateCount = manualTaskCustomRepository.updateTaskStatus(
            taskUuid = taskUuid,
            status = ManualTaskStatus.CANCELLED,
            completedAt = LocalDateTime.now(),
            errorMessage = "任务已被手动取消"
        )

        return updateCount > 0
    }

    /**
     * 验证数据源是否存在 (Validate datasource exists)
     */
    private fun validateDatasourceExists(datasourceId: Long) {
        // 这里应该调用数据源服务验证数据源是否存在
        // 简化实现，假设数据源存在
        logger.debug("vwx234 | 验证数据源是否存在，ID: $datasourceId")

        // 通过查询相关作业来验证数据源的有效性
        val jobs = dataExchangeJobService.findDataExchangeJobsByDatasourceId(datasourceId)
        if (jobs.isEmpty()) {
            logger.warn("yzab567 | 数据源ID $datasourceId 没有找到相关的数据交换作业")
        }
    }

    /**
     * 获取数据源相关统计信息 (Get datasource related statistics)
     */
    @Transactional(readOnly = true)
    fun getDatasourceTaskStatistics(datasourceId: Long): DatasourceTaskStatistics {
        logger.info("cdef890 | 获取数据源任务统计，ID: $datasourceId")

        val tasks = manualTaskRepository.findByDatasourceId(datasourceId)

        val totalTasks = tasks.size
        val successfulTasks = tasks.count { it.taskStatus == ManualTaskStatus.SUCCESS }
        val failedTasks = tasks.count { it.taskStatus == ManualTaskStatus.FAILED }
        val runningTasks = tasks.count { it.taskStatus == ManualTaskStatus.RUNNING }
        val pendingTasks = tasks.count { it.taskStatus == ManualTaskStatus.PENDING }

        val lastExecutedTask = tasks
            .filter { it.completedAt != null }
            .maxByOrNull { it.completedAt!! }

        return DatasourceTaskStatistics(
            datasourceId = datasourceId,
            totalTasks = totalTasks,
            successfulTasks = successfulTasks,
            failedTasks = failedTasks,
            runningTasks = runningTasks,
            pendingTasks = pendingTasks,
            lastExecutedAt = lastExecutedTask?.completedAt,
            averageExecutionTimeMs = tasks
                .mapNotNull { it.executionTimeMs }
                .takeIf { it.isNotEmpty() }
                ?.average()?.toLong()
        )
    }

    /**
     * 根据数据源ID查找HDFS Shell脚本作业 (Find HDFS Shell Script Jobs by datasource ID)
     *
     * TODO: 占位符实现 - 当HDFS路径与表血缘关联功能准备好后需要实现
     * 目前返回空列表，因为HDFS路径到表血缘的映射尚未准备好
     */
    private fun findHdfsShellScriptJobsByDatasourceId(datasourceId: Long): List<HdfsShellScriptJob> {
        logger.info("rst901 | 查找数据源相关的HDFS Shell脚本作业，datasourceId: $datasourceId")
        logger.info("uvw234 | 注意：HDFS Shell脚本与数据源关联功能尚未实现，返回空列表")

        // TODO: 实现以下逻辑：
        // 1. 根据数据源ID查找相关的HDFS路径
        // 2. 在这些路径下查找shell脚本
        // 3. 解析脚本中的SQL语句
        // 4. 将SQL语句中的表与数据源进行匹配
        // 5. 返回匹配的HDFS Shell脚本作业列表

        return emptyList()
    }

    /**
     * 处理HDFS Shell脚本血缘 (Process HDFS Shell Script Lineage)
     *
     * TODO: 占位符实现 - 当HDFS路径与表血缘关联功能准备好后需要实现
     */
    private fun processHdfsShellScriptLineage(scriptJob: HdfsShellScriptJob): HdfsShellScriptLineageResult {
        logger.info("xyz567 | 处理HDFS Shell脚本血缘，jobId: ${scriptJob.jobId}")
        logger.info("abc890 | 注意：HDFS Shell脚本血缘处理功能尚未实现，返回占位符结果")

        // TODO: 实现以下逻辑：
        // 1. 解析shell脚本文件
        // 2. 提取其中的SQL语句
        // 3. 分析SQL语句的表依赖关系
        // 4. 将HDFS路径与表血缘进行关联
        // 5. 生成血缘关系并保存到数据库
        // 6. 返回处理结果

        return HdfsShellScriptLineageResult(
            processingResult = "PLACEHOLDER",
            message = "HDFS Shell脚本血缘处理占位符实现",
            processedAt = java.time.LocalDateTime.now()
        )
    }

    /**
     * 获取数据源名称 (Get datasource name)
     */
    private fun getDatasourceName(datasourceId: Long): String? {
        return try {
            // 简化实现，返回默认格式
            "Datasource-$datasourceId"
        } catch (e: Exception) {
            logger.warn("def123 | 获取数据源名称失败，ID: $datasourceId", e)
            null
        }
    }
}

/**
 * HDFS Shell脚本作业 (HDFS Shell Script Job)
 *
 * TODO: 占位符数据类 - 当HDFS功能准备好后需要完善
 */
data class HdfsShellScriptJob(
    val jobId: String,
    val jobName: String,
    val hdfsPath: String,
    val scriptFileName: String,
    val datasourceId: Long? = null  // 当关联功能实现后使用
)

/**
 * HDFS Shell脚本血缘处理结果 (HDFS Shell Script Lineage Result)
 *
 * TODO: 占位符数据类 - 当HDFS功能准备好后需要完善
 */
data class HdfsShellScriptLineageResult(
    val processingResult: String,
    val message: String,
    val processedAt: java.time.LocalDateTime,
    val lineageCount: Int = 0,
    val errorDetails: String? = null
)

/**
 * 数据源任务统计信息 (Datasource Task Statistics)
 */
data class DatasourceTaskStatistics(
    val datasourceId: Long,
    val totalTasks: Int,
    val successfulTasks: Int,
    val failedTasks: Int,
    val runningTasks: Int,
    val pendingTasks: Int,
    val lastExecutedAt: LocalDateTime?,
    val averageExecutionTimeMs: Long?
)