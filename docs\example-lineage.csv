job_id,job_name,source_database,target_database,source_table,target_table,source_column,target_column,source_column_type,target_column_type,sql
example_001,Users to Customers,source_db,target_db,users,customers,id,customer_id,BIGINT,BIGINT,"INSERT INTO customers (customer_id, name) SELECT id, name FROM users"
example_002,Orders to Sales,source_db,target_db,orders,sales,order_id,sale_id,BIGINT,BIGINT,"INSERT INTO sales (sale_id, amount) SELECT order_id, total_amount FROM orders"
example_003,Products Import,external_db,main_db,products,inventory,product_code,item_code,VARCHAR,VARCHAR,"INSERT INTO inventory (item_code, description) SELECT product_code, product_name FROM products"