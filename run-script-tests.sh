#!/bin/bash

# 脚本影响分析控制器测试运行脚本 (Script Impact Analysis Controller Test Runner)
# 
# 用法 (Usage):
#   ./run-script-tests.sh [test-type] [options]
#
# 测试类型 (Test Types):
#   simple    - 运行简化版端到端测试 (Run simplified end-to-end tests)
#   full      - 运行完整的端到端测试 (Run complete end-to-end tests)  
#   all       - 运行所有集成测试 (Run all integration tests)
#
# 选项 (Options):
#   --host HOST    - 设置测试目标主机 (Set test target host)
#   --port PORT    - 设置测试目标端口 (Set test target port)
#   --verbose      - 显示详细输出 (Show verbose output)

set -e  # 遇到错误立即退出 (Exit on error)

# 默认配置 (Default Configuration)
DEFAULT_HOST="localhost"
DEFAULT_PORT="9503"
TEST_TYPE="simple"
VERBOSE=false
HOST=""
PORT=""

# 颜色定义 (Color definitions)
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息 (Print colored messages)
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息 (Show help information)
show_help() {
    echo "脚本影响分析控制器测试运行脚本 (Script Impact Analysis Controller Test Runner)"
    echo ""
    echo "用法 (Usage):"
    echo "  $0 [test-type] [options]"
    echo ""
    echo "测试类型 (Test Types):"
    echo "  simple    运行简化版端到端测试 (默认) [Run simplified end-to-end tests (default)]"
    echo "  full      运行完整的端到端测试 [Run complete end-to-end tests]"
    echo "  all       运行所有集成测试 [Run all integration tests]"
    echo ""
    echo "选项 (Options):"
    echo "  --host HOST     设置测试目标主机 (默认: $DEFAULT_HOST) [Set test target host]"
    echo "  --port PORT     设置测试目标端口 (默认: $DEFAULT_PORT) [Set test target port]"
    echo "  --verbose       显示详细输出 [Show verbose output]"
    echo "  --help         显示此帮助信息 [Show this help message]"
    echo ""
    echo "示例 (Examples):"
    echo "  $0 simple                                     # 运行简化测试"
    echo "  $0 full --host localhost --port 8080          # 运行完整测试，指定主机和端口"
    echo "  $0 all --verbose                              # 运行所有测试，显示详细输出"
}

# 解析命令行参数 (Parse command line arguments)
while [[ $# -gt 0 ]]; do
    case $1 in
        simple|full|all)
            TEST_TYPE="$1"
            shift
            ;;
        --host)
            HOST="$2"
            shift 2
            ;;
        --port)
            PORT="$2"
            shift 2
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        *)
            print_error "未知参数 (Unknown parameter): $1"
            show_help
            exit 1
            ;;
    esac
done

# 设置默认值 (Set default values)
if [[ -z "$HOST" ]]; then
    HOST="$DEFAULT_HOST"
fi

if [[ -z "$PORT" ]]; then
    PORT="$DEFAULT_PORT"
fi

# 检查必要的工具 (Check required tools)
print_info "检查环境依赖 (Checking environment dependencies)..."

if ! command -v ./mvnw &> /dev/null; then
    print_error "Maven wrapper (./mvnw) 未找到 (not found)"
    exit 1
fi

if ! command -v java &> /dev/null; then
    print_error "Java 未安装或未在PATH中 (not installed or not in PATH)"
    exit 1
fi

print_success "环境检查通过 (Environment check passed)"

# 检查应用程序是否运行 (Check if application is running)
print_info "检查应用程序状态 (Checking application status)..."

if curl -f -s "http://$HOST:$PORT/api/actuator/health" > /dev/null 2>&1; then
    print_success "应用程序正在运行 (Application is running) - http://$HOST:$PORT"
else
    print_warning "无法连接到应用程序 (Cannot connect to application) - http://$HOST:$PORT"
    print_warning "请确保应用程序正在运行 (Please ensure the application is running)"
    print_info "启动应用程序 (Start application): ./mvnw spring-boot:run"
    echo ""
    read -p "是否继续运行测试? (Continue running tests? y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "测试已取消 (Test cancelled)"
        exit 0
    fi
fi

# 构建Maven命令 (Build Maven command)
MAVEN_CMD="./mvnw test"

# 设置系统属性 (Set system properties)
SYSTEM_PROPS="-Dtest.api.host=$HOST -Dtest.api.port=$PORT"

# 根据测试类型设置测试类 (Set test class based on test type)
case $TEST_TYPE in
    simple)
        TEST_CLASS="ScriptImpactAnalysisControllerSimpleIT"
        print_info "运行简化版端到端测试 (Running simplified end-to-end tests)..."
        ;;
    full)
        TEST_CLASS="ScriptImpactAnalysisControllerIT"
        print_info "运行完整的端到端测试 (Running complete end-to-end tests)..."
        ;;
    all)
        TEST_CLASS="com.datayes.integration.**IT"
        print_info "运行所有集成测试 (Running all integration tests)..."
        ;;
esac

# 添加详细输出选项 (Add verbose output option)
if [[ "$VERBOSE" == "true" ]]; then
    MAVEN_OPTS="-X"
else
    MAVEN_OPTS=""
fi

# 显示测试配置 (Show test configuration)
print_info "测试配置 (Test Configuration):"
echo "  测试类型 (Test Type): $TEST_TYPE"
echo "  目标主机 (Target Host): $HOST"
echo "  目标端口 (Target Port): $PORT"
echo "  测试类 (Test Class): $TEST_CLASS"
echo "  详细输出 (Verbose): $VERBOSE"
echo ""

# 运行测试 (Run tests)
print_info "开始运行测试 (Starting tests)..."
echo "命令 (Command): $MAVEN_CMD -Dtest=$TEST_CLASS $SYSTEM_PROPS $MAVEN_OPTS"
echo ""

# 记录开始时间 (Record start time)
START_TIME=$(date +%s)

# 执行测试 (Execute tests)
if $MAVEN_CMD -Dtest="$TEST_CLASS" $SYSTEM_PROPS $MAVEN_OPTS; then
    # 计算执行时间 (Calculate execution time)
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    
    print_success "测试执行成功! (Tests executed successfully!)"
    print_info "执行时间 (Execution time): ${DURATION}s"
    
    # 显示测试报告位置 (Show test report location)
    print_info "测试报告位置 (Test reports location): target/surefire-reports/"
    
    exit 0
else
    # 计算执行时间 (Calculate execution time)
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    
    print_error "测试执行失败! (Tests execution failed!)"
    print_info "执行时间 (Execution time): ${DURATION}s"
    print_info "查看详细日志 (Check detailed logs): target/surefire-reports/"
    
    exit 1
fi 