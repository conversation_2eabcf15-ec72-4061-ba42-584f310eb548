package com.datayes.git

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import java.nio.file.Files
import java.nio.file.Path
import java.time.LocalDateTime

/**
 * GitUtils 的单元测试
 */
class GitUtilsTest {

    // 使用公开的 Git 仓库进行测试
    private val testRepoUrl = "http://git.datayes.com/datamgt/datamgt-watcher"

    // 如果需要私有仓库测试，可以设置用户名和密码
    private val username: String? = "jie.wu"
    private val password: String? = System.getenv("password")

    @Test
    @Disabled
    fun `test withClonedRepo creates temp directory and returns result with lastUpdateTime`() {
        // 准备一个简单的操作函数，检查目录是否存在并返回一个标记值
        val operation: (Path) -> String = { path ->
            assertTrue(Files.exists(path), "临时目录应该存在")
            assertTrue(Files.isDirectory(path), "路径应该是一个目录")
            "操作成功"
        }

        // 调用 withClonedRepo
        val result = GitUtils.withClonedRepo(testRepoUrl, username, password, operation)

        // 验证结果
        assertNotNull(result)
        assertEquals("操作成功", result.result)
        assertNotNull(result.lastUpdateTime)
        assertTrue(result.lastUpdateTime.isBefore(LocalDateTime.now()))
    }

    @Test
    @Disabled
    fun `test getLastUpdateTime returns valid time`() {
        // 获取最后更新时间
        val lastUpdateTime = GitUtils.getLastUpdateTime(testRepoUrl, username, password)

        // 验证结果
        assertNotNull(lastUpdateTime)
        assertTrue(lastUpdateTime.isBefore(LocalDateTime.now()))
    }

    @Test
    @Disabled("windows has problem with deleting temp directory")
    fun `test temp directory is deleted after operation`() {
        var tempDirPath: Path? = null

        // 执行操作，记录临时目录路径
        GitUtils.withClonedRepo(testRepoUrl, username, password) { path ->
            tempDirPath = path
            assertTrue(Files.exists(path), "临时目录应该存在")
            "操作完成"
        }

        // 验证临时目录已被删除
        assertNotNull(tempDirPath)
        assertFalse(Files.exists(tempDirPath!!), "操作完成后临时目录应该被删除")
    }
}
