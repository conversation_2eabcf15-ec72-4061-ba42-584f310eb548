package com.datayes.hdfs

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.DisplayName

class HdfsShellScriptLineageConverterTest {

    @Test
    @DisplayName("Should handle both SELECT and INSERT statements")
    fun testMixedSqlStatements() {
        // Given - script with both SELECT and INSERT statements
        val job = HdfsShellScriptJob(
            jobId = "test_mixed_sql",
            jobName = "Mixed SQL Test",
            zipFilePath = "/test/path/mixed_sql_test.zip",
            scriptName = "mixed_sql_test.sql",
            scriptContent = """
                #!/bin/bash
                
                # Pure SELECT query
                hive -e "SELECT customer_id, order_date FROM orders WHERE order_date > '2024-01-01'"
                
                # INSERT OVERWRITE statement
                hive -e "INSERT OVERWRITE TABLE target_table 
                         SELECT customer_id, SUM(amount) as total_amount 
                         FROM orders 
                         WHERE order_date > '2024-01-01' 
                         GROUP BY customer_id"
            """.trimIndent(),
            scriptSizeBytes = 500
        )

        // When
        val result = HdfsShellScriptLineageConverter.convertToLineage(job)

        // Then
        assertThat(result).isNotNull
        assertThat(result.success).isTrue()
        
        // Should have found source tables from both SELECT and INSERT statements
        val lineage = result.lineage
        assertThat(lineage).isNotNull
        assertThat(lineage!!.tableLineage.sourceTables).isNotEmpty()
        
        // Verify that both SQL types were processed
        // The INSERT statement should contribute both source tables (orders) and target table (target_table)
        val tableNames = lineage.tableLineage.sourceTables.map { it.tableName }
        assertThat(tableNames).contains("orders")
        
        // Verify column lineage uses aliases properly
        assertThat(lineage.columnLineages).isNotEmpty()
        val targetColumnNames = lineage.columnLineages.map { it.targetColumn.columnName }
        assertThat(targetColumnNames).contains("total_amount")
        assertThat(targetColumnNames).doesNotContain("SUM(amount)")
        
        println("=== Test Results ===")
        println("Success: ${result.success}")
        println("Warnings: ${result.warnings.size}")
        println("Errors: ${result.errors.size}")
        println("Source tables: ${lineage.tableLineage.sourceTables.map { it.tableName }}")
        println("Target table: ${lineage.tableLineage.targetTable?.tableName}")
        println("Lineage type: ${lineage.tableLineage.lineageType}")
        println("Target columns: $targetColumnNames")
        
        result.warnings.forEach { println("Warning: $it") }
        result.errors.forEach { println("Error: $it") }
    }

    @Test
    @DisplayName("Should extract lineage only from INSERT statements, ignoring pure SELECT queries")
    fun testLineageExtractionFromMixedStatements() {
        // Given - script with pure SELECT and INSERT statements
        val job = HdfsShellScriptJob(
            jobId = "test_lineage_extraction",
            jobName = "Lineage Extraction Test",
            zipFilePath = "/test/path/lineage_test.zip",
            scriptName = "lineage_test.sql",
            scriptContent = """
                #!/bin/bash
                
                # Pure SELECT query - should NOT affect lineage target
                hive -e "SELECT customer_id, order_date FROM orders WHERE order_date > '2024-01-01'"
                
                # INSERT OVERWRITE statement - should define the lineage relationship
                hive -e "INSERT OVERWRITE TABLE target_table 
                         SELECT customer_id, SUM(amount) as total_amount 
                         FROM orders 
                         WHERE order_date > '2024-01-01' 
                         GROUP BY customer_id"
            """.trimIndent(),
            scriptSizeBytes = 600
        )

        // When
        val result = HdfsShellScriptLineageConverter.convertToLineage(job)

        // Then
        assertThat(result).isNotNull
        assertThat(result.success).isTrue()
        
        val lineage = result.lineage
        assertThat(lineage).isNotNull
        
        // Verify source tables from both statements are captured
        val sourceTableNames = lineage!!.tableLineage.sourceTables.map { it.tableName }
        assertThat(sourceTableNames).contains("orders")
        
        // Verify target table is identified from INSERT statement only
        // Note: Current implementation may create a virtual target table based on job name
        // TODO: Enhance target table detection to properly identify target_table from INSERT
        val targetTableName = lineage.tableLineage.targetTable?.tableName
        println("Target table detected: $targetTableName")
        
        // Verify lineage type reflects the INSERT operation characteristics
        val lineageType = lineage.tableLineage.lineageType
        assertThat(lineageType).isIn(
            com.datayes.lineage.LineageType.AGGREGATION,  // Due to SUM and GROUP BY
            com.datayes.lineage.LineageType.DATA_MOVEMENT  // Due to INSERT
        )
        
        println("=== Lineage Extraction Test Results ===")
        println("Success: ${result.success}")
        println("Source tables: $sourceTableNames")
        println("Target table: $targetTableName")
        println("Lineage type: $lineageType")
        println("Column lineages count: ${lineage.columnLineages.size}")
        
        // Verify column mapping expectations for INSERT without explicit column list
        // When INSERT doesn't specify columns, target columns should match source column order
        if (lineage.columnLineages.isNotEmpty()) {
            println("Column mappings:")
            lineage.columnLineages.forEach { columnLineage ->
                println("  ${columnLineage.sourceColumn.columnName} -> ${columnLineage.targetColumn.columnName}")
            }
        }
        
        result.warnings.forEach { println("Warning: $it") }
        result.errors.forEach { println("Error: $it") }
    }

    @Test
    @DisplayName("Should handle pure SELECT statements as before")
    fun testPureSelectStatements() {
        // Given - script with only SELECT statements
        val job = HdfsShellScriptJob(
            jobId = "test_select_only",
            jobName = "Select Only Test",
            zipFilePath = "/test/path/select_only_test.zip",
            scriptName = "select_only_test.sql", 
            scriptContent = """
                #!/bin/bash
                
                # SELECT with JOIN
                hive -e "SELECT o.customer_id, c.customer_name, o.order_date 
                         FROM orders o 
                         JOIN customers c ON o.customer_id = c.customer_id 
                         WHERE o.order_date > '2024-01-01'"
            """.trimIndent(),
            scriptSizeBytes = 300
        )

        // When
        val result = HdfsShellScriptLineageConverter.convertToLineage(job)

        // Then
        assertThat(result).isNotNull
        assertThat(result.success).isTrue()
        
        val lineage = result.lineage
        assertThat(lineage).isNotNull
        
        // Should find both tables from the JOIN
        val tableNames = lineage!!.tableLineage.sourceTables.map { it.tableName }
        assertThat(tableNames).containsExactlyInAnyOrder("orders", "customers")
        
        println("=== Pure SELECT Test Results ===")
        println("Success: ${result.success}")
        println("Source tables: ${lineage.tableLineage.sourceTables.map { it.tableName }}")
        println("Lineage type: ${lineage.tableLineage.lineageType}")
    }
}