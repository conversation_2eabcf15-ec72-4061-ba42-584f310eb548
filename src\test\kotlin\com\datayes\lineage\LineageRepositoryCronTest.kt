package com.datayes.lineage

import com.datayes.scheduler.SystemScheduleInfo
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.*
import org.springframework.dao.EmptyResultDataAccessException
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.jdbc.core.RowMapper
import java.sql.ResultSet

/**
 * LineageRepository Cron 方法单元测试
 */
@DisplayName("LineageRepository Cron 方法测试")
class LineageRepositoryCronTest {

    @Mock
    private lateinit var jdbcTemplate: JdbcTemplate

    @Mock
    private lateinit var resultSet: ResultSet

    private lateinit var lineageRepository: LineageRepository

    @BeforeEach
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        lineageRepository = LineageRepository(jdbcTemplate)
    }

    @Test
    @DisplayName("应该成功查询具有cron表达式的活跃系统")
    fun `should successfully find active systems with cron expression`() {
        // Given
        val expectedSql = """
            SELECT id, system_name, system_code, status, cron_expression, description
            FROM lineage_systems 
            WHERE status = 'ACTIVE' 
              AND cron_expression IS NOT NULL 
              AND cron_expression != ''
            ORDER BY id
        """.trimIndent()

        val mockSystems = listOf(
            SystemScheduleInfo(
                id = 1L,
                systemName = "数据交互平台",
                systemCode = "DATA_EXCHANGE_PLATFORM",
                status = "ACTIVE",
                cronExpression = "0 0 2 * * ?",
                description = "内部数据交互平台系统"
            ),
            SystemScheduleInfo(
                id = 2L,
                systemName = "外部系统",
                systemCode = "EXTERNAL_SYSTEM",
                status = "ACTIVE",
                cronExpression = "0 0 6 1 * ?",
                description = "外部第三方系统"
            )
        )

        whenever(jdbcTemplate.query(eq(expectedSql), any<RowMapper<SystemScheduleInfo>>()))
            .thenReturn(mockSystems)

        // When
        val result = lineageRepository.findSystemsWithCronExpression()

        // Then
        assertThat(result).isNotNull
        assertThat(result).hasSize(2)
        assertThat(result[0].id).isEqualTo(1L)
        assertThat(result[0].systemName).isEqualTo("数据交互平台")
        assertThat(result[0].systemCode).isEqualTo("DATA_EXCHANGE_PLATFORM")
        assertThat(result[0].status).isEqualTo("ACTIVE")
        assertThat(result[0].cronExpression).isEqualTo("0 0 2 * * ?")
        assertThat(result[0].description).isEqualTo("内部数据交互平台系统")

        assertThat(result[1].id).isEqualTo(2L)
        assertThat(result[1].systemName).isEqualTo("外部系统")

        verify(jdbcTemplate).query(eq(expectedSql), any<RowMapper<SystemScheduleInfo>>())
    }

    @Test
    @DisplayName("当没有活跃系统具有cron表达式时应该返回空列表")
    fun `should return empty list when no active systems have cron expression`() {
        // Given
        val expectedSql = """
            SELECT id, system_name, system_code, status, cron_expression, description
            FROM lineage_systems 
            WHERE status = 'ACTIVE' 
              AND cron_expression IS NOT NULL 
              AND cron_expression != ''
            ORDER BY id
        """.trimIndent()

        whenever(jdbcTemplate.query(eq(expectedSql), any<RowMapper<SystemScheduleInfo>>()))
            .thenReturn(emptyList())

        // When
        val result = lineageRepository.findSystemsWithCronExpression()

        // Then
        assertThat(result).isNotNull
        assertThat(result).isEmpty()

        verify(jdbcTemplate).query(eq(expectedSql), any<RowMapper<SystemScheduleInfo>>())
    }

    @Test
    @DisplayName("应该成功根据系统ID查询系统调度信息")
    fun `should successfully find system schedule info by system id`() {
        // Given
        val systemId = 1L
        val expectedSql = """
            SELECT id, system_name, system_code, status, cron_expression, description
            FROM lineage_systems 
            WHERE id = ?
        """.trimIndent()

        val mockSystemInfo = SystemScheduleInfo(
            id = systemId,
            systemName = "数据交互平台",
            systemCode = "DATA_EXCHANGE_PLATFORM",
            status = "ACTIVE",
            cronExpression = "0 0 2 * * ?",
            description = "内部数据交互平台系统"
        )

        whenever(jdbcTemplate.queryForObject(eq(expectedSql), any<RowMapper<SystemScheduleInfo>>(), eq(systemId)))
            .thenReturn(mockSystemInfo)

        // When
        val result = lineageRepository.findSystemScheduleInfo(systemId)

        // Then
        assertThat(result).isNotNull
        assertThat(result!!.id).isEqualTo(systemId)
        assertThat(result.systemName).isEqualTo("数据交互平台")
        assertThat(result.systemCode).isEqualTo("DATA_EXCHANGE_PLATFORM")
        assertThat(result.status).isEqualTo("ACTIVE")
        assertThat(result.cronExpression).isEqualTo("0 0 2 * * ?")
        assertThat(result.description).isEqualTo("内部数据交互平台系统")

        verify(jdbcTemplate).queryForObject(eq(expectedSql), any<RowMapper<SystemScheduleInfo>>(), eq(systemId))
    }

    @Test
    @DisplayName("当系统不存在时应该返回null")
    fun `should return null when system does not exist`() {
        // Given
        val nonExistentSystemId = 999L
        val expectedSql = """
            SELECT id, system_name, system_code, status, cron_expression, description
            FROM lineage_systems 
            WHERE id = ?
        """.trimIndent()

        whenever(jdbcTemplate.queryForObject(eq(expectedSql), any<RowMapper<SystemScheduleInfo>>(), eq(nonExistentSystemId)))
            .thenThrow(EmptyResultDataAccessException(1))

        // When
        val result = lineageRepository.findSystemScheduleInfo(nonExistentSystemId)

        // Then
        assertThat(result).isNull()

        verify(jdbcTemplate).queryForObject(eq(expectedSql), any<RowMapper<SystemScheduleInfo>>(), eq(nonExistentSystemId))
    }

    @Test
    @DisplayName("应该正确处理数据库异常")
    fun `should handle database exceptions correctly`() {
        // Given
        val expectedSql = """
            SELECT id, system_name, system_code, status, cron_expression, description
            FROM lineage_systems 
            WHERE status = 'ACTIVE' 
              AND cron_expression IS NOT NULL 
              AND cron_expression != ''
            ORDER BY id
        """.trimIndent()

        whenever(jdbcTemplate.query(eq(expectedSql), any<RowMapper<SystemScheduleInfo>>()))
            .thenThrow(RuntimeException("数据库连接失败"))

        // When & Then
        try {
            lineageRepository.findSystemsWithCronExpression()
            assertThat(false).withFailMessage("应该抛出异常").isTrue()
        } catch (e: RuntimeException) {
            assertThat(e.message).isEqualTo("数据库连接失败")
        }

        verify(jdbcTemplate).query(eq(expectedSql), any<RowMapper<SystemScheduleInfo>>())
    }

    @Test
    @DisplayName("应该正确映射ResultSet到SystemScheduleInfo")
    fun `should correctly map ResultSet to SystemScheduleInfo`() {
        // Given
        val systemId = 1L
        val expectedSql = """
            SELECT id, system_name, system_code, status, cron_expression, description
            FROM lineage_systems 
            WHERE id = ?
        """.trimIndent()

        whenever(resultSet.getLong("id")).thenReturn(1L)
        whenever(resultSet.getString("system_name")).thenReturn("数据交互平台")
        whenever(resultSet.getString("system_code")).thenReturn("DATA_EXCHANGE_PLATFORM")
        whenever(resultSet.getString("status")).thenReturn("ACTIVE")
        whenever(resultSet.getString("cron_expression")).thenReturn("0 0 2 * * ?")
        whenever(resultSet.getString("description")).thenReturn("内部数据交互平台系统")

        // 使用ArgumentCaptor来捕获RowMapper
        val rowMapperCaptor = argumentCaptor<RowMapper<SystemScheduleInfo>>()
        whenever(jdbcTemplate.queryForObject(eq(expectedSql), rowMapperCaptor.capture(), eq(systemId)))
            .thenAnswer {
                // 调用捕获的RowMapper
                rowMapperCaptor.firstValue.mapRow(resultSet, 0)
            }

        // When
        val result = lineageRepository.findSystemScheduleInfo(systemId)

        // Then
        assertThat(result).isNotNull
        assertThat(result!!.id).isEqualTo(1L)
        assertThat(result.systemName).isEqualTo("数据交互平台")
        assertThat(result.systemCode).isEqualTo("DATA_EXCHANGE_PLATFORM")
        assertThat(result.status).isEqualTo("ACTIVE")
        assertThat(result.cronExpression).isEqualTo("0 0 2 * * ?")
        assertThat(result.description).isEqualTo("内部数据交互平台系统")

        verify(jdbcTemplate).queryForObject(eq(expectedSql), any<RowMapper<SystemScheduleInfo>>(), eq(systemId))
        verify(resultSet).getLong("id")
        verify(resultSet).getString("system_name")
        verify(resultSet).getString("system_code")
        verify(resultSet).getString("status")
        verify(resultSet).getString("cron_expression")
        verify(resultSet).getString("description")
    }

    @Test
    @DisplayName("应该正确处理null值字段")
    fun `should correctly handle null field values`() {
        // Given
        val systemId = 1L
        val expectedSql = """
            SELECT id, system_name, system_code, status, cron_expression, description
            FROM lineage_systems 
            WHERE id = ?
        """.trimIndent()

        whenever(resultSet.getLong("id")).thenReturn(1L)
        whenever(resultSet.getString("system_name")).thenReturn("数据交互平台")
        whenever(resultSet.getString("system_code")).thenReturn("DATA_EXCHANGE_PLATFORM")
        whenever(resultSet.getString("status")).thenReturn("ACTIVE")
        whenever(resultSet.getString("cron_expression")).thenReturn(null) // null cron表达式
        whenever(resultSet.getString("description")).thenReturn(null) // null描述

        val rowMapperCaptor = argumentCaptor<RowMapper<SystemScheduleInfo>>()
        whenever(jdbcTemplate.queryForObject(eq(expectedSql), rowMapperCaptor.capture(), eq(systemId)))
            .thenAnswer {
                rowMapperCaptor.firstValue.mapRow(resultSet, 0)
            }

        // When
        val result = lineageRepository.findSystemScheduleInfo(systemId)

        // Then
        assertThat(result).isNotNull
        assertThat(result!!.id).isEqualTo(1L)
        assertThat(result.systemName).isEqualTo("数据交互平台")
        assertThat(result.systemCode).isEqualTo("DATA_EXCHANGE_PLATFORM")
        assertThat(result.status).isEqualTo("ACTIVE")
        assertThat(result.cronExpression).isNull()
        assertThat(result.description).isNull()

        verify(jdbcTemplate).queryForObject(eq(expectedSql), any<RowMapper<SystemScheduleInfo>>(), eq(systemId))
    }

    @Test
    @DisplayName("应该使用正确的SQL查询参数")
    fun `should use correct SQL query parameters`() {
        // Given
        val systemId = 42L
        val expectedSql = """
            SELECT id, system_name, system_code, status, cron_expression, description
            FROM lineage_systems 
            WHERE id = ?
        """.trimIndent()

        whenever(jdbcTemplate.queryForObject(eq(expectedSql), any<RowMapper<SystemScheduleInfo>>(), eq(systemId)))
            .thenThrow(EmptyResultDataAccessException(1))

        // When
        lineageRepository.findSystemScheduleInfo(systemId)

        // Then
        verify(jdbcTemplate).queryForObject(
            eq(expectedSql),
            any<RowMapper<SystemScheduleInfo>>(),
            eq(systemId)
        )
    }

    @Test
    @DisplayName("应该按ID排序查询结果")
    fun `should order query results by id`() {
        // Given
        val expectedSql = """
            SELECT id, system_name, system_code, status, cron_expression, description
            FROM lineage_systems 
            WHERE status = 'ACTIVE' 
              AND cron_expression IS NOT NULL 
              AND cron_expression != ''
            ORDER BY id
        """.trimIndent()

        whenever(jdbcTemplate.query(eq(expectedSql), any<RowMapper<SystemScheduleInfo>>()))
            .thenReturn(emptyList())

        // When
        lineageRepository.findSystemsWithCronExpression()

        // Then
        verify(jdbcTemplate).query(eq(expectedSql), any<RowMapper<SystemScheduleInfo>>())
        // SQL中包含ORDER BY id子句
    }

    @Test
    @DisplayName("应该过滤掉空字符串的cron表达式")
    fun `should filter out empty string cron expressions`() {
        // Given
        val expectedSql = """
            SELECT id, system_name, system_code, status, cron_expression, description
            FROM lineage_systems 
            WHERE status = 'ACTIVE' 
              AND cron_expression IS NOT NULL 
              AND cron_expression != ''
            ORDER BY id
        """.trimIndent()

        whenever(jdbcTemplate.query(eq(expectedSql), any<RowMapper<SystemScheduleInfo>>()))
            .thenReturn(emptyList())

        // When
        lineageRepository.findSystemsWithCronExpression()

        // Then
        verify(jdbcTemplate).query(eq(expectedSql), any<RowMapper<SystemScheduleInfo>>())
        // SQL中包含cron_expression != ''条件
    }
}