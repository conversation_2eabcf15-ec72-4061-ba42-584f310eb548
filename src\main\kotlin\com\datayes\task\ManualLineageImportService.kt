package com.datayes.task

import com.datayes.lineage.*
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import java.time.LocalDateTime

/**
 * 手动血缘导入服务 (Manual Lineage Import Service)
 *
 * 负责解析上传的文件并将其转换为DataLineage实体
 * 支持JSON和CSV格式的血缘数据文件
 */
@Service
class ManualLineageImportService(
    private val objectMapper: ObjectMapper,
    private val lineageService: LineageService
) {

    private val logger = LoggerFactory.getLogger(ManualLineageImportService::class.java)

    /**
     * 解析上传的血缘文件 (Parse uploaded lineage file)
     * 
     * @param file 上传的文件
     * @return 解析的血缘数据列表
     */
    fun parseLineageFile(file: MultipartFile): List<DataLineage> {
        validateUploadedFile(file)
        
        val fileName = file.originalFilename!!
        val content = file.inputStream.bufferedReader().use { it.readText() }
        
        if (content.isBlank()) {
            throw IllegalArgumentException("文件内容不能为空")
        }
        
        logger.info("4b7e92c1 | 开始解析血缘文件: fileName=$fileName, size=${file.size}")
        
        return when {
            fileName.endsWith(".json", ignoreCase = true) -> parseJsonFile(content, fileName)
            fileName.endsWith(".csv", ignoreCase = true) -> parseCsvFile(content, fileName)
            else -> throw IllegalArgumentException("不支持的文件格式: $fileName. 仅支持 .json 和 .csv 格式")
        }
    }

    /**
     * 验证上传的文件 (Validate uploaded file)
     */
    private fun validateUploadedFile(file: MultipartFile) {
        if (file.isEmpty) {
            throw IllegalArgumentException("上传文件不能为空")
        }
        
        val fileName = file.originalFilename
        if (fileName.isNullOrBlank()) {
            throw IllegalArgumentException("文件名不能为空")
        }
        
        val maxFileSize = 10 * 1024 * 1024 // 10MB
        if (file.size > maxFileSize) {
            throw IllegalArgumentException("文件大小不能超过10MB，当前文件大小: ${file.size / 1024 / 1024}MB")
        }
        
        val supportedExtensions = listOf(".json", ".csv")
        val hasValidExtension = supportedExtensions.any { fileName.endsWith(it, ignoreCase = true) }
        if (!hasValidExtension) {
            throw IllegalArgumentException("不支持的文件格式: $fileName. 仅支持 ${supportedExtensions.joinToString(", ")} 格式")
        }
        
        logger.info("d3a8f5e1 | 文件验证通过: fileName=$fileName, size=${file.size}")
    }

    /**
     * 解析JSON格式的血缘文件 (Parse JSON lineage file)
     */
    private fun parseJsonFile(content: String, fileName: String): List<DataLineage> {
        return try {
            val jsonNode = objectMapper.readTree(content)
            
            when {
                jsonNode.isArray -> {
                    jsonNode.map { parseJsonLineage(it, fileName) }
                }
                jsonNode.isObject -> {
                    if (jsonNode.has("lineages") && jsonNode["lineages"].isArray) {
                        jsonNode["lineages"].map { parseJsonLineage(it, fileName) }
                    } else {
                        listOf(parseJsonLineage(jsonNode, fileName))
                    }
                }
                else -> throw IllegalArgumentException("JSON格式错误: 根节点必须是对象或数组")
            }
        } catch (e: Exception) {
            logger.error("8f3a5d67 | 解析JSON文件失败: fileName=$fileName", e)
            throw IllegalArgumentException("JSON文件解析失败: ${e.message}", e)
        }
    }

    /**
     * 解析单个JSON血缘对象 (Parse single JSON lineage object)
     */
    private fun parseJsonLineage(jsonNode: JsonNode, fileName: String): DataLineage {
        try {
            val jobId = jsonNode.get("jobId")?.asText() 
                ?: jsonNode.get("job_id")?.asText() 
                ?: "manual_import_${System.currentTimeMillis()}"
            
            val jobName = jsonNode.get("jobName")?.asText() 
                ?: jsonNode.get("job_name")?.asText() 
                ?: "手动导入-$fileName"

            // 解析源数据库
            val sourceDbNode = jsonNode.get("sourceDatabase") ?: jsonNode.get("source_database")
                ?: throw IllegalArgumentException("缺少源数据库信息 (sourceDatabase)")
            val sourceDatabase = parseJsonDatabaseInfo(sourceDbNode)

            // 解析目标数据库
            val targetDbNode = jsonNode.get("targetDatabase") ?: jsonNode.get("target_database")
                ?: throw IllegalArgumentException("缺少目标数据库信息 (targetDatabase)")
            val targetDatabase = parseJsonDatabaseInfo(targetDbNode)

            // 解析表血缘
            val tableLineageNode = jsonNode.get("tableLineage") ?: jsonNode.get("table_lineage")
                ?: throw IllegalArgumentException("缺少表血缘信息 (tableLineage)")
            val tableLineage = parseJsonTableLineage(tableLineageNode, sourceDatabase, targetDatabase)

            // 解析列血缘
            val columnLineagesNode = jsonNode.get("columnLineages") ?: jsonNode.get("column_lineages")
            val columnLineages = if (columnLineagesNode?.isArray == true) {
                columnLineagesNode.mapIndexed { index, node ->
                    parseJsonColumnLineage(node, tableLineage.sourceTables, tableLineage.targetTable, index)
                }
            } else {
                emptyList()
            }

            val originalSql = jsonNode.get("originalSql")?.asText() 
                ?: jsonNode.get("original_sql")?.asText() 
                ?: "-- 手动导入的血缘数据"

            return DataLineage(
                jobId = jobId,
                jobName = jobName,
                tableLineage = tableLineage,
                columnLineages = columnLineages,
                sourceDatabase = sourceDatabase,
                targetDatabase = targetDatabase,
                originalSql = originalSql,
                createdAt = LocalDateTime.now()
            )

        } catch (e: Exception) {
            logger.error("9a2c8e14 | 解析JSON血缘对象失败", e)
            throw IllegalArgumentException("JSON血缘对象解析失败: ${e.message}", e)
        }
    }

    /**
     * 解析JSON数据库信息 (Parse JSON database info)
     */
    private fun parseJsonDatabaseInfo(dbNode: JsonNode): DatabaseInfo {
        val dbType = dbNode.get("dbType")?.asText() ?: dbNode.get("db_type")?.asText() ?: "unknown"
        val host = dbNode.get("host")?.asText() ?: "unknown"
        val port = dbNode.get("port")?.asInt() ?: 3306
        val databaseName = dbNode.get("databaseName")?.asText() ?: dbNode.get("database_name")?.asText() ?: "unknown"
        val connectionString = dbNode.get("originalConnectionString")?.asText() 
            ?: dbNode.get("original_connection_string")?.asText()
            ?: "jdbc:$dbType://$host:$port/$databaseName"

        return DatabaseInfo(
            dbType = dbType,
            host = host,
            port = port,
            databaseName = databaseName,
            originalConnectionString = connectionString
        )
    }

    /**
     * 解析JSON表血缘 (Parse JSON table lineage)
     */
    private fun parseJsonTableLineage(
        tableNode: JsonNode, 
        sourceDatabase: DatabaseInfo, 
        targetDatabase: DatabaseInfo
    ): TableLineage {
        val sourceTablesNode = tableNode.get("sourceTables") ?: tableNode.get("source_tables")
            ?: throw IllegalArgumentException("缺少源表信息 (sourceTables)")
        
        val sourceTables = if (sourceTablesNode.isArray) {
            sourceTablesNode.map { parseJsonTableInfo(it, sourceDatabase) }
        } else {
            listOf(parseJsonTableInfo(sourceTablesNode, sourceDatabase))
        }

        val targetTableNode = tableNode.get("targetTable") ?: tableNode.get("target_table")
            ?: throw IllegalArgumentException("缺少目标表信息 (targetTable)")
        val targetTable = parseJsonTableInfo(targetTableNode, targetDatabase)

        val lineageTypeStr = tableNode.get("lineageType")?.asText() 
            ?: tableNode.get("lineage_type")?.asText() 
            ?: "DIRECT_COPY"
        val lineageType = try {
            LineageType.valueOf(lineageTypeStr.uppercase())
        } catch (e: IllegalArgumentException) {
            LineageType.DIRECT_COPY
        }

        return TableLineage(
            sourceTables = sourceTables,
            targetTable = targetTable,
            lineageType = lineageType
        )
    }

    /**
     * 解析JSON表信息 (Parse JSON table info)
     */
    private fun parseJsonTableInfo(tableNode: JsonNode, database: DatabaseInfo): TableInfo {
        val tableName = tableNode.get("tableName")?.asText() 
            ?: tableNode.get("table_name")?.asText()
            ?: tableNode.asText()
            ?: throw IllegalArgumentException("缺少表名")
        
        val schema = tableNode.get("schema")?.asText()

        return TableInfo(
            schema = schema,
            tableName = tableName,
            database = database
        )
    }

    /**
     * 解析JSON列血缘 (Parse JSON column lineage)
     */
    private fun parseJsonColumnLineage(
        columnNode: JsonNode,
        sourceTables: List<TableInfo>,
        targetTable: TableInfo,
        index: Int
    ): ColumnLineage {
        val sourceColumnNode = columnNode.get("sourceColumn") ?: columnNode.get("source_column")
            ?: throw IllegalArgumentException("缺少源列信息 (sourceColumn)")
        val sourceColumn = parseJsonColumnInfo(sourceColumnNode, sourceTables)

        val targetColumnNode = columnNode.get("targetColumn") ?: columnNode.get("target_column")
            ?: throw IllegalArgumentException("缺少目标列信息 (targetColumn)")
        val targetColumn = parseJsonColumnInfo(targetColumnNode, listOf(targetTable))

        val transformationNode = columnNode.get("transformation")
        val transformation = if (transformationNode != null) {
            parseJsonTransformation(transformationNode)
        } else null

        return ColumnLineage(
            sourceColumn = sourceColumn,
            targetColumn = targetColumn,
            transformation = transformation,
            columnIndex = index
        )
    }

    /**
     * 解析JSON列信息 (Parse JSON column info)
     */
    private fun parseJsonColumnInfo(columnNode: JsonNode, tables: List<TableInfo>): ColumnInfo {
        val columnName = columnNode.get("columnName")?.asText() 
            ?: columnNode.get("column_name")?.asText()
            ?: columnNode.asText()
            ?: throw IllegalArgumentException("缺少列名")
        
        val dataType = columnNode.get("dataType")?.asText() ?: columnNode.get("data_type")?.asText() ?: "VARCHAR"
        val comment = columnNode.get("comment")?.asText()

        val tableName = columnNode.get("tableName")?.asText() ?: columnNode.get("table_name")?.asText()
        val table = if (tableName != null) {
            tables.find { it.tableName == tableName }
                ?: throw IllegalArgumentException("找不到表: $tableName")
        } else {
            tables.firstOrNull() ?: throw IllegalArgumentException("无法确定列所属的表")
        }

        return ColumnInfo(
            columnName = columnName,
            dataType = dataType,
            comment = comment,
            table = table
        )
    }

    /**
     * 解析JSON转换信息 (Parse JSON transformation)
     */
    private fun parseJsonTransformation(transformationNode: JsonNode): DataTransformation {
        val typeStr = transformationNode.get("transformationType")?.asText() 
            ?: transformationNode.get("transformation_type")?.asText() 
            ?: "NONE"
        
        val transformationType = try {
            TransformationType.valueOf(typeStr.uppercase())
        } catch (e: IllegalArgumentException) {
            TransformationType.NONE
        }

        val description = transformationNode.get("description")?.asText() ?: "手动导入的转换"
        val expression = transformationNode.get("expression")?.asText()

        return DataTransformation(
            transformationType = transformationType,
            description = description,
            expression = expression
        )
    }

    /**
     * 解析CSV格式的血缘文件 (Parse CSV lineage file)
     */
    private fun parseCsvFile(content: String, fileName: String): List<DataLineage> {
        logger.info("6e8d9a42 | 开始解析CSV格式血缘文件: fileName=$fileName")
        
        val lines = content.split("\n").map { it.trim() }.filter { it.isNotEmpty() }
        if (lines.isEmpty()) {
            throw IllegalArgumentException("CSV文件为空")
        }

        if (lines.size < 2) {
            throw IllegalArgumentException("CSV文件必须包含标题行和至少一行数据")
        }

        val header = lines.first().split(",").map { it.trim() }
        if (header.isEmpty()) {
            throw IllegalArgumentException("CSV文件标题行不能为空")
        }

        val dataLines = lines.drop(1)
        if (dataLines.isEmpty()) {
            throw IllegalArgumentException("CSV文件必须包含至少一行数据")
        }

        validateCsvHeaders(header)

        logger.info("2f5b7c83 | CSV文件解析: 标题行=${header.joinToString(",")}, 数据行数=${dataLines.size}")

        return dataLines.mapIndexed { index, line ->
            try {
                parseCsvLineage(line, header, fileName, index)
            } catch (e: Exception) {
                logger.error("1a3e8f95 | 解析CSV第${index + 2}行失败: line=$line", e)
                throw IllegalArgumentException("CSV第${index + 2}行解析失败: ${e.message}", e)
            }
        }
    }

    /**
     * 验证CSV标题行 (Validate CSV headers)
     */
    private fun validateCsvHeaders(headers: List<String>) {
        val requiredHeaders = listOf("source_table", "target_table")
        val missingHeaders = requiredHeaders.filter { required ->
            headers.none { it.equals(required, ignoreCase = true) || it.equals(required.replace("_", ""), ignoreCase = true) }
        }
        
        if (missingHeaders.isNotEmpty()) {
            throw IllegalArgumentException(
                "CSV文件缺少必需的列: ${missingHeaders.joinToString(", ")}。" +
                "必需列: ${requiredHeaders.joinToString(", ")}"
            )
        }
        
        logger.info("8e3b4c71 | CSV标题验证通过: headers=${headers.joinToString(",")}")
    }

    /**
     * 解析单行CSV血缘数据 (Parse single CSV lineage row)
     */
    private fun parseCsvLineage(line: String, header: List<String>, fileName: String, index: Int): DataLineage {
        val values = line.split(",").map { it.trim().removeSurrounding("\"") }
        
        if (values.size != header.size) {
            throw IllegalArgumentException("列数不匹配: 期望${header.size}列，实际${values.size}列")
        }

        val data = header.zip(values).toMap()

        val jobId = data["job_id"] ?: data["jobId"] ?: "csv_import_${System.currentTimeMillis()}_$index"
        val jobName = data["job_name"] ?: data["jobName"] ?: "CSV导入-$fileName-行${index + 2}"

        // 构建简单的数据库信息
        val sourceDbName = data["source_database"] ?: data["sourceDatabase"] ?: "unknown_source"
        val targetDbName = data["target_database"] ?: data["targetDatabase"] ?: "unknown_target"
        
        val sourceDatabase = DatabaseInfo(
            dbType = data["source_db_type"] ?: "mysql",
            host = data["source_host"] ?: "unknown",
            port = data["source_port"]?.toIntOrNull() ?: 3306,
            databaseName = sourceDbName,
            originalConnectionString = "***************************************"
        )

        val targetDatabase = DatabaseInfo(
            dbType = data["target_db_type"] ?: "mysql",
            host = data["target_host"] ?: "unknown",
            port = data["target_port"]?.toIntOrNull() ?: 3306,
            databaseName = targetDbName,
            originalConnectionString = "***************************************"
        )

        // 构建表血缘
        val sourceTableName = data["source_table"] ?: data["sourceTable"] 
            ?: throw IllegalArgumentException("缺少源表名 (source_table)")
        val targetTableName = data["target_table"] ?: data["targetTable"] 
            ?: throw IllegalArgumentException("缺少目标表名 (target_table)")

        val sourceTable = TableInfo(
            schema = data["source_schema"],
            tableName = sourceTableName,
            database = sourceDatabase
        )

        val targetTable = TableInfo(
            schema = data["target_schema"],
            tableName = targetTableName,
            database = targetDatabase
        )

        val tableLineage = TableLineage(
            sourceTables = listOf(sourceTable),
            targetTable = targetTable,
            lineageType = LineageType.DIRECT_COPY
        )

        // 构建列血缘（如果有列信息）
        val columnLineages = mutableListOf<ColumnLineage>()
        val sourceColumn = data["source_column"] ?: data["sourceColumn"]
        val targetColumn = data["target_column"] ?: data["targetColumn"]

        if (sourceColumn != null && targetColumn != null) {
            val sourceColumnInfo = ColumnInfo(
                columnName = sourceColumn,
                dataType = data["source_column_type"] ?: "VARCHAR",
                comment = null,
                table = sourceTable
            )

            val targetColumnInfo = ColumnInfo(
                columnName = targetColumn,
                dataType = data["target_column_type"] ?: "VARCHAR",
                comment = null,
                table = targetTable
            )

            columnLineages.add(
                ColumnLineage(
                    sourceColumn = sourceColumnInfo,
                    targetColumn = targetColumnInfo,
                    transformation = null,
                    columnIndex = 0
                )
            )
        }

        val originalSql = data["sql"] ?: data["original_sql"] ?: "-- CSV导入的血缘数据"

        return DataLineage(
            jobId = jobId,
            jobName = jobName,
            tableLineage = tableLineage,
            columnLineages = columnLineages,
            sourceDatabase = sourceDatabase,
            targetDatabase = targetDatabase,
            originalSql = originalSql,
            createdAt = LocalDateTime.now()
        )
    }

    /**
     * 验证血缘数据 (Validate lineage data)
     */
    fun validateLineageData(lineages: List<DataLineage>) {
        if (lineages.isEmpty()) {
            throw IllegalArgumentException("解析到的血缘数据为空")
        }

        lineages.forEachIndexed { index, lineage ->
            try {
                validateSingleLineage(lineage)
            } catch (e: Exception) {
                throw IllegalArgumentException("第${index + 1}个血缘记录验证失败: ${e.message}", e)
            }
        }

        logger.info("5c8f2a69 | 血缘数据验证完成: 共${lineages.size}条记录")
    }

    /**
     * 验证单个血缘数据 (Validate single lineage data)
     */
    private fun validateSingleLineage(lineage: DataLineage) {
        if (lineage.jobId.isBlank()) {
            throw IllegalArgumentException("jobId不能为空")
        }

        if (lineage.jobName.isBlank()) {
            throw IllegalArgumentException("jobName不能为空")
        }

        if (lineage.tableLineage.sourceTables.isEmpty()) {
            throw IllegalArgumentException("源表列表不能为空")
        }

        if (lineage.tableLineage.targetTable.tableName.isBlank()) {
            throw IllegalArgumentException("目标表名不能为空")
        }

        // 验证列血缘中的表引用
        lineage.columnLineages.forEach { columnLineage ->
            val sourceTableFound = lineage.tableLineage.sourceTables.any { 
                it.tableName == columnLineage.sourceColumn.table.tableName 
            }
            if (!sourceTableFound) {
                throw IllegalArgumentException(
                    "列血缘中的源表 '${columnLineage.sourceColumn.table.tableName}' 不在表血缘的源表列表中"
                )
            }

            if (columnLineage.targetColumn.table.tableName != lineage.tableLineage.targetTable.tableName) {
                throw IllegalArgumentException(
                    "列血缘中的目标表 '${columnLineage.targetColumn.table.tableName}' 与表血缘的目标表不匹配"
                )
            }
        }
    }
}