package com.datayes.dataexchange

import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.jdbc.DataSourceBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.jdbc.core.JdbcTemplate
import javax.sql.DataSource

/**
 * 数据交互平台数据源配置 (Data Exchange Platform DataSource Configuration)
 * 
 * 配置用于访问数据交互平台MySQL数据库的独立数据源
 * 此数据源不是Spring Boot的主数据源，仅用于查询作业配置信息
 */
@Configuration
class DataExchangeJobDataSourceConfig {

    @Value("\${dataexchange.datasource.url}")
    private lateinit var url: String

    @Value("\${dataexchange.datasource.username}")
    private lateinit var username: String

    @Value("\${dataexchange.datasource.password}")
    private lateinit var password: String

    @Value("\${dataexchange.datasource.driver-class-name}")
    private lateinit var driverClassName: String

    /**
     * 创建数据交互平台专用数据源
     * 
     * @return 配置好的数据源
     */
    @Bean(name = ["dataExchangeDataSource"])
    fun dataExchangeDataSource(): DataSource {
        return DataSourceBuilder.create()
            .url(url)
            .username(username)
            .password(password)
            .driverClassName(driverClassName)
            .build()
    }

    /**
     * 创建数据交互平台专用JdbcTemplate
     * 
     * @param dataExchangeDataSource 数据交互平台数据源
     * @return 配置好的JdbcTemplate
     */
    @Bean(name = ["dataExchangeJdbcTemplate"])
    fun dataExchangeJdbcTemplate(@Qualifier("dataExchangeDataSource") dataExchangeDataSource: DataSource): JdbcTemplate {
        return JdbcTemplate(dataExchangeDataSource)
    }
} 
