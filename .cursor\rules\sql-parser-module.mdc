---
description:
globs:
alwaysApply: false
---
# SQL 解析器模块 (SQL Parser Module)

## 模块概述 (Module Overview)
SQL 解析器模块负责解析 SQL 语句并提取数据血缘关系 (data lineage relationships)，识别数据流向和依赖关系。

## 核心组件 (Core Components)

### 1. 解析器核心 (Parser Core)
- [SqlParser.kt](mdc:src/main/kotlin/com/datayes/parser/SqlParser.kt) - SQL 语句解析器 (SQL statement parser)
- [SqlParseResult.kt](mdc:src/main/kotlin/com/datayes/parser/SqlParseResult.kt) - 解析结果 (parse result)
- [SqlParsingException.kt](mdc:src/main/kotlin/com/datayes/parser/SqlParsingException.kt) - 解析异常 (parsing exception)

### 2. 血缘处理 (Lineage Processing)
- [Lineage.kt](mdc:src/main/kotlin/com/datayes/parser/Lineage.kt) - 血缘关系数据模型 (lineage relationship data model)
- [LineageConverter.kt](mdc:src/main/kotlin/com/datayes/parser/LineageConverter.kt) - 血缘转换器 (lineage converter)

## 功能特性 (Features)
- SQL 语句解析 (SQL statement parsing)
- 表依赖关系提取 (table dependency extraction)
- 列级血缘分析 (column-level lineage analysis)  
- 数据流向识别 (data flow identification)
- 多种 SQL 方言支持 (multiple SQL dialect support)

## 解析流程 (Parsing Process)
1. **词法分析** (Lexical Analysis) - 将 SQL 文本分解为标记 (tokens)
2. **语法分析** (Syntax Analysis) - 构建抽象语法树 (Abstract Syntax Tree)
3. **语义分析** (Semantic Analysis) - 提取血缘关系
4. **结果转换** (Result Conversion) - 生成标准化血缘数据

## 支持的 SQL 操作 (Supported SQL Operations)
- SELECT 查询 (SELECT queries)
- INSERT 插入 (INSERT statements)
- UPDATE 更新 (UPDATE statements)
- DELETE 删除 (DELETE statements)
- CREATE TABLE AS SELECT (CTAS)
- VIEW 视图 (VIEW definitions)

## 使用示例 (Usage Example)
```kotlin
val parser = SqlParser()
val result = parser.parse(sqlStatement)
val lineage = LineageConverter.convert(result)
```

## 设计原则 (Design Principles)
- 纯函数设计 (pure function design)
- 不可变数据结构 (immutable data structures)
- 错误处理优先 (error handling first)
- 可扩展架构 (extensible architecture)
