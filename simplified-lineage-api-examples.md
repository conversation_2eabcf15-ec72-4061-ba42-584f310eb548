# 简化血缘管理 API 使用示例 (Simplified Lineage Management API Examples)

## 概述 (Overview)

新的简化血缘管理 API 提供了更直观的 CRUD 操作接口，自动处理数据源、表和列的创建，统一使用 `source_system = 'MANUAL_INPUT'`。

## API 端点 (API Endpoints)

### 基础路径
```
/api/manual-lineage/v2/
```

### 1. 创建血缘关系 (Create Lineage Relationship)

**端点**: `POST /api/manual-lineage/v2/create`

**功能**: 自动创建数据源、表和列（如果不存在），创建表级和列级血缘关系

**请求示例**:
```json
{
    "sourceDatasourceName": "mysql_prod",
    "sourceDbType": "mysql",
    "sourceHost": "**********",
    "sourcePort": 3306,
    "sourceDatabaseName": "ecommerce",
    "sourceSchemaName": null,
    "sourceTableName": "orders",

    "targetDatasourceName": "warehouse",
    "targetDbType": "mysql", 
    "targetHost": "**********",
    "targetPort": 3306,
    "targetDatabaseName": "analytics",
    "targetSchemaName": null,
    "targetTableName": "fact_orders",

    "columns": [
        {
            "sourceColumnName": "order_id",
            "sourceDataType": "BIGINT",
            "sourceColumnComment": "订单ID",
            "sourceIsPrimaryKey": true,
            "sourceIsNullable": false,

            "targetColumnName": "order_key",
            "targetDataType": "BIGINT", 
            "targetColumnComment": "订单主键",
            "targetIsPrimaryKey": true,
            "targetIsNullable": false
        },
        {
            "sourceColumnName": "customer_id",
            "sourceDataType": "BIGINT",
            "targetColumnName": "customer_key",
            "targetDataType": "BIGINT"
        },
        {
            "sourceColumnName": "order_date",
            "sourceDataType": "DATE",
            "targetColumnName": "order_date",
            "targetDataType": "DATE"
        }
    ],

    "updateBy": "data_analyst"
}
```

**使用自定义 JDBC URL 的示例**:
```json
{
    "sourceDatasourceName": "mysql_prod",
    "sourceDbType": "mysql",
    "sourceCustomJdbcUrl": "***************************************************",
    "sourceDatabaseName": "ecommerce",
    "sourceTableName": "orders",

    "targetDatasourceName": "warehouse", 
    "targetDbType": "mysql",
    "targetCustomJdbcUrl": "***************************************************",
    "targetDatabaseName": "analytics",
    "targetTableName": "fact_orders",

    "columns": [
        {
            "sourceColumnName": "order_id",
            "targetColumnName": "order_key"
        }
    ],

    "updateBy": "data_analyst"
}
```

**响应示例**:
```json
{
    "success": true,
    "message": "血缘关系创建成功",
    "tableRelationshipId": 1001,
    "affectedTableRelationships": 1,
    "affectedColumnRelationships": 3,
    "errors": [],
    "warnings": []
}
```

### 2. 更新血缘关系 (Update Lineage Relationship)

**端点**: `PUT /api/manual-lineage/v2/update`

**功能**: 根据表级关系ID更新列映射，替换现有的列映射关系

**请求示例**:
```json
{
    "tableRelationshipId": 1001,
    "columns": [
        {
            "sourceColumnName": "order_id",
            "sourceDataType": "BIGINT",
            "targetColumnName": "order_key", 
            "targetDataType": "BIGINT"
        },
        {
            "sourceColumnName": "customer_id",
            "sourceDataType": "BIGINT",
            "targetColumnName": "customer_key",
            "targetDataType": "BIGINT"
        },
        {
            "sourceColumnName": "order_amount",
            "sourceDataType": "DECIMAL(10,2)",
            "targetColumnName": "amount",
            "targetDataType": "DECIMAL(10,2)"
        }
    ],
    "updateBy": "data_analyst"
}
```

**响应示例**:
```json
{
    "success": true,
    "message": "血缘关系更新成功",
    "tableRelationshipId": 1001,
    "affectedTableRelationships": 1,
    "affectedColumnRelationships": 3,
    "errors": [],
    "warnings": []
}
```

### 3. 删除血缘关系 (Delete Lineage Relationships)

**端点**: `DELETE /api/manual-lineage/v2/delete`

**功能**: 批量删除表级和相关列级血缘关系

**请求示例**:
```json
{
    "tableRelationshipIdList": [1001, 1002, 1003]
}
```

**响应示例**:
```json
{
    "success": true,
    "message": "血缘关系删除成功",
    "tableRelationshipId": null,
    "affectedTableRelationships": 3,
    "affectedColumnRelationships": 8,
    "errors": [],
    "warnings": []
}
```

### 4. 获取血缘关系详情 (Get Lineage Relationship Details)

**端点**: `GET /api/manual-lineage/v2/details/{tableRelationshipId}`

**功能**: 根据表级血缘关系ID获取详细信息

**请求示例**:
```
GET /api/manual-lineage/v2/details/1001
```

**响应示例**:
```json
{
    "tableRelationshipId": 1001,
    "sourceDatasourceName": "mysql_prod",
    "sourceTableName": "orders",
    "sourceSchemaName": null,
    "targetDatasourceName": "warehouse",
    "targetTableName": "fact_orders", 
    "targetSchemaName": null,
    "columns": [
        {
            "columnRelationshipId": 5001,
            "sourceColumnName": "order_id",
            "sourceDataType": "BIGINT",
            "targetColumnName": "order_key",
            "targetDataType": "BIGINT"
        },
        {
            "columnRelationshipId": 5002,
            "sourceColumnName": "customer_id",
            "sourceDataType": "BIGINT", 
            "targetColumnName": "customer_key",
            "targetDataType": "BIGINT"
        }
    ],
    "createdBy": "data_analyst",
    "createdAt": "2024-01-15T10:30:00",
    "lastUpdatedBy": "data_analyst",
    "lastUpdatedAt": "2024-01-15T10:30:00"
}
```

## 错误处理 (Error Handling)

### 验证错误示例
```json
{
    "success": false,
    "message": "验证失败",
    "tableRelationshipId": null,
    "affectedTableRelationships": 0,
    "affectedColumnRelationships": 0,
    "errors": [
        "源数据源名称不能为空",
        "必须提供sourceCustomJdbcUrl或者同时提供sourceHost和sourcePort"
    ],
    "warnings": []
}
```

### 业务逻辑错误示例
```json
{
    "success": false,
    "message": "只能更新手动输入的血缘关系",
    "tableRelationshipId": 1001,
    "affectedTableRelationships": 0,
    "affectedColumnRelationships": 0,
    "errors": [
        "该血缘关系的来源为 SYSTEM_COLLECTED，只能更新来源为 MANUAL_INPUT 的关系"
    ],
    "warnings": []
}
```

## 特性说明 (Features)

### 1. 自动创建资源
- **数据源**: 如果不存在，自动创建 `lineage_datasources` 记录
- **表**: 如果不存在，自动创建 `lineage_tables` 记录  
- **列**: 如果不存在，自动创建 `lineage_columns` 记录

### 2. 安全性约束
- **创建**: 始终使用 `source_system = 'MANUAL_INPUT'`
- **更新**: 只能更新 `source_system = 'MANUAL_INPUT'` 的数据
- **删除**: 只能删除 `source_system = 'MANUAL_INPUT'` 的数据

### 3. 数据一致性
- **表级关系**: 在 `lineage_relationships` 表中创建 `relationship_type = 'TABLE_LEVEL'` 记录
- **列级关系**: 在 `lineage_relationships` 表中创建 `relationship_type = 'COLUMN_LEVEL'` 记录
- **软删除**: 使用 `is_active = false` 进行软删除，保留历史数据

### 4. 连接信息解析
- **优先级**: `customJdbcUrl` > (`host` + `port`)
- **JDBC URL 解析**: 自动从 JDBC URL 中解析主机和端口信息
- **回退机制**: 如果 JDBC URL 解析失败，使用直接提供的 host/port

## 与现有 API 的比较

### 旧 API (复杂)
```json
// 需要先创建表级关系，再单独创建列映射
POST /api/manual-lineage/table-lineage
{
    "sourceTableId": 100,  // 需要预先知道表ID
    "targetTableId": 200,
    "columnMappings": [...] 
}
```

### 新 API (简化)
```json
// 一次性创建所有内容，自动处理资源创建
POST /api/manual-lineage/v2/create  
{
    "sourceTableName": "orders",     // 直接使用表名
    "targetTableName": "fact_orders",
    "columns": [...]
}
```

## 最佳实践 (Best Practices)

1. **使用描述性的数据源名称**: 如 `mysql_prod`、`warehouse` 而不是 `db1`、`db2`
2. **提供完整的列信息**: 包括数据类型、注释等，便于后续维护
3. **使用自定义 JDBC URL**: 当连接字符串复杂时，使用 `customJdbcUrl` 更可靠
4. **批量删除**: 尽量使用批量删除接口提高效率
5. **错误处理**: 检查响应中的 `errors` 字段，处理验证失败的情况