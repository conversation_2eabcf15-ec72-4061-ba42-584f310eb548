package com.datayes.integration

import io.restassured.RestAssured.given
import io.restassured.http.ContentType
import io.restassured.path.json.JsonPath
import org.assertj.core.api.Assertions.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.MethodOrderer
import org.junit.jupiter.api.Order
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestMethodOrder

/**
 * 脚本上传功能专项测试 (Script Upload Functionality Specific Test)
 * 
 * 专门用于验证脚本上传功能是否正常工作，特别是文件名处理逻辑
 */
@DisplayName("脚本上传功能专项测试")
@TestMethodOrder(MethodOrderer.OrderAnnotation::class)
class ScriptUploadTestIT : RestApiIntegrationTestBase() {

    companion object {
        private const val TEST_UPLOAD_USER = "upload-test-user"
        private var uploadedScriptId: Long? = null
        private var actualScriptName: String? = null
    }

    @Test
    @Order(1)
    @DisplayName("应该成功上传SQL脚本并正确处理文件名")
    fun `should successfully upload SQL script and handle filename correctly`() {
        // 创建测试文件
        val tempFile = TestFileUtils.createTempSqlFile("upload_test.sql", """
            -- 简单的SQL测试脚本
            SELECT 'Hello, World!' as greeting;
            SELECT COUNT(*) FROM test_table;
        """.trimIndent())

        println("创建的临时文件名: ${tempFile.name}")

        val response = given()
            .contentType(ContentType.MULTIPART)
            .multiPart("file", tempFile, "application/octet-stream")
            .multiPart("uploadUser", TEST_UPLOAD_USER)
            .`when`()
            .post("/v1/script-impact/upload")
            .then()
            .statusCode(200)
            .contentType("application/json")
            .body("success", equalTo(true))
            .body("message", notNullValue())
            .body("data.scriptId", notNullValue())
            .body("data.scriptName", notNullValue())
            .body("data.scriptType", equalTo("SQL"))
            .body("data.uploadUser", equalTo(TEST_UPLOAD_USER))
            .body("data.analysisStatus", equalTo("PENDING"))
            .body("data.fileSize", greaterThan(0))
            .body("data.createdAt", notNullValue())
            .extract()
            .response()

        // 保存实际的脚本信息
        val jsonPath = JsonPath.from(response.asString())
        uploadedScriptId = jsonPath.getLong("data.scriptId")
        actualScriptName = jsonPath.getString("data.scriptName")

        // 验证结果
        assertThat(uploadedScriptId).isNotNull()
        assertThat(actualScriptName).isNotNull()
        assertThat(actualScriptName).endsWith(".sql")

        println("✅ 上传成功:")
        println("  脚本ID: $uploadedScriptId")
        println("  实际脚本名: $actualScriptName")
        println("  临时文件名: ${tempFile.name}")
        
        // 验证服务器使用的是临时文件的实际名称
        assertThat(actualScriptName).isEqualTo(tempFile.name)
    }

    @Test
    @Order(2)
    @DisplayName("应该能够查询上传的脚本")
    fun `should be able to query uploaded script`() {
        val response = given()
            .queryParam("uploadUser", TEST_UPLOAD_USER)
            .`when`()
            .get("/v1/script-impact/scripts")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data.content", hasSize<Any>(1))
            .body("data.content[0].id", equalTo(uploadedScriptId!!.toInt()))
            .body("data.content[0].scriptName", equalTo(actualScriptName))
            .body("data.content[0].uploadUser", equalTo(TEST_UPLOAD_USER))
            .extract()
            .response()

        println("✅ 查询验证成功: 找到上传的脚本")
    }

    @Test
    @Order(3)
    @DisplayName("应该能够下载上传的脚本")
    fun `should be able to download uploaded script`() {
        val response = given()
            .pathParam("scriptId", uploadedScriptId!!)
            .`when`()
            .get("/v1/script-impact/scripts/{scriptId}/download")
            .then()
            .statusCode(200)
            .header("Content-Disposition", containsString("attachment"))
            .header("Content-Disposition", containsString(actualScriptName!!))
            .extract()
            .response()

        val downloadedContent = response.asString()
        assertThat(downloadedContent).contains("Hello, World!")
        assertThat(downloadedContent).contains("test_table")

        println("✅ 下载验证成功: 内容长度=${downloadedContent.length}")
    }

    @Test
    @Order(4)
    @DisplayName("清理测试数据")
    fun `cleanup test data`() {
        // 删除上传的脚本
        given()
            .pathParam("scriptId", uploadedScriptId!!)
            .`when`()
            .delete("/v1/script-impact/scripts/{scriptId}")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))

        // 验证删除成功
        given()
            .pathParam("scriptId", uploadedScriptId!!)
            .`when`()
            .get("/v1/script-impact/scripts/{scriptId}")
            .then()
            .statusCode(404)

        println("✅ 清理完成: 脚本ID=$uploadedScriptId 已删除")
    }
} 