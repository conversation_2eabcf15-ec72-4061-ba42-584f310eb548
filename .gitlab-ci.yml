image: registry.minshenglife.com/infrastructure/cibase:vv3

stages:
  - build
  - release

build:
  stage: build
  script:
    - mvn clean install -U -DskipTests=true -s settings.xml
    - docker_build

  only:
    - tags
    - master
    - uat
    - sit
    - stg
    - dev
    - /^release-.*$/
    - /^hotfix-.*$/

release:
  stage: release
  script:
    - chart_build
  only:
    - tags
    - master
    - uat
    - sit
    - stg
    - dev
    - /^hotfix-.*$/
    - /^release-.*$/

.auto_devops: &auto_devops |
  echo "Debug: CHOERODON_URL=${CHOERODON_URL}"
  echo "Debug: Token=${Token}"
  echo "Debug: Full URL=${CHOERODON_URL}/devops/ci?token=${Token}&type=microservice"
  http_status_code=`curl -v -o .auto_devops.sh -s -m 10 --connect-timeout 10 -w %{http_code} "${CHOERODON_URL}/devops/ci?token=${Token}&type=microservice"`
  echo "Debug: HTTP Status Code=${http_status_code}"
  if [ "$http_status_code" != "200" ]; then
    cat .auto_devops.sh
    exit 1
  fi
  source .auto_devops.sh
  function docker_build(){
      docker login -u ${DOCKER_USER} -p ${DOCKER_PWD} ${DOCKER_REGISTRY}
      docker build -t ${DOCKER_REGISTRY}/${GROUP_NAME}/${PROJECT_NAME}:${CI_COMMIT_TAG} -f src/main/docker/Dockerfile .
      docker push ${DOCKER_REGISTRY}/${GROUP_NAME}/${PROJECT_NAME}:${CI_COMMIT_TAG}
  }

before_script:
  - *auto_devops
