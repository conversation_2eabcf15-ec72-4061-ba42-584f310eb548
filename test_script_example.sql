-- 示例SQL脚本：数据清洗和转换任务 (Example SQL Script: Data Cleaning and Transformation)
-- 用于测试脚本影响分析系统的上传功能

-- 创建临时表用于数据清洗
DROP TABLE IF EXISTS temp_user_cleaned;

CREATE TABLE temp_user_cleaned AS
SELECT 
    user_id,
    TRIM(UPPER(user_name)) as clean_user_name,
    email,
    registration_date,
    CASE 
        WHEN age BETWEEN 18 AND 65 THEN age 
        ELSE NULL 
    END as valid_age,
    status
FROM raw_user_data
WHERE email IS NOT NULL 
  AND email LIKE '%@%'
  AND registration_date >= '2023-01-01';

-- 更新主用户表
INSERT INTO cleaned_users (
    user_id, 
    user_name, 
    email, 
    registration_date, 
    age, 
    status,
    last_updated
)
SELECT 
    user_id,
    clean_user_name,
    email,
    registration_date,
    valid_age,
    status,
    CURRENT_TIMESTAMP
FROM temp_user_cleaned
ON DUPLICATE KEY UPDATE
    user_name = VALUES(user_name),
    age = VALUES(age),
    last_updated = VALUES(last_updated);

-- 生成用户统计报表
INSERT INTO user_statistics (
    stat_date,
    total_users,
    active_users,
    avg_age,
    created_at
)
SELECT 
    CURRENT_DATE as stat_date,
    COUNT(*) as total_users,
    COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as active_users,
    AVG(age) as avg_age,
    CURRENT_TIMESTAMP as created_at
FROM cleaned_users
WHERE registration_date >= CURRENT_DATE - INTERVAL 30 DAY;

-- 清理临时表
DROP TABLE temp_user_cleaned; 