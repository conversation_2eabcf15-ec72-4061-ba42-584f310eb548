# TODO: 解析内部数据交互平台 (Feature: Parse Internal Data Interaction Platform)

## 1. 项目设置与准备 (Project Setup & Preparation)
- [x] 任务1.1: 为此特性定义清晰的项目模块结构 (Define a clear project module structure for this feature).
- [x] 任务1.2: 调研并引入必要的依赖库 (Identify and add necessary dependencies) (例如：SQL解析库 (SQL parser library), HTTP客户端库 (HTTP client library) - 若需对接真实API (real API)).

## 2. 数据模型定义 (Data Model Definition)
- [x] 任务2.1: 根据提供的示例，定义内部作业 (Internal Job) 的程序语言对应的数据结构 (Define data structure in your programming language for an 'Internal Job' based on the provided example).
- [x] 任务2.2: 定义血缘关系 (Lineage) 的输出数据结构 (Define data structure for the output lineage information)，应包含源/目标表 (source/target tables)、列映射 (column mappings) 等.

## 3. 数据获取模块 (Data Acquisition Module)
- [x] 任务3.1: 实现一个模拟的作业API客户端 (Implement a mock API client for fetching job data)，用于开发和测试 (for development and testing).
    - [x] 任务3.1.1: 使用提供的JSON示例数据作为模拟API的固定返回值 (Use the provided JSON sample job data as the fixed response for the mock API).
- [x] 任务3.2: (未来扩展 - Future Extension) 设计与真实作业平台API (real job platform API) 对接的客户端接口 (client interface).

## 4. SQL提取与预处理模块 (SQL Extraction and Preprocessing Module)
- [x] 任务4.1: 从作业数据中准确提取 `reader_sql` 字段 (Accurately extract the `reader_sql` field from job data).
- [x] 任务4.2: (可选 - Optional) 实现SQL预处理逻辑 (Implement SQL preprocessing logic if needed) (例如：去除注释 (removing comments), 标准化SQL格式 (standardizing SQL format)).

## 5. SQL解析模块 (SQL Parsing Module)
- [x] 任务5.1: 调研并选择一个合适的SQL解析库 (Research and select a suitable SQL parsing library). (I pick jsqlparser)
    - [x] 任务5.1.1: 评估所选库对作业中SQL方言 (SQL dialect) (例如：示例中的MySQL) 的支持程度和健壮性 (robustness). (there's no need to consider other SQL dialects)
- [x] 任务5.2: 基于所选库实现SQL解析逻辑 (Implement SQL parsing logic using the chosen library).
    - [x] 任务5.2.1: 从 `reader_sql` 中解析出源表名 (source table names).
    - [x] 任务5.2.2: 从 `reader_sql` 中解析出查询的列名 (selected column names).
    - [x] 任务5.2.3: (可选 - Optional) 解析JOIN条件 (Parse JOIN conditions) 以支持更复杂的血缘分析.
    - [x] 任务5.2.4: (可选 - Optional) 解析WHERE子句 (Parse WHERE clauses) 以便理解数据过滤逻辑.

## 6. 血缘构建模块 (Lineage Construction Module)
- [x] 任务6.1: 确定读取方 (Reader) 的源表信息 (Determine reader source table information).
    - [x] 任务6.1.1: 优先使用作业数据中的 `reader_table_name` 字段 (Prioritize using the `reader_table_name` field from job data).
    - [x] 任务6.1.2: 若 `reader_table_name` 不明确或 `reader_sql` 涉及多表/复杂查询，则依赖SQL解析结果 (If `reader_table_name` is unclear or `reader_sql` involves multiple tables/complex queries, rely on SQL parsing results).
- [x] 任务6.2: 根据作业数据中的 `writer_table_name` 字段确定写入方 (Writer) 的目标表信息 (Determine writer target table information using the `writer_table_name` field from job data).
- [x] 任务6.3: 构建表级血缘关系 (Construct table-level lineage): Source Table(s) -> Target Table.
- [x] 任务6.4: 构建列级血缘关系 (Construct column-level lineage).
    - [x] 任务6.4.1: 主要利用作业数据中的 `columns` 数组 (array) 进行精确的列映射 (Primarily utilize the `columns` array from job data for precise column mapping) (`srcColumnName` -> `dstColumnName`).
    - [x] 任务6.4.2: (高级功能 - Advanced Feature) 当 `columns` 信息不足或需要分析SQL内转换时，尝试从 `reader_sql` 的解析结果中推断列级别操作和映射 (When `cloumns` info is insufficient or SQL internal transformations need analysis, attempt to infer column-level operations and mappings from parsed `reader_sql`).
- [x] 任务6.5: 整合 `db_r` 和 `db_w` 中的数据库连接信息 (database connection info) (例如：数据库类型 (DB type), 主机 (host), 库名 (database name)) 以丰富血缘上下文 (enrich lineage context).

## 7. 输出与存储 (Output and Storage)
- [x] 任务7.1: 设计血缘信息的存储格式或模型 (Design storage format or model for lineage information) (例如：JSON文件 (JSON files), 关系型数据库表 (relational DB tables), 图数据库节点/边 (graph DB nodes/edges)).
- [x] 任务7.2: 实现将构建好的血缘信息持久化 (persist) 或输出 (output) 到所选格式.

## 8. 错误处理与日志记录 (Error Handling and Logging)
- [ ] 任务8.1: 在数据获取、SQL解析、血缘构建等关键步骤加入全面的错误处理机制 (Implement comprehensive error handling for key steps like data acquisition, SQL parsing, lineage construction).
- [ ] 任务8.2: 集成日志框架 (Integrate a logging framework) 并实现详细的日志记录 (detailed logging) 以便于调试 (debugging) 和监控 (monitoring).

## 9. 测试 (Testing)
- [ ] 任务9.1: 编写单元测试 (Unit Tests) 覆盖核心逻辑:
    - [ ] SQL提取逻辑 (SQL extraction logic).
    - [ ] SQL解析器对不同SQL语句的处理 (SQL parser's handling of various SQL statements).
    - [ ] 血缘构建逻辑 (Lineage construction logic)，特别是列映射 (column mapping) 和表关系确定 (table relationship determination).
- [ ] 任务9.2: 编写集成测试 (Integration Tests) 使用模拟作业数据 (mock job data) 验证端到端 (end-to-end) 流程的正确性.

## 10. 文档 (Documentation)
- [ ] 任务10.1: 编写模块/组件的设计文档 (Write design documents for modules/components).
- [ ] 任务10.2: (可选 - Optional) 编写简要的用户指南 (Write a brief user guide) 说明如何运行此功能及解读其输出.

## 11. (未来考虑 - Future Considerations)
- [ ] 任务11.1: 设计方案以支持作业配置的增量更新 (Design a solution to support incremental updates of job configurations).
- [ ] 任务11.2: 考虑如何将此解析任务纳入调度系统 (Consider integrating this parsing task into a scheduling system).