package com.datayes.util

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.ValueSource

@DisplayName("TableNameNormalizer 单元测试")
class TableNameNormalizerTest {

    @Nested
    @DisplayName("normalizeTableName() 方法测试")
    inner class NormalizeTableNameTest {

        @Test
        @DisplayName("应该移除双引号包装")
        fun shouldRemoveDoubleQuotes() {
            assertEquals("table_name", TableNameNormalizer.normalizeTableName("\"table_name\""))
            assertEquals("user_data", TableNameNormalizer.normalizeTableName("\"user_data\""))
            assertEquals("gl_balances", TableNameNormalizer.normalizeTableName("\"gl_balances\""))
        }

        @Test
        @DisplayName("应该移除反引号包装")
        fun shouldRemoveBackticks() {
            assertEquals("table_name", TableNameNormalizer.normalizeTableName("`table_name`"))
            assertEquals("loloan", TableNameNormalizer.normalizeTableName("`loloan`"))
            assertEquals("user_account", TableNameNormalizer.normalizeTableName("`user_account`"))
        }

        @Test
        @DisplayName("应该移除单引号包装")
        fun shouldRemoveSingleQuotes() {
            assertEquals("table_name", TableNameNormalizer.normalizeTableName("'table_name'"))
            assertEquals("customer_info", TableNameNormalizer.normalizeTableName("'customer_info'"))
        }

        @Test
        @DisplayName("应该移除中括号包装 (SQL Server风格)")
        fun shouldRemoveSquareBrackets() {
            assertEquals("table_name", TableNameNormalizer.normalizeTableName("[table_name]"))
            assertEquals("dbo.users", TableNameNormalizer.normalizeTableName("[dbo.users]"))
            assertEquals("schema.table", TableNameNormalizer.normalizeTableName("[schema.table]"))
        }

        @Test
        @DisplayName("不应该修改未包装的表名")
        fun shouldNotModifyUnwrappedNames() {
            assertEquals("normal_table", TableNameNormalizer.normalizeTableName("normal_table"))
            assertEquals("table_123", TableNameNormalizer.normalizeTableName("table_123"))
            assertEquals("my_schema.my_table", TableNameNormalizer.normalizeTableName("my_schema.my_table"))
            assertEquals("TABLE_NAME", TableNameNormalizer.normalizeTableName("TABLE_NAME"))
        }

        @ParameterizedTest(name = "输入: {0} -> 期望: {1}")
        @CsvSource(
            value = [
                "\"users\", users",
                "`customers`, customers",
                "'orders', orders",
                "[products], products",
                "normal_table, normal_table",
                "schema.table, schema.table"
            ]
        )
        @DisplayName("参数化测试：各种包装类型")
        fun shouldNormalizeVariousWrappingTypes(input: String, expected: String) {
            assertEquals(expected, TableNameNormalizer.normalizeTableName(input))
        }

        @Test
        @DisplayName("应该处理嵌套内容")
        fun shouldHandleNestedContent() {
            assertEquals("table.with.dots", TableNameNormalizer.normalizeTableName("\"table.with.dots\""))
            assertEquals("table_with_spaces", TableNameNormalizer.normalizeTableName("`table_with_spaces`"))
            assertEquals("complex-name_123", TableNameNormalizer.normalizeTableName("'complex-name_123'"))
        }

        @Test
        @DisplayName("应该处理包含特殊字符的表名")
        fun shouldHandleSpecialCharacters() {
            assertEquals("table\$name", TableNameNormalizer.normalizeTableName("\"table\$name\""))
            assertEquals("table@host", TableNameNormalizer.normalizeTableName("`table@host`"))
            assertEquals("table#temp", TableNameNormalizer.normalizeTableName("'table#temp'"))
        }

        @Test
        @DisplayName("不应该处理不匹配的包装符号")
        fun shouldNotHandleMismatchedWrappers() {
            assertEquals("\"table`", TableNameNormalizer.normalizeTableName("\"table`"))
            assertEquals("`table\"", TableNameNormalizer.normalizeTableName("`table\""))
            assertEquals("'table]", TableNameNormalizer.normalizeTableName("'table]"))
            assertEquals("[table'", TableNameNormalizer.normalizeTableName("[table'"))
        }

        @Test
        @DisplayName("应该处理null和空值")
        fun shouldHandleNullAndEmptyValues() {
            assertNull(TableNameNormalizer.normalizeTableName(null))
            assertEquals("", TableNameNormalizer.normalizeTableName(""))
            assertEquals("  ", TableNameNormalizer.normalizeTableName("  "))
        }

        @Test
        @DisplayName("应该处理单字符输入")
        fun shouldHandleSingleCharacterInput() {
            assertEquals("a", TableNameNormalizer.normalizeTableName("a"))
            assertEquals("\"", TableNameNormalizer.normalizeTableName("\""))
            assertEquals("`", TableNameNormalizer.normalizeTableName("`"))
        }

        @Test
        @DisplayName("应该处理空包装")
        fun shouldHandleEmptyWrapping() {
            assertEquals("", TableNameNormalizer.normalizeTableName("\"\""))
            assertEquals("", TableNameNormalizer.normalizeTableName("``"))
            assertEquals("", TableNameNormalizer.normalizeTableName("''"))
            assertEquals("", TableNameNormalizer.normalizeTableName("[]"))
        }

        @Test
        @DisplayName("应该修剪空白字符")
        fun shouldTrimWhitespace() {
            assertEquals("table_name", TableNameNormalizer.normalizeTableName("  \"table_name\"  "))
            assertEquals("table_name", TableNameNormalizer.normalizeTableName("\t`table_name`\n"))
            assertEquals("normal_table", TableNameNormalizer.normalizeTableName("  normal_table  "))
        }
    }

    @Nested
    @DisplayName("isTableNameWrapped() 方法测试")
    inner class IsTableNameWrappedTest {

        @ParameterizedTest
        @ValueSource(strings = ["\"table\"", "`table`", "'table'", "[table]"])
        @DisplayName("应该识别包装的表名")
        fun shouldIdentifyWrappedNames(tableName: String) {
            assertTrue(TableNameNormalizer.isTableNameWrapped(tableName))
        }

        @ParameterizedTest
        @ValueSource(strings = ["table", "schema.table", "TABLE_123", "table-name"])
        @DisplayName("应该识别未包装的表名")
        fun shouldIdentifyUnwrappedNames(tableName: String) {
            assertFalse(TableNameNormalizer.isTableNameWrapped(tableName))
        }

        @ParameterizedTest
        @ValueSource(strings = ["\"table`", "`table\"", "'table]", "[table'", "\"", "`", "a"])
        @DisplayName("应该正确处理无效包装")
        fun shouldHandleInvalidWrapping(tableName: String) {
            assertFalse(TableNameNormalizer.isTableNameWrapped(tableName))
        }

        @Test
        @DisplayName("应该处理null和空值")
        fun shouldHandleNullAndEmpty() {
            assertFalse(TableNameNormalizer.isTableNameWrapped(null))
            assertFalse(TableNameNormalizer.isTableNameWrapped(""))
            assertFalse(TableNameNormalizer.isTableNameWrapped("  "))
        }
    }

    @Nested
    @DisplayName("getWrappingType() 方法测试")
    inner class GetWrappingTypeTest {

        @Test
        @DisplayName("应该正确识别包装类型")
        fun shouldIdentifyWrappingTypes() {
            assertEquals("DOUBLE_QUOTE", TableNameNormalizer.getWrappingType("\"table\""))
            assertEquals("BACKTICK", TableNameNormalizer.getWrappingType("`table`"))
            assertEquals("SINGLE_QUOTE", TableNameNormalizer.getWrappingType("'table'"))
            assertEquals("SQUARE_BRACKET", TableNameNormalizer.getWrappingType("[table]"))
        }

        @Test
        @DisplayName("应该为未包装的表名返回null")
        fun shouldReturnNullForUnwrappedNames() {
            assertNull(TableNameNormalizer.getWrappingType("table"))
            assertNull(TableNameNormalizer.getWrappingType("schema.table"))
            assertNull(TableNameNormalizer.getWrappingType("TABLE_123"))
        }

        @Test
        @DisplayName("应该为无效包装返回null")
        fun shouldReturnNullForInvalidWrapping() {
            assertNull(TableNameNormalizer.getWrappingType("\"table`"))
            assertNull(TableNameNormalizer.getWrappingType("`table\""))
            assertNull(TableNameNormalizer.getWrappingType("'table]"))
            assertNull(TableNameNormalizer.getWrappingType("[table'"))
        }

        @Test
        @DisplayName("应该处理null和空值")
        fun shouldHandleNullAndEmptyForWrappingType() {
            assertNull(TableNameNormalizer.getWrappingType(null))
            assertNull(TableNameNormalizer.getWrappingType(""))
            assertNull(TableNameNormalizer.getWrappingType("  "))
        }
    }

    @Nested
    @DisplayName("normalizeTableNames() 批量方法测试")
    inner class NormalizeTableNamesBatchTest {

        @Test
        @DisplayName("应该批量规范化表名")
        fun shouldNormalizeBatch() {
            val input = listOf("\"users\"", "`customers`", "'orders'", "[products]", "normal_table")
            val expected = listOf("users", "customers", "orders", "products", "normal_table")
            val result = TableNameNormalizer.normalizeTableNames(input)
            assertEquals(expected, result)
        }

        @Test
        @DisplayName("应该处理空列表")
        fun shouldHandleEmptyList() {
            val result = TableNameNormalizer.normalizeTableNames(emptyList())
            assertTrue(result.isEmpty())
        }

        @Test
        @DisplayName("应该处理混合包装类型")
        fun shouldHandleMixedWrappingTypes() {
            val input = listOf(
                "\"double_quoted\"",
                "`backticked`",
                "'single_quoted'",
                "[square_bracketed]",
                "unwrapped",
                "\"table.with.schema\"",
                "  `trimmed`  "
            )
            val expected = listOf(
                "double_quoted",
                "backticked",
                "single_quoted",
                "square_bracketed",
                "unwrapped",
                "table.with.schema",
                "trimmed"
            )
            val result = TableNameNormalizer.normalizeTableNames(input)
            assertEquals(expected, result)
        }
    }

    @Nested
    @DisplayName("getWrappingStatistics() 统计方法测试")
    inner class GetWrappingStatisticsTest {

        @Test
        @DisplayName("应该正确计算包装统计信息")
        fun shouldCalculateWrappingStatistics() {
            val tableNames = listOf(
                "\"table1\"",           // DOUBLE_QUOTE
                "\"table2\"",           // DOUBLE_QUOTE
                "`table3`",             // BACKTICK
                "`table4`",             // BACKTICK
                "`table5`",             // BACKTICK
                "'table6'",             // SINGLE_QUOTE
                "[table7]",             // SQUARE_BRACKET
                "table8",               // UNWRAPPED
                "table9",               // UNWRAPPED
                "table10"               // UNWRAPPED
            )

            val stats = TableNameNormalizer.getWrappingStatistics(tableNames)

            assertEquals(10, stats.total)
            assertEquals(2, stats.doubleQuoteWrapped)
            assertEquals(3, stats.backtickWrapped)
            assertEquals(1, stats.singleQuoteWrapped)
            assertEquals(1, stats.squareBracketWrapped)
            assertEquals(3, stats.unwrapped)
            assertEquals(7, stats.totalWrapped)
            assertEquals(70.0, stats.wrappingPercentage, 0.1)
        }

        @Test
        @DisplayName("应该处理空列表统计")
        fun shouldHandleEmptyListStatistics() {
            val stats = TableNameNormalizer.getWrappingStatistics(emptyList())

            assertEquals(0, stats.total)
            assertEquals(0, stats.doubleQuoteWrapped)
            assertEquals(0, stats.backtickWrapped)
            assertEquals(0, stats.singleQuoteWrapped)
            assertEquals(0, stats.squareBracketWrapped)
            assertEquals(0, stats.unwrapped)
            assertEquals(0, stats.totalWrapped)
            assertEquals(0.0, stats.wrappingPercentage)
        }

        @Test
        @DisplayName("应该处理全部未包装的统计")
        fun shouldHandleAllUnwrappedStatistics() {
            val tableNames = listOf("table1", "table2", "table3")
            val stats = TableNameNormalizer.getWrappingStatistics(tableNames)

            assertEquals(3, stats.total)
            assertEquals(0, stats.doubleQuoteWrapped)
            assertEquals(0, stats.backtickWrapped)
            assertEquals(0, stats.singleQuoteWrapped)
            assertEquals(0, stats.squareBracketWrapped)
            assertEquals(3, stats.unwrapped)
            assertEquals(0, stats.totalWrapped)
            assertEquals(0.0, stats.wrappingPercentage)
        }

        @Test
        @DisplayName("应该处理全部包装的统计")
        fun shouldHandleAllWrappedStatistics() {
            val tableNames = listOf("\"table1\"", "`table2`", "'table3'", "[table4]")
            val stats = TableNameNormalizer.getWrappingStatistics(tableNames)

            assertEquals(4, stats.total)
            assertEquals(1, stats.doubleQuoteWrapped)
            assertEquals(1, stats.backtickWrapped)
            assertEquals(1, stats.singleQuoteWrapped)
            assertEquals(1, stats.squareBracketWrapped)
            assertEquals(0, stats.unwrapped)
            assertEquals(4, stats.totalWrapped)
            assertEquals(100.0, stats.wrappingPercentage)
        }
    }

    @Nested
    @DisplayName("边界条件和极端情况测试")
    inner class EdgeCasesTest {

        @Test
        @DisplayName("应该处理Unicode表名")
        fun shouldHandleUnicodeTableNames() {
            assertEquals("用户表", TableNameNormalizer.normalizeTableName("\"用户表\""))
            assertEquals("顧客情報", TableNameNormalizer.normalizeTableName("`顧客情報`"))
            assertEquals("사용자_테이블", TableNameNormalizer.normalizeTableName("'사용자_테이블'"))
        }

        @Test
        @DisplayName("应该处理包含引号的表名")
        fun shouldHandleTablesWithQuotesInside() {
            assertEquals("table\"name", TableNameNormalizer.normalizeTableName("`table\"name`"))
            assertEquals("table`name", TableNameNormalizer.normalizeTableName("\"table`name\""))
            assertEquals("table'name", TableNameNormalizer.normalizeTableName("[table'name]"))
        }

        @Test
        @DisplayName("应该处理长表名")
        fun shouldHandleLongTableNames() {
            val longName = "a".repeat(1000)
            val wrappedLongName = "\"$longName\""
            assertEquals(longName, TableNameNormalizer.normalizeTableName(wrappedLongName))
        }

        @Test
        @DisplayName("应该处理只包含空格的包装表名")
        fun shouldHandleWhitespaceOnlyWrappedNames() {
            assertEquals("   ", TableNameNormalizer.normalizeTableName("\"   \""))
            assertEquals("\t\n", TableNameNormalizer.normalizeTableName("`\t\n`"))
        }

        @Test
        @DisplayName("应该处理连续包装")
        fun shouldHandleConsecutiveWrapping() {
            // 这些情况下，只会移除最外层的包装
            assertEquals("\"table\"", TableNameNormalizer.normalizeTableName("'\"table\"'"))
            assertEquals("`table`", TableNameNormalizer.normalizeTableName("\"`table`\""))
        }
    }

    @Nested
    @DisplayName("性能和大数据集测试")
    inner class PerformanceTest {

        @Test
        @DisplayName("应该能处理大数据集")
        fun shouldHandleLargeDataset() {
            val largeDataset = (1..10000).map { i ->
                when (i % 5) {
                    0 -> "\"table_$i\""
                    1 -> "`table_$i`"
                    2 -> "'table_$i'"
                    3 -> "[table_$i]"
                    else -> "table_$i"
                }
            }

            val result = TableNameNormalizer.normalizeTableNames(largeDataset)
            assertEquals(10000, result.size)

            // 验证所有表名都已正确规范化
            result.forEachIndexed { index, tableName ->
                assertEquals("table_${index + 1}", tableName)
            }
        }

        @Test
        @DisplayName("统计方法应该能处理大数据集")
        fun shouldCalculateStatisticsForLargeDataset() {
            val largeDataset = (1..10000).map { i ->
                when (i % 5) {
                    0 -> "\"table_$i\""  // 2000个
                    1 -> "`table_$i`"    // 2000个
                    2 -> "'table_$i'"    // 2000个
                    3 -> "[table_$i]"    // 2000个
                    else -> "table_$i"   // 2000个
                }
            }

            val stats = TableNameNormalizer.getWrappingStatistics(largeDataset)

            assertEquals(10000, stats.total)
            assertEquals(2000, stats.doubleQuoteWrapped)
            assertEquals(2000, stats.backtickWrapped)
            assertEquals(2000, stats.singleQuoteWrapped)
            assertEquals(2000, stats.squareBracketWrapped)
            assertEquals(2000, stats.unwrapped)
            assertEquals(8000, stats.totalWrapped)
            assertEquals(80.0, stats.wrappingPercentage)
        }
    }
}