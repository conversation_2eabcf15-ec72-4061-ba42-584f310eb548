package com.datayes.integration

import io.restassured.RestAssured.given
import io.restassured.path.json.JsonPath
import org.assertj.core.api.Assertions.assertThat

/**
 * 手动血缘任务测试数据工具类 (Manual Lineage Task Test Data Utilities)
 *
 * 提供测试数据验证和查询的辅助方法
 * 遵循只读测试原则，不修改或删除现有数据
 */
object ManualLineageTestDataUtils {

    /**
     * 验证数据源是否存在 (Verify datasource exists)
     */
    fun verifyDatasourceExists(datasourceId: Long): Boolean {
        println("a1b2c3d4 | 验证数据源是否存在: datasourceId=$datasourceId")

        return try {
            val response = given()
                .pathParam("datasourceId", datasourceId)
                .`when`()
                .get("/v1/lineage/manual-tasks/datasource/{datasourceId}/statistics")
                .then()
                .statusCode(200)
                .extract()
                .response()

            val jsonPath = JsonPath.from(response.asString())
            val returnedDatasourceId = jsonPath.getLong("datasourceId")
            
            val exists = returnedDatasourceId == datasourceId
            println("e5f6g7h8 | 数据源验证结果: datasourceId=$datasourceId, exists=$exists")
            exists
        } catch (e: Exception) {
            println("i9j0k1l2 | 数据源验证失败: datasourceId=$datasourceId, error=${e.message}")
            false
        }
    }

    /**
     * 获取可用的测试数据源ID (Get available test datasource ID)
     */
    fun getAvailableTestDatasourceId(): Long? {
        println("m3n4o5p6 | 查找可用的测试数据源ID")

        // 常见的测试数据源ID列表
        val candidateIds = listOf(1L, 2L, 3L, 4L, 5L)

        for (id in candidateIds) {
            if (verifyDatasourceExists(id)) {
                println("q7r8s9t0 | 找到可用的数据源ID: $id")
                return id
            }
        }

        println("u1v2w3x4 | 警告：未找到可用的测试数据源ID")
        return null
    }

    /**
     * 验证任务UUID格式 (Validate task UUID format)
     */
    fun validateTaskUuidFormat(uuid: String): Boolean {
        val uuidPattern = "[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}"
        val isValid = uuid.matches(Regex(uuidPattern))
        
        assertThat(uuid)
            .`as`("Task UUID should match standard UUID format")
            .matches(uuidPattern)
            
        return isValid
    }

    /**
     * 验证任务状态有效性 (Validate task status validity)
     */
    fun validateTaskStatus(status: String): Boolean {
        val validStatuses = setOf("PENDING", "RUNNING", "SUCCESS", "FAILED", "CANCELLED")
        val isValid = status in validStatuses
        
        assertThat(status)
            .`as`("Task status should be one of the valid values")
            .isIn(validStatuses)
            
        return isValid
    }

    /**
     * 创建测试请求体 (Create test request body)
     */
    fun createTriggerRequest(datasourceId: Long, triggerUser: String = "integration-test-user"): Map<String, Any> {
        return mapOf(
            "datasourceId" to datasourceId,
            "triggerUser" to triggerUser
        )
    }

    /**
     * 验证任务响应结构 (Validate task response structure)
     */
    fun validateTaskResponse(responseJson: String): TaskValidationResult {
        val jsonPath = JsonPath.from(responseJson)
        
        return try {
            val taskUuid = jsonPath.getString("taskUuid")
            val datasourceId = jsonPath.getLong("datasourceId")
            val taskStatus = jsonPath.getString("taskStatus")
            val message = jsonPath.getString("message")
            val createdAt = jsonPath.getString("createdAt")

            // 验证必需字段
            assertThat(taskUuid).isNotNull()
            assertThat(datasourceId).isGreaterThan(0)
            assertThat(taskStatus).isNotNull()
            assertThat(message).isNotNull()
            assertThat(createdAt).isNotNull()

            // 验证格式
            validateTaskUuidFormat(taskUuid)
            validateTaskStatus(taskStatus)

            TaskValidationResult(
                isValid = true,
                taskUuid = taskUuid,
                datasourceId = datasourceId,
                taskStatus = taskStatus,
                message = message
            )
        } catch (e: Exception) {
            println("y5z6a7b8 | 任务响应验证失败: ${e.message}")
            TaskValidationResult(
                isValid = false,
                errorMessage = e.message
            )
        }
    }

    /**
     * 验证任务列表响应结构 (Validate task list response structure)
     */
    fun validateTaskListResponse(responseJson: String): TaskListValidationResult {
        val jsonPath = JsonPath.from(responseJson)
        
        return try {
            val tasks = jsonPath.getList<Map<String, Any>>("tasks")
            val totalElements = jsonPath.getLong("totalElements")
            val totalPages = jsonPath.getInt("totalPages")
            val currentPage = jsonPath.getInt("currentPage")
            val size = jsonPath.getInt("size")

            // 验证分页信息
            assertThat(totalElements).isGreaterThanOrEqualTo(0)
            assertThat(totalPages).isGreaterThanOrEqualTo(0)
            assertThat(currentPage).isGreaterThanOrEqualTo(0)
            assertThat(size).isGreaterThan(0)

            // 验证任务列表
            assertThat(tasks).isNotNull()
            
            // 验证每个任务的基本结构
            tasks.forEach { task ->
                assertThat(task).containsKeys("id", "taskUuid", "datasourceId", "taskStatus", "createdAt")
                validateTaskUuidFormat(task["taskUuid"] as String)
                validateTaskStatus(task["taskStatus"] as String)
            }

            TaskListValidationResult(
                isValid = true,
                taskCount = tasks.size,
                totalElements = totalElements,
                totalPages = totalPages,
                currentPage = currentPage,
                size = size
            )
        } catch (e: Exception) {
            println("c9d0e1f2 | 任务列表响应验证失败: ${e.message}")
            TaskListValidationResult(
                isValid = false,
                errorMessage = e.message
            )
        }
    }

    /**
     * 验证统计响应结构 (Validate statistics response structure)
     */
    fun validateStatisticsResponse(responseJson: String, expectedDatasourceId: Long): StatisticsValidationResult {
        val jsonPath = JsonPath.from(responseJson)
        
        return try {
            val datasourceId = jsonPath.getLong("datasourceId")
            val totalTasks = jsonPath.getInt("totalTasks")
            val successfulTasks = jsonPath.getInt("successfulTasks")
            val failedTasks = jsonPath.getInt("failedTasks")
            val runningTasks = jsonPath.getInt("runningTasks")
            val pendingTasks = jsonPath.getInt("pendingTasks")

            // 验证数据源ID匹配
            assertThat(datasourceId).isEqualTo(expectedDatasourceId)

            // 验证统计数据逻辑
            assertThat(totalTasks).isEqualTo(successfulTasks + failedTasks + runningTasks + pendingTasks)
            assertThat(totalTasks).isGreaterThanOrEqualTo(0)

            StatisticsValidationResult(
                isValid = true,
                datasourceId = datasourceId,
                totalTasks = totalTasks,
                successfulTasks = successfulTasks,
                failedTasks = failedTasks,
                runningTasks = runningTasks,
                pendingTasks = pendingTasks
            )
        } catch (e: Exception) {
            println("g3h4i5j6 | 统计响应验证失败: ${e.message}")
            StatisticsValidationResult(
                isValid = false,
                errorMessage = e.message
            )
        }
    }

    /**
     * 等待任务状态变化 (Wait for task status change)
     * 
     * 注意：这是一个辅助方法，用于等待异步任务完成
     */
    fun waitForTaskStatusChange(
        taskUuid: String,
        fromStatus: String,
        maxWaitTimeMs: Long = 30000L,
        pollIntervalMs: Long = 1000L
    ): String? {
        println("k7l8m9n0 | 等待任务状态变化: taskUuid=$taskUuid, fromStatus=$fromStatus")
        
        val startTime = System.currentTimeMillis()
        
        while (System.currentTimeMillis() - startTime < maxWaitTimeMs) {
            try {
                val response = given()
                    .pathParam("taskUuid", taskUuid)
                    .`when`()
                    .get("/v1/lineage/manual-tasks/{taskUuid}")
                    .then()
                    .statusCode(200)
                    .extract()
                    .response()

                val currentStatus = JsonPath.from(response.asString()).getString("taskStatus")
                
                if (currentStatus != fromStatus) {
                    println("o1p2q3r4 | 任务状态已变化: taskUuid=$taskUuid, newStatus=$currentStatus")
                    return currentStatus
                }
                
                Thread.sleep(pollIntervalMs)
            } catch (e: Exception) {
                println("s5t6u7v8 | 检查任务状态时发生错误: ${e.message}")
                Thread.sleep(pollIntervalMs)
            }
        }
        
        println("w9x0y1z2 | 等待任务状态变化超时: taskUuid=$taskUuid")
        return null
    }
}

/**
 * 任务验证结果 (Task Validation Result)
 */
data class TaskValidationResult(
    val isValid: Boolean,
    val taskUuid: String? = null,
    val datasourceId: Long? = null,
    val taskStatus: String? = null,
    val message: String? = null,
    val errorMessage: String? = null
)

/**
 * 任务列表验证结果 (Task List Validation Result)
 */
data class TaskListValidationResult(
    val isValid: Boolean,
    val taskCount: Int? = null,
    val totalElements: Long? = null,
    val totalPages: Int? = null,
    val currentPage: Int? = null,
    val size: Int? = null,
    val errorMessage: String? = null
)

/**
 * 统计验证结果 (Statistics Validation Result)
 */
data class StatisticsValidationResult(
    val isValid: Boolean,
    val datasourceId: Long? = null,
    val totalTasks: Int? = null,
    val successfulTasks: Int? = null,
    val failedTasks: Int? = null,
    val runningTasks: Int? = null,
    val pendingTasks: Int? = null,
    val errorMessage: String? = null
)