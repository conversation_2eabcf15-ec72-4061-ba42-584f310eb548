package com.datayes.util

import org.slf4j.LoggerFactory

/**
 * 表名规范化工具 (Table Name Normalizer Utility)
 * 
 * 用于处理被引号、反引号包围的表名，统一去除包装符号
 */
object TableNameNormalizer {
    
    private val logger = LoggerFactory.getLogger(TableNameNormalizer::class.java)
    
    /**
     * 规范化表名，去除包装的引号或反引号 (Normalize table name by removing wrapping quotes or backticks)
     * 
     * 支持的包装符号：
     * - 双引号: "table_name" -> table_name
     * - 反引号: `table_name` -> table_name
     * - 单引号: 'table_name' -> table_name
     * - 中括号: [table_name] -> table_name (SQL Server风格)
     * 
     * @param tableName 原始表名，可能包含包装符号
     * @return 规范化后的表名，去除包装符号
     */
    fun normalizeTableName(tableName: String?): String? {
        if (tableName.isNullOrBlank()) {
            return tableName
        }
        
        val trimmed = tableName.trim()
        if (trimmed.length < 2) {
            return trimmed
        }
        
        // 检查并移除各种包装符号
        val normalized = when {
            // 双引号包装
            trimmed.startsWith("\"") && trimmed.endsWith("\"") -> {
                val unwrapped = trimmed.substring(1, trimmed.length - 1)
                logger.debug("f2e9a7c4 | 移除双引号包装: \"$trimmed\" -> \"$unwrapped\"")
                unwrapped
            }
            // 反引号包装
            trimmed.startsWith("`") && trimmed.endsWith("`") -> {
                val unwrapped = trimmed.substring(1, trimmed.length - 1)
                logger.debug("b8d3f6a1 | 移除反引号包装: \"$trimmed\" -> \"$unwrapped\"")
                unwrapped
            }
            // 单引号包装
            trimmed.startsWith("'") && trimmed.endsWith("'") -> {
                val unwrapped = trimmed.substring(1, trimmed.length - 1)
                logger.debug("k5j9h2l6 | 移除单引号包装: \"$trimmed\" -> \"$unwrapped\"")
                unwrapped
            }
            // 中括号包装 (SQL Server风格)
            trimmed.startsWith("[") && trimmed.endsWith("]") -> {
                val unwrapped = trimmed.substring(1, trimmed.length - 1)
                logger.debug("p7q4r8s3 | 移除中括号包装: \"$trimmed\" -> \"$unwrapped\"")
                unwrapped
            }
            // 无包装，直接返回
            else -> {
                logger.trace("v9w1x5y2 | 表名无包装符号: \"$trimmed\"")
                trimmed
            }
        }
        
        return normalized
    }
    
    /**
     * 批量规范化表名 (Batch normalize table names)
     * 
     * @param tableNames 表名列表
     * @return 规范化后的表名列表
     */
    fun normalizeTableNames(tableNames: List<String>): List<String> {
        return tableNames.map { normalizeTableName(it) ?: it }
    }
    
    /**
     * 检查表名是否被包装 (Check if table name is wrapped)
     * 
     * @param tableName 表名
     * @return true 如果表名被引号或反引号包装
     */
    fun isTableNameWrapped(tableName: String?): Boolean {
        if (tableName.isNullOrBlank()) {
            return false
        }
        
        val trimmed = tableName.trim()
        if (trimmed.length < 2) {
            return false
        }
        
        return when {
            trimmed.startsWith("\"") && trimmed.endsWith("\"") -> true
            trimmed.startsWith("`") && trimmed.endsWith("`") -> true
            trimmed.startsWith("'") && trimmed.endsWith("'") -> true
            trimmed.startsWith("[") && trimmed.endsWith("]") -> true
            else -> false
        }
    }
    
    /**
     * 获取包装类型 (Get wrapping type)
     * 
     * @param tableName 表名
     * @return 包装类型字符串，如果无包装返回null
     */
    fun getWrappingType(tableName: String?): String? {
        if (tableName.isNullOrBlank()) {
            return null
        }
        
        val trimmed = tableName.trim()
        if (trimmed.length < 2) {
            return null
        }
        
        return when {
            trimmed.startsWith("\"") && trimmed.endsWith("\"") -> "DOUBLE_QUOTE"
            trimmed.startsWith("`") && trimmed.endsWith("`") -> "BACKTICK"
            trimmed.startsWith("'") && trimmed.endsWith("'") -> "SINGLE_QUOTE"
            trimmed.startsWith("[") && trimmed.endsWith("]") -> "SQUARE_BRACKET"
            else -> null
        }
    }
    
    /**
     * 统计包装符号使用情况 (Get wrapping statistics)
     * 
     * @param tableNames 表名列表
     * @return 包装符号统计结果
     */
    fun getWrappingStatistics(tableNames: List<String>): WrappingStatistics {
        var doubleQuoteCount = 0
        var backtickCount = 0
        var singleQuoteCount = 0
        var squareBracketCount = 0
        var unwrappedCount = 0
        
        for (tableName in tableNames) {
            when (getWrappingType(tableName)) {
                "DOUBLE_QUOTE" -> doubleQuoteCount++
                "BACKTICK" -> backtickCount++
                "SINGLE_QUOTE" -> singleQuoteCount++
                "SQUARE_BRACKET" -> squareBracketCount++
                else -> unwrappedCount++
            }
        }
        
        return WrappingStatistics(
            total = tableNames.size,
            doubleQuoteWrapped = doubleQuoteCount,
            backtickWrapped = backtickCount,
            singleQuoteWrapped = singleQuoteCount,
            squareBracketWrapped = squareBracketCount,
            unwrapped = unwrappedCount
        )
    }
}

/**
 * 表名包装统计信息 (Table Name Wrapping Statistics)
 */
data class WrappingStatistics(
    val total: Int,
    val doubleQuoteWrapped: Int,
    val backtickWrapped: Int,
    val singleQuoteWrapped: Int,
    val squareBracketWrapped: Int,
    val unwrapped: Int
) {
    /**
     * 计算包装表名的总数 (Calculate total wrapped count)
     */
    val totalWrapped: Int
        get() = doubleQuoteWrapped + backtickWrapped + singleQuoteWrapped + squareBracketWrapped
    
    /**
     * 计算包装比例 (Calculate wrapping percentage)
     */
    val wrappingPercentage: Double
        get() = if (total > 0) (totalWrapped.toDouble() / total) * 100 else 0.0
}