1.  **核心目标**：
    * 解析SQL查询字符串。
    * 提取查询中涉及的**表名** (table names)。
    * 提取查询中 `SELECT` 子句定义的**列名** (column names) 或列表达式。

2.  **SQL方言 (SQL Dialect)**：
    * 主要针对 **MySQL**。

3.  **要提取的表信息**：
    * 来源：`FROM` 子句和各种 `JOIN` 子句。
    * 包含内容：模式名 (schema name) (如果SQL中指定，例如 `APPS.CUX_FA_INTERFACE_TRANSFER` 中的 `APPS`)、表名本身以及表的别名 (alias) (如果SQL中指定，例如 `lis.lpaddress a` 中的 `a`)。

4.  **要提取的列信息**：
    * 来源：主 `SELECT` 子句。
    * 对于 `SELECT *`：输出一个特殊标记 (例如字符串 `*`)，您后续会自行处理具体列的解析。
    * 对于 `SELECT table_alias.*`：类似地输出标记，并能关联到表别名。
    * 对于普通列：提取列名 (例如 `ID`, `ASSET_NUMBER`)，如果列有表限定符 (table qualifier) (例如 `a.modifydate`)，也应能体现。
    * 对于列表达式和函数：提取原始表达式字符串 (例如 `COUNT(*)`)。
    * 列别名 (column alias)：提取列的别名 (例如 `c.name AS customer_name` 中的 `customer_name`)。

5.  **子查询 (Subqueries) 处理**：
    * 需要能够递归地从嵌套在 `FROM`、`WHERE` (例如 `EXISTS` 或 `IN` 子句中)、`JOIN` 条件等位置的子查询中提取表名。

6.  **输出数据结构**：
    * 使用 Kotlin 的数据类 (data classes) 来定义解析结果的结构 (例如，可以有 `ExtractedTable` 和 `ExtractedColumn` 这样的类)。
    * 设计应偏向数据导向 (data-oriented design)。

7.  **API 设计与封装**：
    * 提供一个清晰的 Kotlin 函数接口。
    * 底层的SQL解析库 (例如 JSqlParser) 应该是实现细节，不应暴露在公开API中。这意味着API的参数和返回类型应该是您自定义的 Kotlin 类型。

8.  **其他SQL子句**：
    * 函数需要能正确处理包含 `GROUP BY`, `ORDER BY`, `LIMIT`, `OFFSET` 等子句的SQL，即使这些子句本身不直接提供表名或列名信息供提取。

9.  **性能**：
    * 目前阶段，性能不是首要考虑因素。