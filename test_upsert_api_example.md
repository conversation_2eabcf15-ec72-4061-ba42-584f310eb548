# 手动血缘 Upsert API 测试示例

## 新的 API 端点

```
POST /api/manual-lineage/table-lineage-upsert
```

## 插入操作示例（Insert Example）

当 `relationshipId` 为 `null` 时，执行插入操作：

```json
{
  "relationshipId": null,
  
  "sourceDatasourceName": "production_mysql_01",
  "sourceDbType": "MYSQL",
  "sourceHost": "**********",
  "sourcePort": 3306,
  "sourceDatabaseName": "ecommerce",
  "sourceSchemaName": "public",
  "sourceTableName": "user_orders",
  "sourceTableType": "TABLE",
  "sourceChineseName": "用户订单表",
  "sourceDescription": "存储用户订单信息的主表",
  
  "targetDatasourceName": "warehouse_hive_cluster",
  "targetDbType": "HIVE",
  "targetHost": "hdfs-cluster.internal",
  "targetPort": 10000,
  "targetDatabaseName": "ods",
  "targetSchemaName": "ods_layer",
  "targetTableName": "ods_user_orders",
  "targetTableType": "EXTERNAL_TABLE",
  "targetChineseName": "ODS用户订单表",
  "targetDescription": "从业务系统同步的用户订单原始数据",
  
  "lineageType": "ETL_TRANSFORM",
  "lineageDescription": "从业务数据库ETL到数据仓库ODS层",
  "confidenceScore": 0.95,
  
  "columnMappings": [
    {
      "mappingId": null,
      "sourceColumnName": "order_id",
      "sourceDataType": "BIGINT",
      "sourceColumnComment": "订单唯一标识",
      "sourceIsPrimaryKey": true,
      "sourceIsNullable": false,
      "sourceColumnOrder": 1,
      
      "targetColumnName": "order_id",
      "targetDataType": "BIGINT",
      "targetColumnComment": "订单唯一标识",
      "targetIsPrimaryKey": false,
      "targetIsNullable": false,
      "targetColumnOrder": 1,
      
      "transformationType": "DIRECT_COPY",
      "transformationExpression": null,
      "transformationDescription": "直接复制，无转换",
      "columnConfidenceScore": 1.0,
      "action": "UPSERT"
    },
    {
      "mappingId": null,
      "sourceColumnName": "order_amount",
      "sourceDataType": "DECIMAL(10,2)",
      "sourceColumnComment": "订单金额",
      "sourceIsPrimaryKey": false,
      "sourceIsNullable": true,
      "sourceDefaultValue": "0.00",
      "sourceColumnOrder": 3,
      
      "targetColumnName": "order_total",
      "targetDataType": "DECIMAL(12,2)",
      "targetColumnComment": "订单总金额（含税）",
      "targetIsPrimaryKey": false,
      "targetIsNullable": true,
      "targetDefaultValue": "0.00",
      "targetColumnOrder": 3,
      
      "transformationType": "EXPRESSION",
      "transformationExpression": "order_amount * 1.1",
      "transformationDescription": "添加10%的税费计算",
      "columnConfidenceScore": 0.9,
      "action": "UPSERT"
    }
  ],
  
  "operatedBy": "data_engineer_alice"
}
```

## 更新操作示例（Update Example）

当 `relationshipId` 有值时，执行更新操作：

```json
{
  "relationshipId": 12345,
  
  "sourceDatasourceName": "production_mysql_01",
  "sourceDbType": "MYSQL",
  "sourceHost": "**********",
  "sourcePort": 3306,
  "sourceDatabaseName": "ecommerce",
  "sourceSchemaName": "public",
  "sourceTableName": "user_orders",
  "sourceTableType": "TABLE",
  "sourceChineseName": "用户订单表",
  "sourceDescription": "更新后的描述：存储用户订单信息的主表",
  
  "targetDatasourceName": "warehouse_hive_cluster",
  "targetDbType": "HIVE",
  "targetHost": "hdfs-cluster.internal",
  "targetPort": 10000,
  "targetDatabaseName": "ods",
  "targetSchemaName": "ods_layer",
  "targetTableName": "ods_user_orders",
  "targetTableType": "EXTERNAL_TABLE",
  "targetChineseName": "ODS用户订单表",
  "targetDescription": "更新后的描述：从业务系统同步的用户订单原始数据",
  
  "lineageType": "ETL_TRANSFORM",
  "lineageDescription": "更新后的描述：从业务数据库ETL到数据仓库ODS层",
  "confidenceScore": 0.98,
  
  "columnMappings": [
    {
      "mappingId": 67890,
      "sourceColumnName": "order_id",
      "sourceDataType": "BIGINT",
      "sourceColumnComment": "订单唯一标识",
      "sourceIsPrimaryKey": true,
      "sourceIsNullable": false,
      "sourceColumnOrder": 1,
      
      "targetColumnName": "order_id",
      "targetDataType": "BIGINT",
      "targetColumnComment": "订单唯一标识",
      "targetIsPrimaryKey": false,
      "targetIsNullable": false,
      "targetColumnOrder": 1,
      
      "transformationType": "DIRECT_COPY",
      "transformationExpression": null,
      "transformationDescription": "直接复制，无转换",
      "columnConfidenceScore": 1.0,
      "action": "UPSERT"
    },
    {
      "mappingId": null,
      "sourceColumnName": "created_at",
      "sourceDataType": "TIMESTAMP",
      "sourceColumnComment": "创建时间",
      "sourceIsPrimaryKey": false,
      "sourceIsNullable": false,
      "sourceColumnOrder": 4,
      
      "targetColumnName": "created_timestamp",
      "targetDataType": "TIMESTAMP",
      "targetColumnComment": "创建时间戳",
      "targetIsPrimaryKey": false,
      "targetIsNullable": false,
      "targetColumnOrder": 4,
      
      "transformationType": "DIRECT_COPY",
      "transformationExpression": null,
      "transformationDescription": "时间字段直接复制",
      "columnConfidenceScore": 1.0,
      "action": "UPSERT"
    },
    {
      "mappingId": 67891,
      "action": "DELETE"
    }
  ],
  
  "operatedBy": "data_engineer_bob"
}
```

## 响应格式

成功响应：
```json
{
  "success": true,
  "relationshipId": 12345,
  "affectedRows": 3,
  "message": "手动血缘关系创建成功",
  "errors": [],
  "warnings": []
}
```

失败响应：
```json
{
  "success": false,
  "relationshipId": null,
  "affectedRows": 0,
  "message": "验证失败",
  "errors": [
    "源数据源名称不能为空",
    "血缘类型不能为空"
  ],
  "warnings": []
}
```

## 使用 curl 测试

### 插入操作
```bash
curl -X POST \
  http://localhost:8080/api/manual-lineage/table-lineage-upsert \
  -H "Content-Type: application/json" \
  -d @insert_example.json
```

### 更新操作
```bash
curl -X POST \
  http://localhost:8080/api/manual-lineage/table-lineage-upsert \
  -H "Content-Type: application/json" \
  -d @update_example.json
```

## 关键特性

1. **统一接口**：一个端点支持插入和更新操作
2. **扁平化结构**：所有字段都在顶层，易于表单绑定
3. **完整数据管理**：支持数据源、表、列的完整定义
4. **灵活的列映射**：支持UPSERT、DELETE操作
5. **详细验证**：提供全面的参数验证和错误信息
6. **血缘增强**：包含转换规则和置信度评分

## 注意事项

1. `relationshipId` 为 `null` 时执行插入，有值时执行更新
2. `mappingId` 为 `null` 时创建新的列映射，有值时更新现有映射
3. 列映射的 `action` 字段支持 `UPSERT` 和 `DELETE`
4. 所有必填字段都有验证，会返回详细的错误信息
5. 目前 `ensureTableExists` 方法还需要完整实现 