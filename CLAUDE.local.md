- For every log message, prepend a unique short literal uuid followed by ' | ', e.g. '3610991a | <the actual message>', ensuring that each unique prefix is only used for one distinct log message to maintain traceability.
- Prioritize data-oriented programming, focusing first on data structures and their transformations.
- Adopt a functional core and imperative shell architecture: encapsulate business logic in pure functions and handle side effects with an imperative shell to improve code testability and maintainability.
- Prefer simple and clear solutions, avoiding unnecessary complexity.
- Only introduce abstraction when it significantly improves code quality; avoid over-abstraction.
- Write inherently testable code, minimizing reliance on mocks.
- Prefer immutable objects and data structures to enhance code robustness.
- Don't try to run tests yourself; I want to run them manually when I want to.
- In <PERSON><PERSON><PERSON>'s triple-quoted strings (NOT normal string), replace every `$` that should be output literally with `${'$'}`.
- Use modern JdbcTemplate API: replace `query(sql, params, rowMapper)` with `query(sql, rowMapper, *params)`.
- Avoid using the XxxDto suffix; use Command for modification operations (e.g., EditXxxCommand or UpdateXxxCommand) and Request for query or read-only operations (e.g., GetXxxRequest or QueryXxxRequest).

## Database Query Tool (db-query-tool.py)
Use for database inspection and debugging. Read-only operations only.

```bash
python db-query-tool.py --list-tables
python db-query-tool.py --describe TABLE_NAME
python db-query-tool.py --query "SELECT * FROM table LIMIT 10"
python db-query-tool.py --interactive
```