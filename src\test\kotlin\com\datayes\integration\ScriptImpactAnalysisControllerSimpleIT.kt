package com.datayes.integration

import io.restassured.RestAssured
import io.restassured.RestAssured.given
import io.restassured.http.ContentType
import io.restassured.path.json.JsonPath
import org.assertj.core.api.Assertions.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.server.LocalServerPort

/**
 * 脚本影响分析控制器简化端到端测试 (Script Impact Analysis Controller Simple End-to-End Test)
 *
 * 这是一个简化版本的端到端测试，专注于测试核心功能：
 * - 脚本文件上传 (Script File Upload)
 * - 脚本列表查询 (Script List Query)
 * - 脚本详情查询 (Script Details Query)
 * - 脚本文件下载 (Script File Download)
 * - 脚本删除 (Script Deletion)
 *
 * 前提条件 (Prerequisites):
 * - 应用程序必须已经启动并运行在配置的端口上
 * - 数据库连接正常
 *
 * 使用方法 (Usage):
 * 1. 启动应用程序：./mvnw spring-boot:run
 * 2. 运行测试：./mvnw test -Dtest=ScriptImpactAnalysisControllerSimpleIT
 */
@DisplayName("脚本影响分析控制器简化端到端测试")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class ScriptImpactAnalysisControllerSimpleIT {

    companion object {
        private const val TEST_UPLOAD_USER = "simple-test-user"
    }

    @LocalServerPort
    private var port: Int = 0

    @BeforeEach
    fun setupRestAssured() {
        val host = System.getProperty("test.api.host") ?: System.getenv("TEST_API_HOST") ?: "localhost"
        RestAssured.baseURI = "http://$host"
        RestAssured.port = port
        RestAssured.basePath = "/api"
    }

    private fun uploadTestScript(userName: String, scriptFileName: String, scriptContent: String): Pair<Long, String> {
        val tempFile = TestFileUtils.createTempSqlFile(scriptFileName, scriptContent)
        try {
            val response = given()
                .contentType(ContentType.MULTIPART)
                .multiPart("file", tempFile, "application/octet-stream")
                .multiPart("uploadUser", userName)
                .`when`()
                .post("/v1/script-impact/upload")
                .then()
                .statusCode(200) // Now all responses return 200
                .contentType(ContentType.JSON)
                .extract()
                .response()

            val jsonPath = JsonPath.from(response.asString())
            
            // Handle both success and conflict responses based on success field
            val scriptId = if (jsonPath.getBoolean("success")) {
                // New upload successful
                jsonPath.getLong("data.scriptId")
            } else {
                // File already exists, get the existing script ID from conflict response
                jsonPath.getLong("data.scriptId") // Conflict response should include existing script data
            }
            
            val scriptName = jsonPath.getString("data.scriptName")
            return Pair(scriptId, scriptName)
        } finally {
            tempFile.delete()
        }
    }

    private fun deleteTestScript(scriptId: Long) {
        given()
            .pathParam("scriptId", scriptId)
            .`when`()
            .delete("/v1/script-impact/scripts/{scriptId}")
            .then()
            .statusCode(anyOf(equalTo(200), equalTo(404))) // Tolerate if already deleted
    }

    @Test
    @DisplayName("应该成功上传SQL脚本文件")
    fun `should successfully upload SQL script file`() {
        var scriptId: Long? = null
        try {
            val uniqueContent = "-- Upload test ${System.currentTimeMillis()}\\n${TestFileUtils.SAMPLE_SQL_CONTENT}"
            val (id, _) = uploadTestScript(
                TEST_UPLOAD_USER,
                "upload_test.sql",
                uniqueContent
            )
            scriptId = id
            assertThat(scriptId).isNotNull()
        } finally {
            scriptId?.let { deleteTestScript(it) }
        }
    }

    @Test
    @DisplayName("上传空的用户名应该被拒绝")
    fun `should reject upload with empty user name`() {
        val tempFile = TestFileUtils.createTempSqlFile("empty_user.sql", "SELECT 1")
        try {
            given()
                .contentType(ContentType.MULTIPART)
                .multiPart("file", tempFile, "application/octet-stream")
                .multiPart("uploadUser", "")
                .`when`()
                .post("/v1/script-impact/upload")
                .then()
                .statusCode(400) // Expect a client error
        } finally {
            tempFile.delete()
        }
    }

    @Test
    @DisplayName("应该成功查询脚本列表")
    fun `should successfully query script list`() {
        val uniqueContent = "-- Query test ${System.currentTimeMillis()}\\nSELECT 1"
        val (scriptId, scriptName) = uploadTestScript(TEST_UPLOAD_USER, "query_test.sql", uniqueContent)
        try {
            val listResponse = given()
                .`when`()
                .get("/v1/script-impact/scripts")
                .then()
                .statusCode(200)
                .contentType(ContentType.JSON)
                .body("success", equalTo(true))
                .body("data.content", not(emptyList<Any>()))
                .extract()
                .response()
                
            // Verify our script is in the list
            val jsonPath = JsonPath.from(listResponse.asString())
            val scripts = jsonPath.getList<Map<String, Any>>("data.content")
            val ourScript = scripts.find { (it["id"] as Int).toLong() == scriptId }
            assertThat(ourScript).isNotNull()
            assertThat(ourScript!!["scriptName"]).isEqualTo(scriptName)
        } finally {
            deleteTestScript(scriptId)
        }
    }

    @Test
    @DisplayName("应该支持按用户名过滤")
    fun `should support filtering by user name`() {
        val userA = "filter-user-a"
        val userB = "filter-user-b"
        val contentA = "-- Filter test A ${System.currentTimeMillis()}\\nSELECT 1"
        val contentB = "-- Filter test B ${System.currentTimeMillis()}\\nSELECT 2"
        val (scriptIdA, _) = uploadTestScript(userA, "filter_a.sql", contentA)
        val (scriptIdB, _) = uploadTestScript(userB, "filter_b.sql", contentB)

        try {
            given()
                .queryParam("uploadUser", userA)
                .`when`()
                .get("/v1/script-impact/scripts")
                .then()
                .statusCode(200)
                .body("data.content.size()", equalTo(1))
                .body("data.content[0].uploadUser", equalTo(userA))
        } finally {
            deleteTestScript(scriptIdA)
            deleteTestScript(scriptIdB)
        }
    }

    @Test
    @DisplayName("应该支持按脚本类型过滤")
    fun `should support filtering by script type`() {
        // This test assumes the upload process correctly identifies the script type as SQL.
        val uniqueContent = "-- Type filter test ${System.currentTimeMillis()}\\nSELECT 1"
        val (scriptId, _) = uploadTestScript(TEST_UPLOAD_USER, "type_filter.sql", uniqueContent)
        try {
            given()
                .queryParam("scriptType", "SQL")
                .`when`()
                .get("/v1/script-impact/scripts")
                .then()
                .statusCode(200)
                .body("data.content", not(emptyList<Any>()))
                .body("data.content.uploadUser", everyItem(notNullValue()))
                .body("data.content.scriptType", everyItem(equalTo("SQL")))
        } finally {
            deleteTestScript(scriptId)
        }
    }

    @Test
    @DisplayName("应该成功获取脚本详情")
    fun `should successfully get script details`() {
        val uniqueContent = "-- Details test ${System.currentTimeMillis()}\\nSELECT 1"
        val (scriptId, scriptName) = uploadTestScript(TEST_UPLOAD_USER, "details_test.sql", uniqueContent)
        try {
            given()
                .pathParam("scriptId", scriptId)
                .`when`()
                .get("/v1/script-impact/scripts/{scriptId}")
                .then()
                .statusCode(200)
                .contentType(ContentType.JSON)
                .body("success", equalTo(true))
                .body("data.id", equalTo(scriptId.toInt()))
                .body("data.scriptName", equalTo(scriptName))
                .body("data.uploadUser", equalTo(TEST_UPLOAD_USER))
        } finally {
            deleteTestScript(scriptId)
        }
    }

    @Test
    @DisplayName("获取不存在的脚本详情应返回404")
    fun `should return 404 when getting non-existent script details`() {
        val nonExistentId = 999999L
        given()
            .pathParam("scriptId", nonExistentId)
            .`when`()
            .get("/v1/script-impact/scripts/{scriptId}")
            .then()
            .statusCode(404)
            .body("success", equalTo(false))
            .body("message", containsString("不存在"))
    }

    @Test
    @DisplayName("应该成功下载脚本文件")
    fun `should successfully download script file`() {
        val scriptContent = "-- Download test ${System.currentTimeMillis()}\\nSELECT * FROM fact_sales WHERE report_date = '2023-01-01'"
        val (scriptId, scriptName) = uploadTestScript(TEST_UPLOAD_USER, "download_test.sql", scriptContent)
        try {
            val downloadedContent = given()
                .pathParam("scriptId", scriptId)
                .`when`()
                .get("/v1/script-impact/scripts/{scriptId}/download")
                .then()
                .statusCode(200)
                .header("Content-Disposition", containsString("attachment; filename=\"$scriptName\""))
                .header("Content-Type", anyOf(equalTo("application/sql"), containsString("application"), containsString("octet-stream")))
                .extract()
                .asString()

            assertThat(downloadedContent).isEqualTo(scriptContent)
        } finally {
            deleteTestScript(scriptId)
        }
    }

    @Test
    @DisplayName("下载不存在的脚本应返回404")
    fun `should return 404 when downloading non-existent script`() {
        val nonExistentId = 999999L
        given()
            .pathParam("scriptId", nonExistentId)
            .`when`()
            .get("/v1/script-impact/scripts/{scriptId}/download")
            .then()
            .statusCode(404)
    }

    @Test
    @DisplayName("应该成功删除脚本")
    fun `should successfully delete script`() {
        val uniqueContent = "-- Delete test ${System.currentTimeMillis()}\\nSELECT 1"
        val (scriptId, _) = uploadTestScript(TEST_UPLOAD_USER, "delete_test.sql", uniqueContent)

        // Perform deletion
        given()
            .pathParam("scriptId", scriptId)
            .`when`()
            .delete("/v1/script-impact/scripts/{scriptId}")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("message", containsString("删除成功"))

        // Verify the script is deleted
        given()
            .pathParam("scriptId", scriptId)
            .`when`()
            .get("/v1/script-impact/scripts/{scriptId}")
            .then()
            .statusCode(404)
    }

    @Test
    @DisplayName("删除不存在的脚本应返回404")
    fun `should return 404 when deleting non-existent script`() {
        val nonExistentId = 999999L
        given()
            .pathParam("scriptId", nonExistentId)
            .`when`()
            .delete("/v1/script-impact/scripts/{scriptId}")
            .then()
            .statusCode(404)
            .body("success", equalTo(false))
            .body("message", containsString("不存在"))
    }

    @Test
    @DisplayName("应该验证分页参数")
    fun `should validate pagination parameters`() {
        given()
            .queryParam("page", 0)
            .queryParam("size", 5)
            .`when`()
            .get("/v1/script-impact/scripts")
            .then()
            .statusCode(200)
            .body("data.page", equalTo(0))
            .body("data.size", equalTo(5))
    }

    @Test
    @DisplayName("应该返回正确的数据结构")
    fun `should return correct data structure`() {
        val uniqueContent = "-- Structure test ${System.currentTimeMillis()}\\nSELECT 1"
        val (scriptId, _) = uploadTestScript(TEST_UPLOAD_USER, "structure_test.sql", uniqueContent)
        try {
            val response = given()
                .`when`()
                .get("/v1/script-impact/scripts")
                .then()
                .statusCode(200)
                .body("success", equalTo(true))
                .body("message", notNullValue())
                .body("data", notNullValue())
                .body("data.content", notNullValue())
                .body("data.page", notNullValue())
                .body("data.size", notNullValue())
                .body("data.totalElements", notNullValue())
                .body("data.totalPages", notNullValue())
                .extract()
                .response()

            val jsonPath = JsonPath.from(response.asString())
            val scripts = jsonPath.getList<Map<String, Any>>("data.content")

            assertThat(scripts).isNotEmpty
            val firstScript = scripts.first { (it["id"] as Int).toLong() == scriptId }
            assertThat(firstScript).containsKeys(
                "id", "scriptName", "scriptType", "uploadUser",
                "analysisStatus", "createdAt", "updatedAt"
            )
        } finally {
            deleteTestScript(scriptId)
        }
    }
}