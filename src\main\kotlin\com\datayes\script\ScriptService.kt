package com.datayes.script

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import java.security.MessageDigest
import java.time.LocalDateTime

/**
 * 脚本服务 (Script Service)
 * 
 * 实现脚本上传和管理的业务逻辑
 */
@Service
class ScriptService(
    private val scriptRepository: ScriptRepository,
    private val scriptAnalysisService: ScriptAnalysisService
) {

    private val logger = LoggerFactory.getLogger(ScriptService::class.java)

    /**
     * 上传脚本文件 (Upload script file)
     * 
     * UC-1: 处理脚本文件上传，包括验证、去重、存储
     */
    fun uploadScript(file: MultipartFile, uploadUser: String): ScriptUploadResult {
        logger.info("f9e8d7c6 | 开始处理脚本上传: fileName=${file.originalFilename}, uploadUser=$uploadUser")

        try {
            // 1. 验证文件 (Validate file)
            validateUploadedFile(file)

            // 2. 读取文件内容 (Read file content)
            val scriptContent = file.inputStream.bufferedReader().use { it.readText() }
            if (scriptContent.isBlank()) {
                throw IllegalArgumentException("脚本文件内容不能为空 (Script content cannot be empty)")
            }

            // 3. 确定脚本类型 (Determine script type)
            val scriptType = determineScriptType(file.originalFilename!!)

            // 4. 计算文件哈希 (Calculate file hash)
            val fileHash = calculateFileHash(scriptContent)

            // 5. 检查重复文件 (Check for duplicate files)
            val existingScript = scriptRepository.findByFileHash(fileHash)
            if (existingScript != null) {
                logger.warn("b5a4e3f2 | 发现重复文件: fileHash=$fileHash, existingScriptId=${existingScript.id}")
                return ScriptUploadResult(
                    success = false,
                    message = "文件已存在，相同内容的脚本已上传 (File already exists with same content)",
                    scriptId = existingScript.id,
                    duplicateScript = existingScript
                )
            }

            // 6. 创建脚本实体 (Create script entity)
            val script = UploadedScript(
                scriptName = file.originalFilename!!,
                scriptType = scriptType,
                fileSize = file.size,
                fileHash = fileHash,
                scriptContent = scriptContent,
                uploadUser = uploadUser,
                analysisStatus = AnalysisStatus.PENDING,
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            )

            // 7. 保存到数据库 (Save to database)
            val savedScript = scriptRepository.save(script)

            logger.info("c8f2a9e1 | 脚本上传成功: scriptId=${savedScript.id}, scriptType=${savedScript.scriptType}")

            // 8. 触发异步分析 (Trigger async analysis)
            val scriptId = savedScript.id ?: throw IllegalStateException("Saved script should have non-null id")
            scriptAnalysisService.triggerScriptAnalysis(scriptId)

            return ScriptUploadResult(
                success = true,
                message = "脚本上传成功 (Script uploaded successfully)",
                scriptId = scriptId,
                uploadedScript = savedScript
            )

        } catch (e: IllegalArgumentException) {
            logger.warn("d7e6f5a4 | 脚本上传参数错误: ${e.message}")
            return ScriptUploadResult(
                success = false,
                message = e.message ?: "参数错误 (Invalid parameters)"
            )
        } catch (e: Exception) {
            logger.error("a3b2c1d0 | 脚本上传失败", e)
            return ScriptUploadResult(
                success = false,
                message = "上传失败: ${e.message} (Upload failed)"
            )
        }
    }

    /**
     * 查询脚本列表 (Query script list)
     * 
     * UC-2: 分页查询已上传脚本的列表，支持筛选
     */
    fun queryScripts(
        scriptName: String? = null,
        uploadUser: String? = null,
        startDate: LocalDateTime? = null,
        endDate: LocalDateTime? = null,
        scriptType: ScriptType? = null,
        analysisStatus: AnalysisStatus? = null,
        page: Int = 0,
        size: Int = 20
    ): ScriptQueryResult {
        logger.info("e4f3g2h1 | 查询脚本列表: page=$page, size=$size")

        // 参数验证 (Parameter validation)
        if (page < 0) throw IllegalArgumentException("页码不能小于0 (Page number cannot be negative)")
        if (size <= 0 || size > 100) throw IllegalArgumentException("每页大小必须在1-100之间 (Page size must be between 1 and 100)")

        return scriptRepository.findScripts(
            scriptName = scriptName,
            uploadUser = uploadUser,
            startDate = startDate,
            endDate = endDate,
            scriptType = scriptType,
            analysisStatus = analysisStatus,
            page = page,
            size = size
        )
    }

    /**
     * 根据ID获取脚本详情 (Get script details by ID)
     */
    fun getScriptById(id: Long): UploadedScript? {
        logger.info("f8e7d6c5 | 查询脚本详情: scriptId=$id")
        return scriptRepository.findById(id)
    }

    /**
     * 删除脚本 (Delete script)
     * 
     * UC-4: 删除已上传的脚本及其元数据记录
     */
    fun deleteScript(id: Long): ScriptDeleteResult {
        logger.info("g9h8i7j6 | 删除脚本: scriptId=$id")

        try {
            // 检查脚本是否存在 (Check if script exists)
            val script = scriptRepository.findById(id)
            if (script == null) {
                return ScriptDeleteResult(
                    success = false,
                    message = "脚本不存在 (Script not found)"
                )
            }

            // 执行删除 (Perform deletion)
            val deleted = scriptRepository.deleteById(id)
            
            return if (deleted) {
                logger.info("a1b2c3d4 | 脚本删除成功: scriptId=$id")
                ScriptDeleteResult(
                    success = true,
                    message = "脚本删除成功 (Script deleted successfully)"
                )
            } else {
                ScriptDeleteResult(
                    success = false,
                    message = "脚本删除失败 (Failed to delete script)"
                )
            }

        } catch (e: Exception) {
            logger.error("e5f6g7h8 | 删除脚本时发生错误: scriptId=$id", e)
            return ScriptDeleteResult(
                success = false,
                message = "删除失败: ${e.message} (Delete failed)"
            )
        }
    }

    /**
     * 验证上传的文件 (Validate uploaded file)
     */
    private fun validateUploadedFile(file: MultipartFile) {
        if (file.isEmpty) {
            throw IllegalArgumentException("上传文件不能为空 (Uploaded file cannot be empty)")
        }

        val fileName = file.originalFilename
        if (fileName.isNullOrBlank()) {
            throw IllegalArgumentException("文件名不能为空 (File name cannot be empty)")
        }

        // 文件大小限制 10MB (File size limit)
        val maxFileSize = 10 * 1024 * 1024 // 10MB
        if (file.size > maxFileSize) {
            throw IllegalArgumentException(
                "文件大小不能超过10MB，当前文件大小: ${file.size / 1024 / 1024}MB " +
                "(File size cannot exceed 10MB, current size: ${file.size / 1024 / 1024}MB)"
            )
        }

        // 文件扩展名验证 (File extension validation)
        val supportedExtensions = listOf(".sql", ".sh")
        val hasValidExtension = supportedExtensions.any { fileName.endsWith(it, ignoreCase = true) }
        if (!hasValidExtension) {
            throw IllegalArgumentException(
                "不支持的文件格式: $fileName. 仅支持 ${supportedExtensions.joinToString(", ")} 格式 " +
                "(Unsupported file format, only ${supportedExtensions.joinToString(", ")} are supported)"
            )
        }

        logger.info("i9j8k7l6 | 文件验证通过: fileName=$fileName, size=${file.size}")
    }

    /**
     * 确定脚本类型 (Determine script type)
     */
    private fun determineScriptType(fileName: String): ScriptType {
        return when {
            fileName.endsWith(".sql", ignoreCase = true) -> ScriptType.SQL
            fileName.endsWith(".sh", ignoreCase = true) -> ScriptType.SHELL
            else -> throw IllegalArgumentException("无法确定脚本类型: $fileName (Cannot determine script type)")
        }
    }

    /**
     * 计算文件哈希值 (Calculate file hash)
     */
    private fun calculateFileHash(content: String): String {
        val digest = MessageDigest.getInstance("SHA-256")
        val hashBytes = digest.digest(content.toByteArray())
        return hashBytes.joinToString("") { "%02x".format(it) }
    }
}

/**
 * 脚本上传结果 (Script Upload Result)
 */
data class ScriptUploadResult(
    val success: Boolean,
    val message: String,
    val scriptId: Long? = null,
    val uploadedScript: UploadedScript? = null,
    val duplicateScript: UploadedScript? = null
)

/**
 * 脚本删除结果 (Script Delete Result)
 */
data class ScriptDeleteResult(
    val success: Boolean,
    val message: String
) 