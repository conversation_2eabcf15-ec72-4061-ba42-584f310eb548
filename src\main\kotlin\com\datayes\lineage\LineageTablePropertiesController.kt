package com.datayes.lineage

import com.datayes.ApiResponse
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

/**
 * 血缘表属性控制器 (Lineage Table Properties Controller)
 * 
 * 提供血缘表属性管理的REST API接口，直接操作lineage_tables表
 */
@Tag(name = "Lineage Table Properties", description = "血缘表属性管理接口")
@RestController
@RequestMapping("/api/lineage/table-properties")
@CrossOrigin(origins = ["*"])
class LineageTablePropertiesController(
    private val propertiesService: LineageTablePropertiesService
) {
    
    companion object {
        private val log = LoggerFactory.getLogger(LineageTablePropertiesController::class.java)
    }

    /**
     * 根据血缘表ID查询表信息和属性
     * 
     * @param tableId 血缘表ID
     * @return 血缘表信息
     */
    @Operation(summary = "查询血缘表信息", description = "根据血缘表ID查询表信息和属性")
    @GetMapping("/{tableId}")
    fun getTableInfoById(
        @Parameter(description = "血缘表ID", example = "123")
        @PathVariable tableId: Long
    ): ResponseEntity<ApiResponse<LineageTableInfo?>> {
        return try {
            log.info("f1g2h3i4 | Received request to get table info for table ID: {}", tableId)
            
            val tableInfo = propertiesService.findTableInfoById(tableId)
            
            if (tableInfo != null) {
                log.info("j5k6l7m8 | Successfully retrieved table info for table ID: {}", tableId)
                ResponseEntity.ok(ApiResponse.success(tableInfo, "Table info retrieved successfully"))
            } else {
                log.info("n9o0p1q2 | No table found for table ID: {}", tableId)
                ResponseEntity.ok(ApiResponse.success(null, "No table found for this ID"))
            }
        } catch (e: Exception) {
            log.error("r3s4t5u6 | Error retrieving table info for table ID {}: {}", tableId, e.message, e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error<LineageTableInfo?>("Failed to retrieve table info: ${e.message}"))
        }
    }

    /**
     * 根据查询条件搜索血缘表信息
     * 
     * @param tableId 血缘表ID（可选）
     * @param tableName 表名筛选（可选）
     * @param schemaName 模式名筛选（可选）
     * @param syncFrequency 同步频率筛选（可选）
     * @param requirementId 软开需求编号筛选（可选）
     * @param datasourceId 数据源ID筛选（可选）
     * @return 匹配的血缘表信息列表
     */
    @Operation(summary = "搜索血缘表信息", description = "根据查询条件搜索血缘表信息")
    @GetMapping("/search")
    fun searchTableInfo(
        @Parameter(description = "血缘表ID", example = "123")
        @RequestParam(required = false) tableId: Long?,
        @Parameter(description = "表名", example = "user_info")
        @RequestParam(required = false) tableName: String?,
        @Parameter(description = "模式名", example = "public")
        @RequestParam(required = false) schemaName: String?,
        @Parameter(description = "同步频率", example = "daily")
        @RequestParam(required = false) syncFrequency: String?,
        @Parameter(description = "软开需求编号", example = "REQ-2024-001")
        @RequestParam(required = false) requirementId: String?,
        @Parameter(description = "数据源ID", example = "456")
        @RequestParam(required = false) datasourceId: Long?
    ): ResponseEntity<ApiResponse<List<LineageTableInfo>>> {
        return try {
            log.info("v7w8x9y0 | Received search request with params: tableId={}, tableName={}, syncFrequency={}, requirementId={}", 
                tableId, tableName, syncFrequency, requirementId)
            
            val request = QueryLineageTablePropertiesRequest(
                tableId = tableId,
                tableName = tableName,
                schemaName = schemaName,
                syncFrequency = syncFrequency,
                requirementId = requirementId,
                datasourceId = datasourceId
            )
            
            val results = propertiesService.queryTableInfo(request)
            
            log.info("z1a2b3c4 | Search returned {} table records", results.size)
            ResponseEntity.ok(ApiResponse.success(results, "Search completed successfully"))
        } catch (e: Exception) {
            log.error("d5e6f7g8 | Error during table search: {}", e.message, e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error<List<LineageTableInfo>>("Search failed: ${e.message}"))
        }
    }

    /**
     * 更新血缘表属性信息
     * 
     * @param tableId 血缘表ID
     * @param command 更新命令
     * @return 更新后的表信息
     */
    @Operation(summary = "更新血缘表属性", description = "更新血缘表的属性信息")
    @PutMapping("/{tableId}")
    fun updateTableProperties(
        @Parameter(description = "血缘表ID", example = "123")
        @PathVariable tableId: Long,
        @RequestBody command: UpdateLineageTablePropertiesCommand
    ): ResponseEntity<ApiResponse<LineageTableInfo>> {
        return try {
            log.info("h9i0j1k2 | Received update request for table ID {}: {}", tableId, command)
            
            val updatedTableInfo = propertiesService.updateTableProperties(tableId, command)
            
            log.info("l3m4n5o6 | Successfully updated properties for table ID: {}", tableId)
            ResponseEntity.ok(ApiResponse.success(updatedTableInfo, "Properties updated successfully"))
        } catch (e: IllegalArgumentException) {
            log.warn("p7q8r9s0 | Invalid request for table ID {}: {}", tableId, e.message)
            ResponseEntity.badRequest()
                .body(ApiResponse.error<LineageTableInfo>("Invalid request: ${e.message}"))
        } catch (e: Exception) {
            log.error("t1u2v3w4 | Error updating properties for table ID {}: {}", tableId, e.message, e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error<LineageTableInfo>("Update failed: ${e.message}"))
        }
    }

    /**
     * 清空血缘表属性信息
     * 
     * @param tableId 血缘表ID
     * @return 清空结果
     */
    @Operation(summary = "清空血缘表属性", description = "清空血缘表的属性信息（将属性字段设为null）")
    @DeleteMapping("/{tableId}")
    fun clearTableProperties(
        @Parameter(description = "血缘表ID", example = "123")
        @PathVariable tableId: Long
    ): ResponseEntity<ApiResponse<String>> {
        return try {
            log.info("n1o2p3q4 | Received clear request for table ID: {}", tableId)
            
            val cleared = propertiesService.clearTableProperties(tableId)
            
            if (cleared) {
                log.info("r5s6t7u8 | Successfully cleared properties for table ID: {}", tableId)
                ResponseEntity.ok(ApiResponse.success("Success", "Properties cleared successfully"))
            } else {
                log.info("v9w0x1y2 | No table found to clear properties for table ID: {}", tableId)
                ResponseEntity.ok(ApiResponse.success("Not Found", "No table found for this ID"))
            }
        } catch (e: Exception) {
            log.error("z3a4b5c6 | Error clearing properties for table ID {}: {}", tableId, e.message, e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error<String>("Clear operation failed: ${e.message}"))
        }
    }

    /**
     * 获取血缘表属性统计信息
     * 
     * @return 统计信息
     */
    @Operation(summary = "获取属性统计信息", description = "获取血缘表属性的统计信息")
    @GetMapping("/statistics")
    fun getStatistics(): ResponseEntity<ApiResponse<Map<String, Any>>> {
        return try {
            log.info("d7e8f9g0 | Received request for properties statistics")
            
            val statistics = propertiesService.getStatistics()
            
            log.info("h1i2j3k4 | Successfully retrieved properties statistics")
            ResponseEntity.ok(ApiResponse.success(statistics, "Statistics retrieved successfully"))
        } catch (e: Exception) {
            log.error("l5m6n7o8 | Error retrieving properties statistics: {}", e.message, e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error<Map<String, Any>>("Failed to retrieve statistics: ${e.message}"))
        }
    }
}