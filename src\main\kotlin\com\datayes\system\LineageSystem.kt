package com.datayes.system

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.time.LocalDateTime

/**
 * 系统管理实体 (Lineage System Entity)
 *
 * 对应数据库表 lineage_systems
 */
@Table("lineage_systems")
data class LineageSystem(
    @Id
    val id: Long = 0,

    @Column("system_name")
    val systemName: String,

    @Column("system_code")
    val systemCode: String,

    @Column("description")
    val description: String? = null,

    @Column("contact_person")
    val contactPerson: String? = null,

    @Column("status")
    val status: SystemStatus = SystemStatus.ACTIVE,

    @Column("cron_expression")
    val cronExpression: String? = null,

    @Column("created_at")
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column("updated_at")
    val updatedAt: LocalDateTime = LocalDateTime.now()
)

/**
 * 系统状态 (System Status)
 */
enum class SystemStatus {
    ACTIVE,
    INACTIVE
}
