package com.datayes.lineage

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

/**
 * 手动血缘管理控制器 (Manual Lineage Management Controller)
 *
 * UC-14, UC-15, UC-16: 提供手动血缘记录的创建、编辑和删除API接口
 */
@Tag(name = "Manual Lineage API", description = "手动血缘管理 (Manual Lineage Management) 相关接口")
@RestController
@RequestMapping("/api/manual-lineage")
@CrossOrigin("*")
class ManualLineageController(
    private val manualLineageService: ManualLineageService
) {

    private val logger = LoggerFactory.getLogger(ManualLineageController::class.java)

    /**
     * 创建血缘关系 (Create lineage relationship)
     *
     * 自动处理数据源、表和列的创建，使用source_system = 'MANUAL_INPUT'
     */
    @Operation(
        summary = "创建血缘关系",
        description = "自动创建数据源、表和列（如果不存在），创建表级和列级血缘关系，source_system 设置为 MANUAL_INPUT"
    )
    @PostMapping("/v2/create")
    fun createLineageRelationship(
        @Parameter(description = "创建血缘关系请求", required = true)
        @RequestBody request: CreateLineageRequest
    ): ResponseEntity<LineageOperationResponse> {
        logger.info("k1l2m3n4 | 收到简化创建血缘关系请求: sourceTable=${request.sourceTableName}, targetTable=${request.targetTableName}")

        return try {
            val result = manualLineageService.createLineageRelationship(request)

            if (result.success) {
                logger.info("o5p6q7r8 | 简化血缘关系创建成功: tableRelationshipId=${result.tableRelationshipId}")
                ResponseEntity.ok(result)
            } else {
                logger.warn("s9t0u1v2 | 简化血缘关系创建失败: ${result.message}")
                ResponseEntity.badRequest().body(result)
            }
        } catch (e: Exception) {
            logger.error("w3x4y5z6 | 创建简化血缘关系时发生未预期错误", e)
            ResponseEntity.internalServerError().body(
                LineageOperationResponse(
                    success = false,
                    message = "系统内部错误",
                    errors = listOf("系统错误: ${e.message}")
                )
            )
        }
    }

    /**
     * 更新血缘关系 (Update lineage relationship)
     *
     * 根据表级关系ID更新列映射，只影响source_system = 'MANUAL_INPUT'的数据
     */
    @Operation(
        summary = "更新血缘关系",
        description = "根据表级关系ID更新列映射，只影响 source_system = 'MANUAL_INPUT' 的数据，替换现有列映射"
    )
    @PutMapping("/v2/update")
    fun updateLineageRelationship(
        @Parameter(description = "更新血缘关系请求", required = true)
        @RequestBody request: UpdateLineageRequest
    ): ResponseEntity<LineageOperationResponse> {
        logger.info("a7b8c9d0 | 收到简化更新血缘关系请求: tableRelationshipId=${request.tableRelationshipId}")

        return try {
            val result = manualLineageService.updateLineageRelationship(request)

            if (result.success) {
                logger.info("e1f2g3h4 | 简化血缘关系更新成功: tableRelationshipId=${request.tableRelationshipId}")
                ResponseEntity.ok(result)
            } else {
                logger.warn("i5j6k7l8 | 简化血缘关系更新失败: ${result.message}")
                ResponseEntity.badRequest().body(result)
            }
        } catch (e: Exception) {
            logger.error("m9n0o1p2 | 更新简化血缘关系时发生未预期错误", e)
            ResponseEntity.internalServerError().body(
                LineageOperationResponse(
                    success = false,
                    message = "系统内部错误",
                    tableRelationshipId = request.tableRelationshipId,
                    errors = listOf("系统错误: ${e.message}")
                )
            )
        }
    }

    /**
     * 删除血缘关系 (Delete lineage relationships)
     *
     * 批量删除表级和相关列级血缘关系，只删除source_system = 'MANUAL_INPUT'的数据
     */
    @Operation(
        summary = "删除血缘关系",
        description = "批量删除表级和相关列级血缘关系，只删除 source_system = 'MANUAL_INPUT' 的数据"
    )
    @DeleteMapping("/v2/delete")
    fun deleteLineageRelationships(
        @Parameter(description = "删除血缘关系请求", required = true)
        @RequestBody request: DeleteLineageRequest
    ): ResponseEntity<LineageOperationResponse> {
        logger.info("q3r4s5t6 | 收到简化删除血缘关系请求: count=${request.tableRelationshipIdList.size}")

        return try {
            val result = manualLineageService.deleteLineageRelationships(request)

            if (result.success) {
                logger.info("u7v8w9x0 | 简化血缘关系删除成功: deletedCount=${result.affectedTableRelationships}")
                ResponseEntity.ok(result)
            } else {
                logger.warn("y1z2a3b4 | 简化血缘关系部分删除失败: ${result.message}")
                ResponseEntity.ok(result) // 返回200但包含错误信息
            }
        } catch (e: Exception) {
            logger.error("c5d6e7f8 | 删除简化血缘关系时发生未预期错误", e)
            ResponseEntity.internalServerError().body(
                LineageOperationResponse(
                    success = false,
                    message = "系统内部错误",
                    errors = listOf("系统错误: ${e.message}")
                )
            )
        }
    }

    /**
     * 获取血缘关系详情 (Get lineage relationship details)
     */
    @Operation(
        summary = "获取血缘关系详情",
        description = "根据表级血缘关系ID获取详细信息，包括源表、目标表和列映射信息"
    )
    @GetMapping("/v2/details/{tableRelationshipId}")
    fun getLineageRelationshipDetails(
        @Parameter(description = "表级血缘关系ID", example = "1001")
        @PathVariable tableRelationshipId: Long
    ): ResponseEntity<LineageRelationshipDetails> {
        logger.info("g9h0i1j2 | 收到获取简化血缘关系详情请求: tableRelationshipId=$tableRelationshipId")

        return try {
            val details = manualLineageService.getLineageRelationshipDetails(tableRelationshipId)

            if (details != null) {
                logger.info("k3l4m5n6 | 简化血缘关系详情查询成功: tableRelationshipId=$tableRelationshipId")
                ResponseEntity.ok(details)
            } else {
                logger.warn("o7p8q9r0 | 未找到简化血缘关系详情: tableRelationshipId=$tableRelationshipId")
                ResponseEntity.notFound().build()
            }
        } catch (e: Exception) {
            logger.error("s1t2u3v4 | 获取简化血缘关系详情时发生未预期错误", e)
            ResponseEntity.internalServerError().build()
        }
    }
}