# Metadata Service API Documentation

## Overview

The Metadata Service provides REST API endpoints to query metadata information from `metadata_data_source` and `metadata_system_info` tables. The service focuses on matching lineage datasources with metadata datasources and provides read-only operations.

## Key Features

- **Lineage Matching**: Match `lineage_datasources` entries with `metadata_data_source` entries
- **Raw SQL Queries**: Uses JdbcTemplate with raw SQL for optimal performance
- **Password Security**: DB_PASSWORD field is excluded from all responses
- **System Integration**: Joins metadata_data_source with metadata_system_info
- **Read-Only Operations**: No update/delete operations on metadata tables

## API Endpoints

### Base URL: `/api/v1/metadata`

### 1. Find Matched Metadata Data Sources

**Endpoint:** `GET /datasources/match/{lineageDatasourceId}`

**Description:** Find metadata data sources that match a specific lineage datasource entry based on database type, host, port, and database name.

**Parameters:**
- `lineageDatasourceId` (path): ID from lineage_datasources table

**Response:**
```json
{
  "success": true,
  "message": "匹配成功",
  "data": {
    "lineageDatasourceId": 1,
    "matchedDataSources": [
      {
        "id": 1,
        "sourceName": "MySQL生产数据库",
        "dbType": "mysql",
        "dbDriver": "com.mysql.cj.jdbc.Driver",
        "dbName": "production_db",
        "dbUrl": "***********",
        "dbPort": 3306,
        "dbUsername": "admin",
        "customJdbcUrl": null,
        "activeFlag": true,
        "systemId": 1,
        "description": "生产环境主数据库",
        "systemInfo": {
          "id": 1,
          "systemName": "数据管理平台",
          "systemAbbreviation": "DMP",
          "systemType": "数据平台",
          "systemModule": "核心模块",
          "moduleOwner": "张三",
          "developmentDepartment": "数据部",
          "activeFlag": true
        }
      }
    ],
    "totalCount": 1
  }
}
```

### 2. Get All Active Metadata Data Sources

**Endpoint:** `GET /datasources`

**Description:** Retrieve all active metadata data sources with their associated system information.

**Response:**
```json
{
  "success": true,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "sourceName": "MySQL生产数据库",
      "dbType": "mysql",
      "dbDriver": "com.mysql.cj.jdbc.Driver",
      "dbName": "production_db",
      "dbUrl": "***********",
      "dbPort": 3306,
      "dbUsername": "admin",
      "customJdbcUrl": null,
      "activeFlag": true,
      "systemId": 1,
      "description": "生产环境主数据库",
      "systemInfo": { /* system info object */ }
    }
  ]
}
```

### 3. Get Metadata Data Sources by System ID

**Endpoint:** `GET /datasources/system/{systemId}`

**Description:** Get all metadata data sources belonging to a specific system.

**Parameters:**
- `systemId` (path): System ID from metadata_system_info table

**Response:** Same structure as endpoint #2, filtered by system ID

### 4. Get All Active System Info

**Endpoint:** `GET /systems`

**Description:** Retrieve all active system information.

**Response:**
```json
{
  "success": true,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "systemName": "数据管理平台",
      "systemAbbreviation": "DMP",
      "systemType": "数据平台",
      "systemModule": "核心模块",
      "moduleOwner": "张三",
      "developmentDepartment": "数据部",
      "activeFlag": true,
      "createBy": "admin",
      "createTime": "2024-01-01T00:00:00",
      "updateBy": "admin",
      "updateTime": "2024-01-01T00:00:00"
    }
  ]
}
```

### 5. Health Check

**Endpoint:** `GET /health`

**Description:** Check service health status.

**Response:**
```json
{
  "success": true,
  "message": "服务正常",
  "data": {
    "service": "MetadataService",
    "status": "UP",
    "timestamp": 1703001600000,
    "version": "1.0.0"
  }
}
```

## Matching Logic

The service matches lineage datasources with metadata datasources using the following criteria:

1. **Primary Matching:**
   - Database Type (`DB_TYPE` = `db_type`)
   - Host/URL (`DB_URL` LIKE `%host%`)
   - Port (`DB_PORT` = `port`)
   - Database Name (`DB_NAME` = `database_name`)

2. **Secondary Matching (Custom JDBC):**
   - Database Type (`DB_TYPE` = `db_type`)
   - Custom JDBC URL contains host (`CUSTOM_JDBC_URL` LIKE `%host%`)
   - Custom JDBC URL contains database name (`CUSTOM_JDBC_URL` LIKE `%database_name%`)

## Usage Examples

### Test Endpoints with curl

```bash
# Health check
curl -X GET "http://localhost:8080/api/v1/metadata/health"

# Get all systems
curl -X GET "http://localhost:8080/api/v1/metadata/systems"

# Get all data sources
curl -X GET "http://localhost:8080/api/v1/metadata/datasources"

# Get data sources by system ID
curl -X GET "http://localhost:8080/api/v1/metadata/datasources/system/1"

# Find matched data sources for lineage datasource ID 1
curl -X GET "http://localhost:8080/api/v1/metadata/datasources/match/1"
```

### Test in Browser

Navigate to these URLs after starting the application:

- Health Check: `http://localhost:8080/api/v1/metadata/health`
- All Systems: `http://localhost:8080/api/v1/metadata/systems`
- All Data Sources: `http://localhost:8080/api/v1/metadata/datasources`

## Error Handling

The service provides comprehensive error handling:

- **400 Bad Request**: Invalid lineage datasource ID
- **500 Internal Server Error**: Database connection issues or unexpected errors
- **Detailed Logging**: Each request has unique UUID prefixes for traceability

## Security Considerations

- **Password Exclusion**: DB_PASSWORD field is never returned in API responses
- **Read-Only Operations**: Service only performs SELECT queries
- **Input Validation**: Path parameters are validated for type safety
- **CORS Enabled**: Allows cross-origin requests for development/testing

## Database Dependencies

The service requires access to these tables:
- `metadata_data_source`: Metadata data source information
- `metadata_system_info`: System configuration information  
- `lineage_datasources`: Lineage data source information (for matching)

All queries use INNER/LEFT JOINs to ensure data consistency and referential integrity.