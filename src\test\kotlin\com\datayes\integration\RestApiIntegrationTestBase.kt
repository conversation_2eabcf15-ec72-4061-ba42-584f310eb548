package com.datayes.integration

import io.restassured.RestAssured
import io.restassured.http.ContentType
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.TestInstance

/**
 * REST API 集成测试基类 (Base class for REST API integration tests)
 * 
 * 该基类配置 RestAssured 来测试运行中的应用程序。
 * 测试假设应用程序已经启动并运行在配置的端口上。
 * 
 * 使用说明:
 * 1. 手动启动应用程序 (通过 IDE 或 ./mvnw spring-boot:run)
 * 2. 继承此基类编写 REST API 集成测试
 * 3. 测试将对真实的 HTTP 端点发送请求
 * 4. 遵循只读操作原则，不对数据库进行删除或更新操作
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
abstract class RestApiIntegrationTestBase {

    companion object {
        /**
         * 应用程序默认端口 (Application default port)
         */
        private const val DEFAULT_PORT = 9503
        
        /**
         * 应用程序默认主机 (Application default host)  
         */
        private const val DEFAULT_HOST = "**********" // don't any why
        
        /**
         * API 基础路径 (API base path)
         */
        private const val API_BASE_PATH = "/api"
    }

    @BeforeAll
    fun setupRestAssured() {
        // 从系统属性或环境变量获取配置，否则使用默认值
        val host = System.getProperty("test.api.host") ?: System.getenv("TEST_API_HOST") ?: DEFAULT_HOST
        val port = System.getProperty("test.api.port")?.toIntOrNull() 
                   ?: System.getenv("TEST_API_PORT")?.toIntOrNull() 
                   ?: DEFAULT_PORT
        
        RestAssured.baseURI = "http://$host"
        RestAssured.port = port
        RestAssured.basePath = API_BASE_PATH
        
        // 设置默认内容类型
        RestAssured.requestSpecification = RestAssured.given()
            .contentType(ContentType.JSON)
            .accept(ContentType.JSON)
        
        println("9a1f4d2e | RestAssured 配置完成: baseURI=http://$host:$port$API_BASE_PATH")
    }
    
    /**
     * 获取当前配置的基础 URL (Get current configured base URL)
     */
    protected fun getBaseUrl(): String {
        return "${RestAssured.baseURI}:${RestAssured.port}${RestAssured.basePath}"
    }
}