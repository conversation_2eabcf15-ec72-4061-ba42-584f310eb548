1.3 脚本影响分析 (Script Impact Analysis) - API 用户故事
UC-1: 上传脚本文件 (Upload Script File)
作为一个 开发人员 (Developer)，
我想要 通过一个API端点上传一个SQL (.sql) 或 Shell (.sh) 脚本文件，
以便 系统可以存储该文件并对其进行后续的影响分析。

UC-2: 查询脚本列表 (Query Script List)
作为一个 开发人员 (Developer)，
我想要 通过API请求一个已上传脚本的分页列表，并能根据脚本名称、上传人和上传时间范围进行筛选，
以便 我可以高效地查找和管理已经上传的脚本。

UC-3: 获取脚本影响分析结果 (Get Script Impact Analysis Result)
作为一个 开发人员 (Developer)，
我想要 通过API为一个指定的脚本ID请求影响分析，
以便 我能获得一个结构化的数据（如JSON），该数据描述了完整的血缘关系图谱。这个图谱应包含：a) 从脚本中直接解析出的表和字段关系；b) 这些受影响的表在现有血缘库中的完整上下游依赖关系。

UC-4: 删除已上传的脚本 (Delete Uploaded Script)
作为一个 开发人员 (Developer)，
我想要 通过API使用脚本ID来删除一个已上传的脚本及其元数据记录，
以便 我可以移除系统中不再需要或已过时的脚本，保持列表的整洁。

UC-5: 下载原始脚本文件 (Download Original Script File)
作为一个 开发人员 (Developer)，
我想要 通过API使用脚本ID下载原始的脚本文件，
以便 我可以随时查看或审计脚本的确切内容。