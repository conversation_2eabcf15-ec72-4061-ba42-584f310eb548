---
description:
globs:
alwaysApply: false
---
# DGP 血缘收集器主规则 (DGP Lineage Collector Main Rules)

## 项目概览 (Project Overview)
详细信息请参考：[project-overview.mdc](mdc:.cursor/rules/project-overview.mdc)

## 核心模块 (Core Modules)

### 1. 血缘任务模块 (Lineage Task Module)
负责任务生命周期管理，详见：[lineage-task-module.mdc](mdc:.cursor/rules/lineage-task-module.mdc)

### 2. SQL 解析器模块 (SQL Parser Module)  
负责 SQL 解析和血缘提取，详见：[sql-parser-module.mdc](mdc:.cursor/rules/sql-parser-module.mdc)

### 3. 血缘目录模块 (Lineage Catalog Module)
负责血缘数据存储和查询，详见：[lineage-catalog-module.mdc](mdc:.cursor/rules/lineage-catalog-module.mdc)

## 开发规范 (Development Standards)
编码标准和最佳实践请参考：[coding-standards.mdc](mdc:.cursor/rules/coding-standards.mdc)

## 快速开始 (Quick Start)

### 编译项目 (Compile Project)
```bash
mvn clean compile
```

### 运行应用 (Run Application)
```bash
mvn spring-boot:run
```

### 运行测试 (Run Tests)
```bash
mvn test
```

## 关键文件导航 (Key File Navigation)

### 应用入口 (Application Entry)
- [App.kt](mdc:src/main/kotlin/com/datayes/App.kt) - 主应用类 (main application class)

### 配置文件 (Configuration Files)
- [pom.xml](mdc:pom.xml) - Maven 构建配置 (Maven build configuration)
- [application.properties](mdc:src/main/resources/application.properties) - 应用配置 (application configuration)

### API 控制器 (API Controllers)
- [LineageTaskController.kt](mdc:src/main/kotlin/com/datayes/lineagetask/LineageTaskController.kt) - 血缘任务 REST API

### 核心服务 (Core Services)
- [LineageTaskService.kt](mdc:src/main/kotlin/com/datayes/lineagetask/LineageTaskService.kt) - 血缘任务业务逻辑
- [SqlParser.kt](mdc:src/main/kotlin/com/datayes/parser/SqlParser.kt) - SQL 解析器

### 数据访问 (Data Access)
- [LineageRepository.kt](mdc:src/main/kotlin/com/datayes/lineagecatalog/LineageRepository.kt) - 血缘数据访问
- [LineageTaskRepository.kt](mdc:src/main/kotlin/com/datayes/lineagetask/LineageTaskRepository.kt) - 任务数据访问

## 模块间依赖 (Module Dependencies)
```
lineagetask → parser → lineagecatalog
     ↓           ↓           ↓
  REST API → SQL解析 → 数据存储
```

## 调试提示 (Debugging Tips)
- 查看编译警告并逐步修复 (review compilation warnings and fix gradually)
- 使用日志记录关键操作 (use logging for key operations)  
- 单元测试覆盖核心逻辑 (unit test coverage for core logic)
- 数据库连接配置检查 (database connection configuration check)
