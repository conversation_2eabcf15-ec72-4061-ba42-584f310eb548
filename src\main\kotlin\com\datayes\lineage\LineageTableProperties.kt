package com.datayes.lineage

import java.time.LocalDateTime

/**
 * 血缘表信息 (Lineage Table Info)
 * 
 * 血缘表的完整信息，包括基本信息和属性
 *
 * @property id 表ID
 * @property datasourceId 数据源ID
 * @property schemaName 模式名
 * @property tableName 表名
 * @property tableType 表类型
 * @property chineseName 中文名称
 * @property description 表描述
 * @property syncFrequency 同步频率
 * @property requirementId 软开需求编号
 * @property dataSyncScope 数据同步范围
 * @property status 状态
 * @property createdAt 创建时间
 * @property updatedAt 更新时间
 */
data class LineageTableInfo(
    val id: Long,
    val datasourceId: Long,
    val schemaName: String? = null,
    val tableName: String,
    val tableType: String? = null,
    val chineseName: String? = null,
    val description: String? = null,
    val syncFrequency: String? = null,
    val requirementId: String? = null,
    val dataSyncScope: String? = null,
    val status: String = "ACTIVE",
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
)

/**
 * 更新血缘表属性命令 (Update Lineage Table Properties Command)
 * 
 * 用于更新血缘表属性的命令对象
 *
 * @property syncFrequency 同步频率
 * @property dataSyncScope 数据同步范围
 * @property requirementId 软开需求编号
 */
data class UpdateLineageTablePropertiesCommand(
    val syncFrequency: String? = null,
    val dataSyncScope: String? = null,
    val requirementId: String? = null
)

/**
 * 查询血缘表属性请求 (Query Lineage Table Properties Request)
 * 
 * 用于查询血缘表属性的请求对象，可根据多个条件进行查询
 *
 * @property tableId 血缘表ID
 * @property tableName 表名筛选
 * @property schemaName 模式名筛选
 * @property syncFrequency 同步频率筛选
 * @property requirementId 软开需求编号筛选
 * @property datasourceId 数据源ID筛选
 */
data class QueryLineageTablePropertiesRequest(
    val tableId: Long? = null,
    val tableName: String? = null,
    val schemaName: String? = null,
    val syncFrequency: String? = null,
    val requirementId: String? = null,
    val datasourceId: Long? = null
)