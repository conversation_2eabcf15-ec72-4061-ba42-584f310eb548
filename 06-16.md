# 脚本影响分析功能 - 待办事项 (To-Do List) - 2025-06-16

基于对用户故事 `user-story-5.3.3_脚本影响分析.md` 和当前代码库的分析，以下是为完整实现脚本影响分析功能（特别是 UC-3）所需完成的任务：

## 1. 实现脚本分析核心逻辑

当前项目的 API 框架已具备存储和查询脚本元数据及分析状态的能力，但实际执行分析并填充分析结果 (`analysisResult`) 和临时血缘ID (`temporaryLineageId`) 的核心逻辑缺失。

*   **创建新的分析服务/组件** (例如 `ScriptAnalysisService`):
    *   [ ] **获取待分析脚本**: 实现逻辑以识别和获取 `analysisStatus` 为 `PENDING` 的脚本。
    *   [ ] **更新状态为分析中**: 在开始分析前，调用 `ScriptRepository.updateAnalysisStatus(id, AnalysisStatus.ANALYZING)` 将脚本状态更新为 `ANALYZING`。
    *   [ ] **脚本内容解析**:
        *   [ ] 实现对 SQL 脚本的解析，提取直接的表和字段依赖关系 (例如，识别 `SELECT FROM`, `INSERT INTO`, `UPDATE`, `DELETE`, `JOIN` 等语句中的表和列)。
        *   [ ] 实现对 Shell 脚本的解析 (如果适用，可能需要识别脚本中执行的数据库命令或调用的其他脚本，这可能比较复杂)。
    *   [ ] **查询现有血缘库**:
        *   [ ] 实现与现有血缘数据库/服务的交互，以获取从脚本中解析出的受影响表和字段的完整上游和下游依赖关系。
    *   [ ] **合并分析结果**:
        *   [ ] 将直接从脚本解析出的关系与从血缘库中获取的依赖关系合并成一个结构化的数据格式 (例如 JSON)。此结果将作为 `analysisResult` 存储。
    *   [ ] **处理 `temporaryLineageId`**:
        *   [ ] 明确 `temporaryLineageId` 的生成逻辑和用途。
        *   [ ] 决定是将其作为 `analysisResult` JSON 的一部分，还是需要修改 `ScriptRepository.updateAnalysisStatus` 方法 (或添加新方法) 以单独存储它。
    *   [ ] **更新最终分析状态**:
        *   [ ] 分析成功后，调用 `ScriptRepository.updateAnalysisStatus(id, AnalysisStatus.COMPLETED, analysisResultJson, temporaryLineageId_if_separate)`。
        *   [ ] 分析失败后，调用 `ScriptRepository.updateAnalysisStatus(id, AnalysisStatus.FAILED, errorMessage)` 并记录详细错误。

## 2. 确定并实现分析触发机制

需要一种机制来调用新创建的脚本分析服务。

*   **选项 A: 上传后异步触发 (当前暗示的设计)**
    *   [ ] **选择异步机制**:
        *   **Spring `@Async`**: 为分析服务的方法添加 `@Async` 注解，使其在独立的线程中执行。
        *   **消息队列 (Message Queue)**: 例如 Kafka, RabbitMQ。脚本上传成功后发送一个消息，由一个专门的消费者 (consumer) 监听并调用分析服务。
        *   **定时任务 (Scheduled Task)**: 创建一个定时任务 (例如使用 Spring `@Scheduled`)，定期轮询数据库查找 `analysisStatus` 为 `PENDING` 的脚本并进行处理。
    *   [ ] **实现所选机制**: 根据选择集成并配置。

*   **选项 B: 显式 API 触发 (用户故事 UC-3 的另一种解读 "请求影响分析")**
    *   [ ] **设计并实现新的 API 端点**:
        *   在 `ScriptImpactAnalysisController` 中添加一个新的 API 端点，例如 `POST /api/v1/script-impact/scripts/{scriptId}/analyze`。
        *   此端点应接受脚本 ID (`scriptId`)。
        *   端点逻辑将调用新的脚本分析服务来处理指定的脚本。
        *   考虑此 API 是同步返回分析结果还是异步启动分析过程（例如，返回一个任务ID供后续查询状态）。

*   **决策**: 需要根据产品需求确定是仅实现异步触发，还是同时提供显式API触发，或者两者都需要。

## 3. 澄清 `temporaryLineageId` 的具体细节

*   [ ] **定义**: 明确 `temporaryLineageId` 代表什么。是一个指向临时存储的图谱ID，还是一个在主血缘系统中的ID，或其它？
*   [ ] **生命周期**: 它的生命周期是多久？何时创建，何时失效或被清理？
*   [ ] **与 `analysisResult` 的关系**: 它是否可以被包含在 `analysisResult` 的 JSON 结构中，还是必须作为 `UploadedScript` 表中的一个独立字段？
*   [ ] **存储**: 如果需要独立存储，确认 `ScriptRepository.updateAnalysisStatus` 是否需要修改以接受并更新此字段，或者是否需要一个新的 repository 方法。

## 4. 测试

*   [ ] **单元测试 (Unit Tests)**: 为新的分析服务、解析逻辑、血缘库交互等编写单元测试。
*   [ ] **集成测试 (Integration Tests)**: 测试脚本上传、分析触发、状态更新、结果获取的完整流程。
*   [ ] **端到端测试 (End-to-End Tests)**: 模拟用户通过 API 上传脚本并获取分析结果的场景。
