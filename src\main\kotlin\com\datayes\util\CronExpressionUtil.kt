package com.datayes.util

import org.quartz.CronExpression
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.*

/**
 * Cron表达式工具类 (Cron Expression Utility)
 * 
 * 提供Cron表达式解析和下次执行时间计算功能
 */
object CronExpressionUtil {
    
    /**
     * 计算Cron表达式的下次执行时间
     * 
     * @param cronExpressionStr Cron表达式字符串
     * @param fromTime 计算起始时间，默认为当前时间
     * @return 下次执行时间，如果Cron表达式无效则返回null
     */
    fun calculateNextScheduleTime(
        cronExpressionStr: String?,
        fromTime: LocalDateTime = LocalDateTime.now()
    ): LocalDateTime? {
        if (cronExpressionStr.isNullOrBlank()) {
            return null
        }
        
        return try {
            val cronExpression = CronExpression(cronExpressionStr.trim())
            val fromDate = Date.from(fromTime.atZone(ZoneId.systemDefault()).toInstant())
            val nextFireTime = cronExpression.getNextValidTimeAfter(fromDate)
            
            nextFireTime?.toInstant()?.atZone(ZoneId.systemDefault())?.toLocalDateTime()
        } catch (e: Exception) {
            // 记录日志但不抛出异常，返回null表示无效的Cron表达式
            println("4a8c7f1d | 解析Cron表达式失败: $cronExpressionStr, 错误: ${e.message}")
            null
        }
    }
    
    /**
     * 验证Cron表达式是否有效
     * 
     * @param cronExpressionStr Cron表达式字符串
     * @return 是否为有效的Cron表达式
     */
    fun isValidCronExpression(cronExpressionStr: String?): Boolean {
        if (cronExpressionStr.isNullOrBlank()) {
            return false
        }
        
        return try {
            CronExpression(cronExpressionStr.trim())
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 计算Cron表达式在指定时间范围内的所有执行时间
     * 
     * @param cronExpressionStr Cron表达式字符串
     * @param fromTime 开始时间
     * @param toTime 结束时间
     * @param maxCount 最大返回数量，防止无限循环
     * @return 在时间范围内的所有执行时间列表
     */
    fun calculateScheduleTimesInRange(
        cronExpressionStr: String?,
        fromTime: LocalDateTime,
        toTime: LocalDateTime,
        maxCount: Int = 100
    ): List<LocalDateTime> {
        if (cronExpressionStr.isNullOrBlank() || fromTime.isAfter(toTime)) {
            return emptyList()
        }
        
        return try {
            val cronExpression = CronExpression(cronExpressionStr.trim())
            val result = mutableListOf<LocalDateTime>()
            
            var currentTime = fromTime
            val endDate = Date.from(toTime.atZone(ZoneId.systemDefault()).toInstant())
            
            repeat(maxCount) {
                val currentDate = Date.from(currentTime.atZone(ZoneId.systemDefault()).toInstant())
                val nextFireTime = cronExpression.getNextValidTimeAfter(currentDate)
                
                if (nextFireTime == null || nextFireTime.after(endDate)) {
                    return@repeat
                }
                
                val nextLocalDateTime = nextFireTime.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime()
                
                result.add(nextLocalDateTime)
                currentTime = nextLocalDateTime.plusSeconds(1) // 避免重复同一时间
            }
            
            result
        } catch (e: Exception) {
            println("9b2e5c8a | 计算Cron表达式时间范围失败: $cronExpressionStr, 错误: ${e.message}")
            emptyList()
        }
    }
}