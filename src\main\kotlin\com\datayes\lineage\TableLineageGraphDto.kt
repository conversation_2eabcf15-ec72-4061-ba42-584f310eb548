package com.datayes.lineage

import com.datayes.metadata.MetadataDataSourceDto
import java.math.BigDecimal

/**
 * 表级血缘图数据传输对象 (Table Lineage Graph DTOs)
 *
 * UC-09 & UC-10: 用于渲染表级血缘图和显示表详情的数据结构
 */

/**
 * 表血缘图 (Table Lineage Graph)
 *
 * 包含图的节点和边信息，用于前端可视化
 * UC-12 & UC-13: 支持搜索和系统分组功能
 */
data class TableLineageGraphDto(
    val nodes: List<TableNodeDto>,
    val edges: List<TableEdgeDto>,
    val metadata: LineageGraphMetadataDto,
    val systemGroups: List<SystemGroupDto> = emptyList(), // UC-13: 系统分组信息
    val searchMetadata: SearchMetadataDto? = null // UC-12: 搜索相关元数据
)

/**
 * 表节点DTO (Table Node DTO)
 *
 * 表示图中的表节点，包含表信息和元数据
 * UC-12 & UC-13: 支持搜索匹配和系统分组
 */
data class TableNodeDto(
    val id: String,                    // 节点唯一标识，格式: "table_{tableId}"
    val tableId: Long,                 // 数据库中的表ID
    val tableName: String,             // 表名
    val schema: String?,               // 模式名
    val datasource: String,            // 数据源名称
    val system: String?,               // 系统名称
    val chineseName: String?,          // 中文名称
    val nodeType: TableNodeType,       // 节点类型
    val level: Int,                    // 在血缘图中的层级
    val confidenceScore: BigDecimal?,  // 置信度分数
    val metadata: List<MetadataDataSourceDto> = emptyList(), // 关联的元数据
    val systemGroupId: String?,        // UC-13: 所属系统分组ID
    val searchScore: Double? = null,   // UC-12: 搜索匹配分数 (0.0-1.0)
    val highlightTerms: List<String> = emptyList() // UC-12: 高亮显示的搜索词
)

/**
 * 表边DTO (Table Edge DTO)
 *
 * 表示图中表与表之间的血缘关系
 */
data class TableEdgeDto(
    val id: String,                    // 边唯一标识，格式: "edge_{relationshipId}"
    val relationshipId: Long,          // 数据库中的关系ID
    val sourceNodeId: String,          // 源节点ID
    val targetNodeId: String,          // 目标节点ID
    val sourceTableId: Long,           // 源表ID
    val targetTableId: Long,           // 目标表ID
    val lineageType: String?,          // 血缘类型 (DIRECT_COPY, SQL_QUERY, etc.)
    val level: Int,                    // 关系层级
    val columnMappingCount: Int,       // 列映射数量
    val transformationType: String?,   // 主要转换类型
    val confidence: BigDecimal?        // 关系置信度
)

/**
 * 表节点类型枚举 (Table Node Type Enum)
 */
enum class TableNodeType {
    SOURCE,          // 源表
    TARGET,          // 目标表  
    INTERMEDIATE,    // 中间表
    ROOT             // 根表（查询的起始表）
}

/**
 * 血缘图元数据 (Lineage Graph Metadata)
 *
 * 包含图的整体信息和统计数据
 */
data class LineageGraphMetadataDto(
    val totalNodes: Int,               // 节点总数
    val totalEdges: Int,               // 边总数
    val maxLevels: Int,                // 最大层级数
    val rootTableId: Long,             // 根表ID
    val rootTableName: String,         // 根表名称
    val systemsInvolved: List<String>, // 涉及的系统列表
    val datasourcesInvolved: List<String>, // 涉及的数据源列表
    val lastUpdated: String,           // 最后更新时间
    val queryTimestamp: String         // 查询时间戳
)

/**
 * 表详情DTO (Table Details DTO)
 *
 * UC-10: 点击表节点时显示的详细信息
 */
data class TableDetailsDto(
    val tableId: Long,
    val tableName: String,
    val schema: String?,
    val datasource: LineageDatasourceDto,
    val description: String?,
    val lineageStatistics: LineageStatisticsDto,
    val metadata: List<MetadataDataSourceDto>,
    val upstreamCount: Int,            // 上游表数量
    val downstreamCount: Int,          // 下游表数量
    val columnCount: Int,              // 列数量
    val lastLineageUpdate: String?,    // 最后血缘更新时间
    val sourceType: String?,           // 血缘数据来源类型
    val syncFrequency: String?,        // 同步频率
    val requirementId: String?,        // 软开需求编号
    val dataSyncScope: String?         // 数据同步范围
)

/**
 * 血缘统计信息DTO (Lineage Statistics DTO)
 */
data class LineageStatisticsDto(
    val totalUpstreamTables: Int,      // 上游表总数
    val totalDownstreamTables: Int,    // 下游表总数
    val directUpstreamTables: Int,     // 直接上游表数
    val directDownstreamTables: Int,   // 直接下游表数
    val maxUpstreamLevels: Int,        // 最大上游层级
    val maxDownstreamLevels: Int,      // 最大下游层级
    val totalColumnMappings: Int,      // 总列映射数
    val systemsInvolved: Int           // 涉及系统数
)

/**
 * 血缘图查询参数 (Lineage Graph Query Parameters)
 */
data class LineageGraphQueryDto(
    val tableId: Long,
    val maxLevels: Int = 3,
    val includeUpstream: Boolean = true,
    val includeDownstream: Boolean = true,
    val filterBySystems: List<String> = emptyList(),
    val filterByDatasources: List<String> = emptyList(),
    val minConfidenceScore: BigDecimal? = null
)

/**
 * 从 TableLineageView 转换为 TableNodeDto
 */
fun TableLineageView.toTableNodeDto(
    nodeType: TableNodeType,
    confidenceScore: BigDecimal? = null
): Pair<TableNodeDto, TableNodeDto> {
    val sourceNode = TableNodeDto(
        id = "table_${this.sourceTableId}",
        tableId = this.sourceTableId,
        tableName = this.sourceTable,
        schema = this.sourceSchema,
        datasource = this.sourceDatasource,
        system = this.sourceSystem,
        chineseName = this.sourceChineseName,
        nodeType = if (nodeType == TableNodeType.ROOT && this.level == 1) TableNodeType.ROOT else TableNodeType.SOURCE,
        level = this.level,
        confidenceScore = confidenceScore,
        systemGroupId = generateSystemGroupId(this.sourceSystem)
    )

    val targetNode = TableNodeDto(
        id = "table_${this.targetTableId}",
        tableId = this.targetTableId,
        tableName = this.targetTable,
        schema = this.targetSchema,
        datasource = this.targetDatasource,
        system = null, // TableLineageView doesn't have targetSystem
        chineseName = null, // TableLineageView doesn't have targetChineseName
        nodeType = if (nodeType == TableNodeType.ROOT && this.level == 1) TableNodeType.ROOT else TableNodeType.TARGET,
        level = this.level,
        confidenceScore = confidenceScore,
        systemGroupId = generateSystemGroupId(null) // No target system available
    )

    return Pair(sourceNode, targetNode)
}

/**
 * 从 TableLineageView 转换为 TableEdgeDto
 */
fun TableLineageView.toTableEdgeDto(columnMappingCount: Int = 0, confidence: BigDecimal? = null): TableEdgeDto {
    return TableEdgeDto(
        id = "edge_${this.relationshipId}",
        relationshipId = this.relationshipId,
        sourceNodeId = "table_${this.sourceTableId}",
        targetNodeId = "table_${this.targetTableId}",
        sourceTableId = this.sourceTableId,
        targetTableId = this.targetTableId,
        lineageType = this.lineageType,
        level = this.level,
        columnMappingCount = columnMappingCount,
        transformationType = null, // 可以从列映射中推导主要转换类型
        confidence = confidence
    )
}

/**
 * 系统分组DTO (System Group DTO)
 *
 * UC-13: 用于在血缘图中按系统对节点进行分组显示
 */
data class SystemGroupDto(
    val id: String,                    // 分组唯一标识
    val systemName: String,            // 系统名称
    val systemCode: String?,           // 系统代码
    val displayName: String,           // 显示名称
    val description: String?,          // 系统描述
    val nodeIds: List<String>,         // 属于该分组的节点ID列表
    val color: String,                 // 分组颜色
    val position: GroupPositionDto?,   // 分组在图中的位置
    val metadata: SystemGroupMetadataDto // 分组统计信息
)

/**
 * 分组位置DTO (Group Position DTO)
 */
data class GroupPositionDto(
    val x: Double,                     // X坐标
    val y: Double,                     // Y坐标
    val width: Double,                 // 分组宽度
    val height: Double                 // 分组高度
)

/**
 * 系统分组元数据DTO (System Group Metadata DTO)
 */
data class SystemGroupMetadataDto(
    val nodeCount: Int,                // 分组内节点数量
    val tableCount: Int,               // 表数量
    val datasourceCount: Int,          // 数据源数量
    val avgConfidenceScore: BigDecimal?, // 平均置信度
    val totalRelationships: Int        // 关系总数
)

/**
 * 搜索元数据DTO (Search Metadata DTO)
 *
 * UC-12: 搜索功能的元数据信息
 */
data class SearchMetadataDto(
    val query: String,                 // 搜索查询
    val totalMatches: Int,             // 匹配总数
    val searchType: SearchType,        // 搜索类型
    val searchFields: List<String>,    // 搜索字段
    val suggestions: List<String> = emptyList(), // 搜索建议
    val searchTime: Long               // 搜索耗时（毫秒）
)

/**
 * 搜索类型枚举 (Search Type Enum)
 */
enum class SearchType {
    EXACT_MATCH,         // 精确匹配
    FUZZY_MATCH,         // 模糊匹配
    WILDCARD_MATCH,      // 通配符匹配
    REGEX_MATCH          // 正则匹配
}

/**
 * 图搜索请求DTO (Graph Search Request DTO)
 *
 * UC-12: 图搜索功能的请求参数
 */
data class GraphSearchRequestDto(
    val query: String,                 // 搜索查询
    val searchType: SearchType = SearchType.FUZZY_MATCH,
    val searchFields: List<String> = listOf("tableName", "chineseName"), // 搜索字段
    val caseSensitive: Boolean = false, // 是否区分大小写
    val maxResults: Int = 100,         // 最大结果数
    val includeSystemGroups: Boolean = true, // 是否包含系统分组
    val highlightMatches: Boolean = true // 是否高亮匹配项
)

/**
 * 生成系统分组ID的辅助函数 (Helper function to generate system group ID)
 */
private fun generateSystemGroupId(systemName: String?): String {
    return "group_${(systemName ?: "unknown").hashCode().toString().replace("-", "")}"
}