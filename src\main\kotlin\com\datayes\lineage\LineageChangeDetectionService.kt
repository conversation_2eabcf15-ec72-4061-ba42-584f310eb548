package com.datayes.lineage

import org.springframework.stereotype.Service

/**
 * 血缘变更检测服务 (Lineage Change Detection Service)
 */
@Service
class LineageChangeDetectionService(private val processingHistoryRepository: JobProcessingHistoryRepository) {

    private val logger = org.slf4j.LoggerFactory.getLogger(this.javaClass)

    /**
     * 检测血缘是否发生变更
     * @param jobKey 作业标识
     * @param newLineage 新的血缘信息
     * @return 变更检测结果
     */
    fun detectChanges(jobKey: String, newLineage: DataLineage): ChangeDetectionResult {
        val newHash = LineageHashCalculator.calculateHash(newLineage)

        // 查询最近一次处理记录
        val lastProcessing = processingHistoryRepository.findLatestByJobKey(jobKey)

        logger.info("Detecting changes for job: $jobKey")
        logger.debug("New lineage hash: $newHash")
        logger.debug("Previous hash: ${lastProcessing?.lineageHash}")

        return if (lastProcessing?.lineageHash == newHash) {
            // 无变更
            logger.info("No changes detected for job: $jobKey")
            ChangeDetectionResult(
                hasChanges = false,
                currentHash = newHash,
                previousHash = lastProcessing.lineageHash
            )
        } else {
            // 有变更
            logger.info("Changes detected for job: $jobKey")
            ChangeDetectionResult(
                hasChanges = true,
                currentHash = newHash,
                previousHash = lastProcessing?.lineageHash
            )
        }
    }
}

/**
 * 变更检测结果 (Change Detection Result)
 */
data class ChangeDetectionResult(
    val hasChanges: Boolean,
    val currentHash: String,
    val previousHash: String?
)