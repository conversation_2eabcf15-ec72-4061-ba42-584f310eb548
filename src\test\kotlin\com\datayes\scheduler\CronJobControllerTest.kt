package com.datayes.scheduler

import com.fasterxml.jackson.databind.ObjectMapper
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.*
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.*
import org.springframework.test.web.servlet.setup.MockMvcBuilders
import java.time.LocalDateTime

/**
 * CronJobController 单元测试
 */
@DisplayName("CronJobController 测试")
class CronJobControllerTest {

    @Mock
    private lateinit var cronJobSchedulerService: CronJobSchedulerService

    private lateinit var cronJobController: CronJobController
    private lateinit var mockMvc: MockMvc
    private lateinit var objectMapper: ObjectMapper

    @BeforeEach
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        cronJobController = CronJobController(cronJobSchedulerService)
        mockMvc = MockMvcBuilders.standaloneSetup(cronJobController).build()
        objectMapper = ObjectMapper()
    }

    @Test
    @DisplayName("应该成功获取所有定时任务列表")
    fun `should successfully get all scheduled tasks`() {
        // Given
        val scheduledTasks = listOf(
            ScheduledTaskInfo(
                systemId = 1L,
                systemName = "数据交互平台",
                systemCode = "DATA_EXCHANGE_PLATFORM",
                cronExpression = "0 0 2 * * ?",
                status = "SCHEDULED",
                nextExecutionTime = LocalDateTime.of(2025, 6, 24, 2, 0, 0)
            ),
            ScheduledTaskInfo(
                systemId = 2L,
                systemName = "外部系统",
                systemCode = "EXTERNAL_SYSTEM",
                cronExpression = "0 0 6 1 * ?",
                status = "SCHEDULED",
                nextExecutionTime = LocalDateTime.of(2025, 7, 1, 6, 0, 0)
            )
        )

        whenever(cronJobSchedulerService.getScheduledTasksInfo()).thenReturn(scheduledTasks)

        // When & Then
        mockMvc.perform(get("/api/v1/cron-jobs"))
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.message").value("获取定时任务列表成功"))
            .andExpect(jsonPath("$.data").isArray)
            .andExpect(jsonPath("$.data.length()").value(2))
            .andExpect(jsonPath("$.data[0].systemId").value(1))
            .andExpect(jsonPath("$.data[0].systemName").value("数据交互平台"))
            .andExpect(jsonPath("$.data[0].cronExpression").value("0 0 2 * * ?"))
            .andExpect(jsonPath("$.data[0].status").value("SCHEDULED"))
            .andExpect(jsonPath("$.data[1].systemId").value(2))
            .andExpect(jsonPath("$.data[1].systemName").value("外部系统"))

        verify(cronJobSchedulerService).getScheduledTasksInfo()
    }

    @Test
    @DisplayName("获取定时任务列表时发生异常应该返回错误响应")
    fun `should return error response when getting scheduled tasks fails`() {
        // Given
        whenever(cronJobSchedulerService.getScheduledTasksInfo())
            .thenThrow(RuntimeException("获取任务列表失败"))

        // When & Then
        mockMvc.perform(get("/api/v1/cron-jobs"))
            .andExpect(status().isInternalServerError)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(false))
            .andExpect(jsonPath("$.message").value("获取定时任务列表失败: 获取任务列表失败"))

        verify(cronJobSchedulerService).getScheduledTasksInfo()
    }

    @Test
    @DisplayName("应该成功重新调度系统任务")
    fun `should successfully reschedule system task`() {
        // Given
        val systemId = 1L
        doNothing().whenever(cronJobSchedulerService).rescheduleSystemTask(systemId)

        // When & Then
        mockMvc.perform(post("/api/v1/cron-jobs/systems/{systemId}/reschedule", systemId))
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.message").value("系统任务重新调度成功"))
            .andExpect(jsonPath("$.data").value("SUCCESS"))

        verify(cronJobSchedulerService).rescheduleSystemTask(systemId)
    }

    @Test
    @DisplayName("重新调度系统任务失败应该返回错误响应")
    fun `should return error response when rescheduling system task fails`() {
        // Given
        val systemId = 1L
        whenever(cronJobSchedulerService.rescheduleSystemTask(systemId))
            .thenThrow(RuntimeException("重新调度失败"))

        // When & Then
        mockMvc.perform(post("/api/v1/cron-jobs/systems/{systemId}/reschedule", systemId))
            .andExpect(status().isInternalServerError)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(false))
            .andExpect(jsonPath("$.message").value("系统任务重新调度失败: 重新调度失败"))

        verify(cronJobSchedulerService).rescheduleSystemTask(systemId)
    }

    @Test
    @DisplayName("应该成功取消系统任务")
    fun `should successfully cancel system task`() {
        // Given
        val systemId = 1L
        doNothing().whenever(cronJobSchedulerService).cancelSystemTask(systemId)

        // When & Then
        mockMvc.perform(delete("/api/v1/cron-jobs/systems/{systemId}", systemId))
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.message").value("系统任务取消成功"))
            .andExpect(jsonPath("$.data").value("SUCCESS"))

        verify(cronJobSchedulerService).cancelSystemTask(systemId)
    }

    @Test
    @DisplayName("取消系统任务失败应该返回错误响应")
    fun `should return error response when canceling system task fails`() {
        // Given
        val systemId = 1L
        whenever(cronJobSchedulerService.cancelSystemTask(systemId))
            .thenThrow(RuntimeException("取消任务失败"))

        // When & Then
        mockMvc.perform(delete("/api/v1/cron-jobs/systems/{systemId}", systemId))
            .andExpect(status().isInternalServerError)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(false))
            .andExpect(jsonPath("$.message").value("系统任务取消失败: 取消任务失败"))

        verify(cronJobSchedulerService).cancelSystemTask(systemId)
    }

    @Test
    @DisplayName("应该成功重新初始化所有定时任务")
    fun `should successfully reinitialize all scheduled tasks`() {
        // Given
        doNothing().whenever(cronJobSchedulerService).initializeScheduledTasks()

        // When & Then
        mockMvc.perform(post("/api/v1/cron-jobs/reinitialize"))
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.message").value("定时任务重新初始化成功"))
            .andExpect(jsonPath("$.data").value("SUCCESS"))

        verify(cronJobSchedulerService).initializeScheduledTasks()
    }

    @Test
    @DisplayName("重新初始化定时任务失败应该返回错误响应")
    fun `should return error response when reinitializing scheduled tasks fails`() {
        // Given
        whenever(cronJobSchedulerService.initializeScheduledTasks())
            .thenThrow(RuntimeException("初始化失败"))

        // When & Then
        mockMvc.perform(post("/api/v1/cron-jobs/reinitialize"))
            .andExpect(status().isInternalServerError)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(false))
            .andExpect(jsonPath("$.message").value("定时任务重新初始化失败: 初始化失败"))

        verify(cronJobSchedulerService).initializeScheduledTasks()
    }

    @Test
    @DisplayName("应该正确处理无效的系统ID参数")
    fun `should handle invalid system id parameter correctly`() {
        // Given - 使用非数字的系统ID
        
        // When & Then
        mockMvc.perform(post("/api/v1/cron-jobs/systems/invalid/reschedule"))
            .andExpect(status().isBadRequest)

        // 验证服务方法没有被调用
        verify(cronJobSchedulerService, never()).rescheduleSystemTask(any())
    }

    @Test
    @DisplayName("应该正确处理跨域请求")
    fun `should handle cross-origin requests correctly`() {
        // Given
        val scheduledTasks = emptyList<ScheduledTaskInfo>()
        whenever(cronJobSchedulerService.getScheduledTasksInfo()).thenReturn(scheduledTasks)

        // When & Then
        mockMvc.perform(get("/api/v1/cron-jobs")
            .header("Origin", "http://localhost:3000"))
            .andExpect(status().isOk)
            .andExpect(header().string("Access-Control-Allow-Origin", "*"))

        verify(cronJobSchedulerService).getScheduledTasksInfo()
    }

    @Test
    @DisplayName("应该返回空列表当没有定时任务时")
    fun `should return empty list when no scheduled tasks exist`() {
        // Given
        val emptyScheduledTasks = emptyList<ScheduledTaskInfo>()
        whenever(cronJobSchedulerService.getScheduledTasksInfo()).thenReturn(emptyScheduledTasks)

        // When & Then
        mockMvc.perform(get("/api/v1/cron-jobs"))
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.message").value("获取定时任务列表成功"))
            .andExpect(jsonPath("$.data").isArray)
            .andExpect(jsonPath("$.data.length()").value(0))

        verify(cronJobSchedulerService).getScheduledTasksInfo()
    }

    @Test
    @DisplayName("应该正确处理大量定时任务")
    fun `should handle large number of scheduled tasks correctly`() {
        // Given
        val scheduledTasks = (1..100).map { index ->
            ScheduledTaskInfo(
                systemId = index.toLong(),
                systemName = "系统$index",
                systemCode = "SYSTEM_$index",
                cronExpression = "0 0 $index * * ?",
                status = "SCHEDULED",
                nextExecutionTime = LocalDateTime.of(2025, 6, 24, index % 24, 0, 0)
            )
        }

        whenever(cronJobSchedulerService.getScheduledTasksInfo()).thenReturn(scheduledTasks)

        // When & Then
        mockMvc.perform(get("/api/v1/cron-jobs"))
            .andExpect(status().isOk)
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.success").value(true))
            .andExpect(jsonPath("$.data").isArray)
            .andExpect(jsonPath("$.data.length()").value(100))
            .andExpect(jsonPath("$.data[0].systemId").value(1))
            .andExpect(jsonPath("$.data[99].systemId").value(100))

        verify(cronJobSchedulerService).getScheduledTasksInfo()
    }
}