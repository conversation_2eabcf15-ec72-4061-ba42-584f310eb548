package com.datayes.spring

import jakarta.annotation.PostConstruct
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.web.bind.annotation.CrossOrigin
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.net.InetAddress
import java.net.UnknownHostException
import java.time.LocalDateTime
import java.util.UUID
import kotlin.random.Random

/**
 * 心跳检测控制器 (Heartbeat controller)
 */
@Tag(name = "Heartbeat", description = "心跳检测接口")
@RequestMapping
@RestController
@CrossOrigin(origins = ["*"])
class HeartbeatController {

    private val log = LoggerFactory.getLogger(HeartbeatController::class.java)

    companion object {
        var SELF_IDENTITY = "undefined_${LocalDateTime.now()}"
    }

    @PostConstruct
    fun init() {
        val builder = StringBuilder()
        try {
            // 获取本机的InetAddress实例 (Get local InetAddress instance)
            val address = InetAddress.getLocalHost()
            // 获取机器名称 (Get machine hostname)
            builder.append(address.hostName)
        } catch (e: UnknownHostException) {
            builder.append(UUID.randomUUID().toString().replace("-", ""))
            log.error("fail to get host name, use UUID:", e)
        }
        builder.append("_${LocalDateTime.now()}_${Random.Default.nextInt(100000, 999999)}")
        SELF_IDENTITY = builder.toString()
        log.info("SELF_IDENTITY inited {}", SELF_IDENTITY)
    }

    @Operation(summary = "心跳检测", description = "多路径心跳检测接口，返回应用实例标识")
    // @GetMapping("/", "/api/heartbeat", "/heartbeat", "/monitor/heartbeat")
    @GetMapping("/")
    fun heartbeat(): String {
        log.debug("get heartbeat")
        return "Hello, this is dgp-issue-executor from $SELF_IDENTITY"
    }

    @Operation(summary = "健康检查", description = "简易健康检查接口，返回固定字符串 ok")
    // @GetMapping("/check")
    fun check(): String {
        log.info("get heartbeat check")
        return "ok"
    }
}