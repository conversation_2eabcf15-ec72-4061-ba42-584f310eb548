package com.datayes.util

import org.slf4j.LoggerFactory

/**
 * JDBC URL解析工具 (JDBC URL Parser Utility)
 * 
 * 支持解析各种数据库的JDBC URL，提取主机、端口等信息
 */
object JdbcUrlParser {
    
    private val logger = LoggerFactory.getLogger(JdbcUrlParser::class.java)
    
    /**
     * JDBC URL解析结果 (JDBC URL Parse Result)
     */
    data class JdbcUrlParseResult(
        val host: String?,
        val port: Int?,
        val database: String?,
        val hosts: List<HostPortPair> = emptyList() // 支持多主机配置
    )
    
    /**
     * 主机端口对 (Host Port Pair)
     */
    data class HostPortPair(
        val host: String,
        val port: Int?
    )
    
    /**
     * 解析JDBC URL (Parse JDBC URL)
     * 
     * 支持的格式：
     * - MySQL: *******************************
     * - Hive2: ************************,host2:port2/database
     * - Oracle: *******************************
     * - PostgreSQL: ************************************
     * 
     * @param jdbcUrl JDBC URL字符串
     * @return 解析结果，如果解析失败返回空值
     */
    fun parseJdbcUrl(jdbcUrl: String?): JdbcUrlParseResult? {
        if (jdbcUrl.isNullOrBlank()) {
            return null
        }
        
        return try {
            logger.debug("f7e2b9c4 | 开始解析JDBC URL: $jdbcUrl")
            
            when {
                jdbcUrl.startsWith("jdbc:mysql://") -> parseMySqlUrl(jdbcUrl)
                jdbcUrl.startsWith("jdbc:hive2://") -> parseHive2Url(jdbcUrl)
                jdbcUrl.startsWith("jdbc:oracle:") -> parseOracleUrl(jdbcUrl)
                jdbcUrl.startsWith("jdbc:postgresql://") -> parsePostgreSqlUrl(jdbcUrl)
                else -> {
                    logger.warn("d3a8f5b1 | 不支持的JDBC URL格式: $jdbcUrl")
                    null
                }
            }
        } catch (e: Exception) {
            logger.error("k9m2n6o7 | 解析JDBC URL时发生错误: $jdbcUrl", e)
            null
        }
    }
    
    /**
     * 解析MySQL JDBC URL
     * 格式: *******************************[?params]
     */
    private fun parseMySqlUrl(jdbcUrl: String): JdbcUrlParseResult? {
        val pattern = Regex("jdbc:mysql://([^:/]+)(?::(\\d+))?/([^?]+)")
        val matchResult = pattern.find(jdbcUrl)
        
        return if (matchResult != null) {
            val host = matchResult.groupValues[1]
            val port = matchResult.groupValues[2].toIntOrNull() ?: 3306
            val database = matchResult.groupValues[3]
            
            JdbcUrlParseResult(
                host = host,
                port = port,
                database = database,
                hosts = listOf(HostPortPair(host, port))
            )
        } else {
            logger.warn("p4q7r1s8 | MySQL JDBC URL格式不正确: $jdbcUrl")
            null
        }
    }
    
    /**
     * 解析Hive2 JDBC URL
     * 格式: ************************,host2:port2/database[;params]
     */
    private fun parseHive2Url(jdbcUrl: String): JdbcUrlParseResult? {
        // 移除jdbc:hive2://前缀
        val urlWithoutPrefix = jdbcUrl.removePrefix("jdbc:hive2://")
        
        // 分离主机部分和数据库部分
        val parts = urlWithoutPrefix.split("/", limit = 2)
        if (parts.isEmpty()) {
            logger.warn("t5u9v2w6 | Hive2 JDBC URL格式不正确: $jdbcUrl")
            return null
        }
        
        val hostPart = parts[0]
        val databasePart = if (parts.size > 1) {
            // 移除参数部分（;开头的部分）
            parts[1].split(";")[0].trim()
        } else {
            null
        }
        
        // 解析多个主机
        val hosts = mutableListOf<HostPortPair>()
        val hostEntries = hostPart.split(",")
        
        for (hostEntry in hostEntries) {
            val hostPortParts = hostEntry.trim().split(":")
            val host = hostPortParts[0].trim()
            val port = if (hostPortParts.size > 1) {
                hostPortParts[1].toIntOrNull() ?: 10000
            } else {
                10000
            }
            
            if (host.isNotBlank()) {
                hosts.add(HostPortPair(host, port))
            }
        }
        
        // 返回第一个主机作为主要主机
        val primaryHost = hosts.firstOrNull()
        
        return if (primaryHost != null) {
            JdbcUrlParseResult(
                host = primaryHost.host,
                port = primaryHost.port,
                database = databasePart,
                hosts = hosts
            )
        } else {
            logger.warn("x3y7z1a4 | 无法从Hive2 JDBC URL中解析主机信息: $jdbcUrl")
            null
        }
    }
    
    /**
     * 解析Oracle JDBC URL
     * 格式: ******************************* 或 *************************************
     */
    private fun parseOracleUrl(jdbcUrl: String): JdbcUrlParseResult? {
        // 支持两种格式
        val thinPattern1 = Regex("jdbc:oracle:thin:@([^:/]+):(\\d+):([^?]+)")
        val thinPattern2 = Regex("jdbc:oracle:thin:@//([^:/]+):(\\d+)/([^?]+)")
        
        val matchResult = thinPattern1.find(jdbcUrl) ?: thinPattern2.find(jdbcUrl)
        
        return if (matchResult != null) {
            val host = matchResult.groupValues[1]
            val port = matchResult.groupValues[2].toIntOrNull() ?: 1521
            val database = matchResult.groupValues[3]
            
            JdbcUrlParseResult(
                host = host,
                port = port,
                database = database,
                hosts = listOf(HostPortPair(host, port))
            )
        } else {
            logger.warn("b8c2d6e9 | Oracle JDBC URL格式不正确: $jdbcUrl")
            null
        }
    }
    
    /**
     * 解析PostgreSQL JDBC URL
     * 格式: ************************************[?params]
     */
    private fun parsePostgreSqlUrl(jdbcUrl: String): JdbcUrlParseResult? {
        val pattern = Regex("jdbc:postgresql://([^:/]+)(?::(\\d+))?/([^?]+)")
        val matchResult = pattern.find(jdbcUrl)
        
        return if (matchResult != null) {
            val host = matchResult.groupValues[1]
            val port = matchResult.groupValues[2].toIntOrNull() ?: 5432
            val database = matchResult.groupValues[3]
            
            JdbcUrlParseResult(
                host = host,
                port = port,
                database = database,
                hosts = listOf(HostPortPair(host, port))
            )
        } else {
            logger.warn("f1g5h9i3 | PostgreSQL JDBC URL格式不正确: $jdbcUrl")
            null
        }
    }
}