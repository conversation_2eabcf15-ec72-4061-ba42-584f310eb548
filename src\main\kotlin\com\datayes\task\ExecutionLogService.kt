package com.datayes.task

import com.datayes.dataexchange.DataExchangeJob
import com.datayes.hdfs.HdfsShellScriptJob
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import java.util.*

/**
 * 执行日志服务 (Execution Log Service)
 *
 * 负责处理LineageTask执行过程中的日志记录，包括任务开始、完成、失败等各个阶段的日志记录
 * 提供任务执行情况的查询和统计功能
 */
@Service
@Transactional
class ExecutionLogService(
    private val executionLogRepository: ExecutionLogRepository,
    private val executionLogCustomRepository: ExecutionLogCustomRepository,
    private val lineageTaskRepository: LineageTaskRepository
) {

    private val logger = LoggerFactory.getLogger(ExecutionLogService::class.java)

    /**
     * 记录任务开始执行 (Log task execution start)
     */
    fun logTaskExecutionStart(
        taskId: Long,
        executionId: String,
        job: Any? = null
    ): Long {
        logger.info("8a9b2c3d | 开始记录任务执行日志: taskId=$taskId, executionId=$executionId")

        val logRequest = when (job) {
            is DataExchangeJob -> createDataExchangeJobStartLog(taskId, executionId, job)
            is HdfsShellScriptJob -> createHdfsShellScriptJobStartLog(taskId, executionId, job)
            else -> createGenericStartLog(taskId, executionId)
        }

        val logId = executionLogCustomRepository.createExecutionLog(logRequest)
        logger.info("1f4e7a8b | 任务执行开始日志已创建: logId=$logId, taskId=$taskId, executionId=$executionId")
        
        return logId
    }

    /**
     * 记录任务执行完成 (Log task execution completion)
     */
    fun logTaskExecutionCompletion(
        executionId: String,
        taskStatus: TaskStatus,
        processingTimeMs: Long,
        errorMessage: String? = null,
        additionalInfo: Map<String, Any>? = null
    ) {
        logger.info("5d8f2e9a | 记录任务执行完成: executionId=$executionId, status=$taskStatus, time=${processingTimeMs}ms")

        val updateRequest = UpdateExecutionLogRequest(
            logMessage = createCompletionMessage(taskStatus, processingTimeMs, errorMessage),
            completedAt = LocalDateTime.now(),
            taskStatus = taskStatus,
            processingTimeMs = processingTimeMs,
            exceptionStack = if (taskStatus == TaskStatus.FAILED) errorMessage else null,
            additionalInfo = additionalInfo
        )

        val updated = executionLogCustomRepository.updateExecutionLogByExecutionId(executionId, updateRequest)
        
        if (updated) {
            logger.info("6c3b7f2d | 任务执行完成日志已更新: executionId=$executionId")
        } else {
            logger.warn("4a1d8e5c | 未找到执行日志记录: executionId=$executionId")
        }
    }

    /**
     * 记录任务执行步骤 (Log task execution step)
     */
    fun logTaskExecutionStep(
        taskId: Long,
        executionId: String,
        step: String,
        message: String,
        logLevel: LogLevel = LogLevel.INFO
    ) {
        logger.debug("9e2f5a7c | 记录任务执行步骤: taskId=$taskId, step=$step, message=$message")

        val logRequest = CreateExecutionLogRequest(
            taskId = taskId,
            executionId = executionId,
            logLevel = logLevel,
            logMessage = message,
            executionStep = step
        )

        executionLogCustomRepository.createExecutionLog(logRequest)
    }

    /**
     * 获取任务的最新执行日志 (Get latest execution log for task)
     */
    fun getLatestExecutionLog(taskId: Long): ExecutionLogResponse? {
        logger.debug("7b4f8e1a | 查询任务最新执行日志: taskId=$taskId")

        val log = executionLogRepository.findLatestByTaskId(taskId)
        return log?.let { ExecutionLogResponse.fromExecutionLog(it) }
    }

    /**
     * 获取任务的详细执行信息 (Get detailed task execution information)
     */
    fun getTaskExecutionInfo(taskId: Long): TaskLatestExecutionLogResponse? {
        logger.debug("3a6c9d2b | 查询任务详细执行信息: taskId=$taskId")

        // 获取任务基本信息
        val task = lineageTaskRepository.findById(taskId).orElse(null) ?: return null
        
        // 获取执行信息
        val executionInfo = executionLogCustomRepository.getTaskLatestExecutionInfo(taskId)
        
        return if (executionInfo != null) {
            TaskLatestExecutionLogResponse(
                taskId = taskId,
                taskName = task.taskName,
                taskType = task.taskType.name,
                latestExecution = executionInfo.latestExecution?.let { ExecutionLogResponse.fromExecutionLog(it) },
                executionCount = executionInfo.executionCount,
                lastSuccessExecution = executionInfo.lastSuccessExecution?.let { ExecutionLogResponse.fromExecutionLog(it) },
                lastFailedExecution = executionInfo.lastFailedExecution?.let { ExecutionLogResponse.fromExecutionLog(it) }
            )
        } else {
            // 如果没有执行日志，返回基本信息
            TaskLatestExecutionLogResponse(
                taskId = taskId,
                taskName = task.taskName,
                taskType = task.taskType.name,
                latestExecution = null,
                executionCount = 0,
                lastSuccessExecution = null,
                lastFailedExecution = null
            )
        }
    }

    /**
     * 获取任务的所有执行日志 (Get all execution logs for task)
     */
    fun getTaskExecutionLogs(taskId: Long): List<ExecutionLogResponse> {
        logger.debug("2e5b8f4a | 查询任务所有执行日志: taskId=$taskId")

        val logs = executionLogRepository.findAllByTaskIdOrderByCreatedAtDesc(taskId)
        return logs.map { ExecutionLogResponse.fromExecutionLog(it) }
    }

    /**
     * 创建数据交换作业开始日志 (Create data exchange job start log)
     */
    private fun createDataExchangeJobStartLog(
        taskId: Long,
        executionId: String,
        job: DataExchangeJob
    ): CreateExecutionLogRequest {
        val sqlQueries = listOfNotNull(job.readerSql.takeIf { it.isNotBlank() })
        val additionalInfo = mapOf(
            "reader_job_id" to job.readerJobId,
            "reader_job_name" to job.readerJobName,
            "write_job_id" to job.writeJobId,
            "write_job_name" to job.writeJobName,
            "reader_table_name" to job.readerTableName,
            "writer_table_name" to job.writerTableName,
            "column_count" to job.columns.size
        )

        return CreateExecutionLogRequest(
            taskId = taskId,
            executionId = executionId,
            logLevel = LogLevel.INFO,
            logMessage = "开始执行数据交换任务: ${job.readerJobName} -> ${job.writeJobName}",
            executionStep = "TASK_START",
            startedAt = LocalDateTime.now(),
            taskStatus = TaskStatus.RUNNING,
            databaseConnectionString = "${job.dbReader} -> ${job.dbWriter}",
            sqlQueries = sqlQueries,
            sourceJobId = "${job.readerJobId}_${job.writeJobId}",
            sourceJobType = SourceJobType.DATA_EXCHANGE_JOB,
            additionalInfo = additionalInfo
        )
    }

    /**
     * 创建HDFS Shell脚本作业开始日志 (Create HDFS shell script job start log)
     */
    private fun createHdfsShellScriptJobStartLog(
        taskId: Long,
        executionId: String,
        job: HdfsShellScriptJob
    ): CreateExecutionLogRequest {
        val additionalInfo = mapOf(
            "zip_file_path" to job.zipFilePath,
            "script_name" to job.scriptName,
            "script_size_bytes" to job.scriptSizeBytes,
            "extracted_at" to job.extractedAt.toString(),
            "last_modified" to job.lastModified.toString(),
            "status" to job.status.name
        )

        return CreateExecutionLogRequest(
            taskId = taskId,
            executionId = executionId,
            logLevel = LogLevel.INFO,
            logMessage = "开始执行HDFS Shell脚本任务: ${job.jobName}",
            executionStep = "TASK_START",
            startedAt = LocalDateTime.now(),
            taskStatus = TaskStatus.RUNNING,
            databaseConnectionString = job.zipFilePath,
            sourceJobId = job.jobId,
            sourceJobType = SourceJobType.HDFS_SHELL_SCRIPT_JOB,
            additionalInfo = additionalInfo
        )
    }

    /**
     * 创建通用开始日志 (Create generic start log)
     */
    private fun createGenericStartLog(
        taskId: Long,
        executionId: String
    ): CreateExecutionLogRequest {
        return CreateExecutionLogRequest(
            taskId = taskId,
            executionId = executionId,
            logLevel = LogLevel.INFO,
            logMessage = "开始执行血缘任务",
            executionStep = "TASK_START",
            startedAt = LocalDateTime.now(),
            taskStatus = TaskStatus.RUNNING
        )
    }

    /**
     * 创建完成消息 (Create completion message)
     */
    private fun createCompletionMessage(
        taskStatus: TaskStatus,
        processingTimeMs: Long,
        errorMessage: String?
    ): String {
        return when (taskStatus) {
            TaskStatus.SUCCESS -> "任务执行成功，耗时 ${processingTimeMs}ms"
            TaskStatus.FAILED -> "任务执行失败，耗时 ${processingTimeMs}ms，错误: ${errorMessage ?: "未知错误"}"
            TaskStatus.CANCELLED -> "任务已取消，耗时 ${processingTimeMs}ms"
            else -> "任务状态: $taskStatus，耗时 ${processingTimeMs}ms"
        }
    }
}