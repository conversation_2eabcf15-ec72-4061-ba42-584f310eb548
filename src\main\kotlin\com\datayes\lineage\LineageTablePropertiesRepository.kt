package com.datayes.lineage

import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.jdbc.core.RowMapper
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

/**
 * 血缘表属性数据访问层 (Lineage Table Properties Data Access Layer)
 * 
 * 负责血缘表属性信息的持久化和查询操作，直接操作lineage_tables表
 */
@Repository
class LineageTablePropertiesRepository(private val jdbcTemplate: JdbcTemplate) {

    /**
     * 根据血缘表ID查询表信息和属性
     * 
     * @param tableId 血缘表ID
     * @return 血缘表信息，如果不存在则返回null
     */
    fun findById(tableId: Long): LineageTableInfo? {
        val sql = """
            SELECT id, datasource_id, schema_name, table_name, table_type, chinese_name, 
                   description, sync_frequency, requirement_id, data_sync_scope, status,
                   created_at, updated_at
            FROM lineage_tables
            WHERE id = ?
        """.trimIndent()

        return try {
            jdbcTemplate.queryForObject(sql, lineageTableInfoRowMapper, tableId)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 根据查询条件搜索血缘表信息
     * 
     * @param request 查询请求参数
     * @return 匹配的血缘表信息列表
     */
    fun findByQuery(request: QueryLineageTablePropertiesRequest): List<LineageTableInfo> {
        val conditions = mutableListOf<String>()
        val parameters = mutableListOf<Any>()

        // 默认只查询活跃状态的表
        conditions.add("status = 'ACTIVE'")

        request.tableId?.let {
            conditions.add("id = ?")
            parameters.add(it)
        }

        request.tableName?.let {
            conditions.add("table_name LIKE ?")
            parameters.add("%$it%")
        }

        request.schemaName?.let {
            conditions.add("schema_name LIKE ?")
            parameters.add("%$it%")
        }

        request.syncFrequency?.let {
            conditions.add("sync_frequency LIKE ?")
            parameters.add("%$it%")
        }

        request.requirementId?.let {
            conditions.add("requirement_id LIKE ?")
            parameters.add("%$it%")
        }

        request.datasourceId?.let {
            conditions.add("datasource_id = ?")
            parameters.add(it)
        }

        val whereClause = "WHERE ${conditions.joinToString(" AND ")}"

        val sql = """
            SELECT id, datasource_id, schema_name, table_name, table_type, chinese_name, 
                   description, sync_frequency, requirement_id, data_sync_scope, status,
                   created_at, updated_at
            FROM lineage_tables
            $whereClause
            ORDER BY updated_at DESC
        """.trimIndent()

        return jdbcTemplate.query(sql, lineageTableInfoRowMapper, *parameters.toTypedArray())
    }

    /**
     * 更新血缘表属性信息
     * 
     * @param tableId 血缘表ID
     * @param command 更新命令
     * @return 更新成功返回true，记录不存在返回false
     */
    @Transactional
    fun updateProperties(tableId: Long, command: UpdateLineageTablePropertiesCommand): Boolean {
        val updates = mutableListOf<String>()
        val parameters = mutableListOf<Any?>()

        // 构建SET子句
        command.syncFrequency?.let {
            updates.add("sync_frequency = ?")
            parameters.add(it)
        }

        command.dataSyncScope?.let {
            updates.add("data_sync_scope = ?")
            parameters.add(it)
        }

        command.requirementId?.let {
            updates.add("requirement_id = ?")
            parameters.add(it)
        }

        if (updates.isEmpty()) {
            return false // 没有要更新的字段
        }

        updates.add("updated_at = ?")
        parameters.add(LocalDateTime.now())
        parameters.add(tableId)

        val sql = """
            UPDATE lineage_tables 
            SET ${updates.joinToString(", ")}
            WHERE id = ?
        """.trimIndent()

        val rowsAffected = jdbcTemplate.update(sql, *parameters.toTypedArray())
        return rowsAffected > 0
    }

    /**
     * 检查血缘表ID是否存在
     * 
     * @param tableId 血缘表ID
     * @return 存在返回true，不存在返回false
     */
    fun existsTable(tableId: Long): Boolean {
        val sql = "SELECT COUNT(*) FROM lineage_tables WHERE id = ?"
        val count = jdbcTemplate.queryForObject(sql, Int::class.java, tableId) ?: 0
        return count > 0
    }

    /**
     * 获取血缘表属性统计信息
     * 
     * @return 统计信息映射
     */
    fun getStatistics(): Map<String, Any> {
        val totalRecords = jdbcTemplate.queryForObject(
            "SELECT COUNT(*) FROM lineage_tables WHERE status = 'ACTIVE'",
            Int::class.java
        ) ?: 0

        val syncFrequencyStats = jdbcTemplate.query(
            """
            SELECT sync_frequency, COUNT(*) as count
            FROM lineage_tables
            WHERE sync_frequency IS NOT NULL AND status = 'ACTIVE'
            GROUP BY sync_frequency
            ORDER BY count DESC
            """.trimIndent()
        ) { rs, _ ->
            rs.getString("sync_frequency") to rs.getInt("count")
        }.toMap()

        val requirementIdStats = jdbcTemplate.queryForObject(
            "SELECT COUNT(*) FROM lineage_tables WHERE requirement_id IS NOT NULL AND status = 'ACTIVE'",
            Int::class.java
        ) ?: 0

        val dataSyncScopeStats = jdbcTemplate.queryForObject(
            "SELECT COUNT(*) FROM lineage_tables WHERE data_sync_scope IS NOT NULL AND status = 'ACTIVE'",
            Int::class.java
        ) ?: 0

        val recentUpdates = jdbcTemplate.query(
            """
            SELECT id, table_name, schema_name, updated_at
            FROM lineage_tables
            WHERE status = 'ACTIVE'
            ORDER BY updated_at DESC
            LIMIT 10
            """.trimIndent()
        ) { rs, _ ->
            mapOf(
                "tableId" to rs.getLong("id"),
                "tableName" to rs.getString("table_name"),
                "schemaName" to rs.getString("schema_name"),
                "updatedAt" to rs.getTimestamp("updated_at")?.toLocalDateTime()
            )
        }

        return mapOf(
            "totalTables" to totalRecords,
            "syncFrequencyDistribution" to syncFrequencyStats,
            "tablesWithRequirementId" to requirementIdStats,
            "tablesWithDataSyncScope" to dataSyncScopeStats,
            "recentUpdates" to recentUpdates
        )
    }

    /**
     * 血缘表信息行映射器
     */
    private val lineageTableInfoRowMapper = RowMapper<LineageTableInfo> { rs, _ ->
        LineageTableInfo(
            id = rs.getLong("id"),
            datasourceId = rs.getLong("datasource_id"),
            schemaName = rs.getString("schema_name"),
            tableName = rs.getString("table_name"),
            tableType = rs.getString("table_type"),
            chineseName = rs.getString("chinese_name"),
            description = rs.getString("description"),
            syncFrequency = rs.getString("sync_frequency"),
            requirementId = rs.getString("requirement_id"),
            dataSyncScope = rs.getString("data_sync_scope"),
            status = rs.getString("status"),
            createdAt = rs.getTimestamp("created_at").toLocalDateTime(),
            updatedAt = rs.getTimestamp("updated_at").toLocalDateTime()
        )
    }
}