---
description:
globs:
alwaysApply: false
---
# 血缘目录模块 (Lineage Catalog Module)

## 模块概述 (Module Overview)
血缘目录模块负责血缘数据的存储、查询、管理和变更检测 (storage, querying, management and change detection of lineage data)。

## 核心组件 (Core Components)

### 1. 数据访问层 (Data Access Layer)
- [LineageRepository.kt](mdc:src/main/kotlin/com/datayes/lineagecatalog/LineageRepository.kt) - 血缘数据访问和查询 (lineage data access and querying)

### 2. 服务层 (Service Layer)
- [EnhancedDataExchangeJobService.kt](mdc:src/main/kotlin/com/datayes/lineagecatalog/EnhancedDataExchangeJobService.kt) - 增强的数据交换作业服务 (enhanced data exchange job service)
- [LineageChangeDetectionService.kt](mdc:src/main/kotlin/com/datayes/lineagecatalog/LineageChangeDetectionService.kt) - 血缘变更检测服务 (lineage change detection service)

### 3. 工具类 (Utilities)
- [LineageHashCalculator.kt](mdc:src/main/kotlin/com/datayes/lineagecatalog/LineageHashCalculator.kt) - 血缘哈希计算器 (lineage hash calculator)

## 功能特性 (Features)

### 1. 血缘数据管理 (Lineage Data Management)
- 血缘关系存储 (lineage relationship storage)
- 血缘数据查询 (lineage data querying)
- 血缘图构建 (lineage graph construction)
- 影响分析 (impact analysis)

### 2. 变更检测 (Change Detection)
- 血缘变更识别 (lineage change identification)
- 哈希值计算和比较 (hash calculation and comparison)
- 变更历史追踪 (change history tracking)

### 3. 查询功能 (Query Features)
- 上游依赖查询 (upstream dependency querying)
- 下游影响查询 (downstream impact querying)
- 表级血缘查询 (table-level lineage querying)
- 列级血缘查询 (column-level lineage querying)

## 数据模型 (Data Models)
- 表血缘关系 (table lineage relationships)
- 列血缘关系 (column lineage relationships)
- 作业执行记录 (job execution records)
- 血缘元数据 (lineage metadata)

## 存储策略 (Storage Strategy)
- 关系型数据库存储 (relational database storage)
- 索引优化 (index optimization)
- 查询性能优化 (query performance optimization)
- 数据一致性保证 (data consistency guarantee)

## 设计模式 (Design Patterns)
- 仓储模式 (Repository Pattern)
- 策略模式 (Strategy Pattern)
- 观察者模式用于变更通知 (Observer Pattern for change notifications)

## 性能考虑 (Performance Considerations)
- 批量操作优化 (bulk operation optimization)
- 查询缓存 (query caching)
- 分页查询 (paginated querying)
- 异步处理 (asynchronous processing)
