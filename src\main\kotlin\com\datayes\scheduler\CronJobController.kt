package com.datayes.scheduler

import com.datayes.ApiResponse
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

/**
 * Cron作业管理控制器 (Cron Job Management Controller)
 */
@Tag(name = "CronJob", description = "定时任务管理接口")
@RestController
@RequestMapping("/api/v1/cron-jobs")
@CrossOrigin(origins = ["*"])
class CronJobController(
    private val cronJobSchedulerService: CronJobSchedulerService
) {

    private val logger = LoggerFactory.getLogger(CronJobController::class.java)

    /**
     * 获取所有已调度的定时任务信息 (Get all scheduled task information)
     */
    @Operation(summary = "获取定时任务列表", description = "返回所有已调度的定时任务信息")
    @GetMapping
    fun getScheduledTasks(): ResponseEntity<ApiResponse<List<ScheduledTaskInfo>>> {
        val logPrefix = "b4c5d6e7"
        
        return try {
            logger.info("$logPrefix | 接收到获取定时任务列表请求")
            
            val scheduledTasks = cronJobSchedulerService.getScheduledTasksInfo()
            
            logger.info("$logPrefix | 获取定时任务列表成功: 任务数量=${scheduledTasks.size}")
            
            ResponseEntity.ok(
                ApiResponse.success(
                    data = scheduledTasks,
                    message = "获取定时任务列表成功"
                )
            )
            
        } catch (e: Exception) {
            logger.error("$logPrefix | 获取定时任务列表失败", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取定时任务列表失败: ${e.message}"))
        }
    }

    /**
     * 重新调度指定系统的定时任务 (Reschedule system task)
     */
    @Operation(summary = "重新调度系统任务", description = "重新调度指定系统的定时任务")
    @PostMapping("/systems/{systemId}/reschedule")
    fun rescheduleSystemTask(
        @Parameter(description = "系统ID", example = "1")
        @PathVariable systemId: Long
    ): ResponseEntity<ApiResponse<String>> {
        val logPrefix = "f8g9h0i1"
        
        return try {
            logger.info("$logPrefix | 接收到重新调度系统任务请求: systemId=$systemId")
            
            cronJobSchedulerService.rescheduleSystemTask(systemId)
            
            logger.info("$logPrefix | 系统任务重新调度成功: systemId=$systemId")
            
            ResponseEntity.ok(
                ApiResponse.success(
                    data = "SUCCESS",
                    message = "系统任务重新调度成功"
                )
            )
            
        } catch (e: Exception) {
            logger.error("$logPrefix | 系统任务重新调度失败: systemId=$systemId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统任务重新调度失败: ${e.message}"))
        }
    }

    /**
     * 取消指定系统的定时任务 (Cancel system task)
     */
    @Operation(summary = "取消系统任务", description = "取消指定系统的定时任务")
    @DeleteMapping("/systems/{systemId}")
    fun cancelSystemTask(
        @Parameter(description = "系统ID", example = "1")
        @PathVariable systemId: Long
    ): ResponseEntity<ApiResponse<String>> {
        val logPrefix = "j2k3l4m5"
        
        return try {
            logger.info("$logPrefix | 接收到取消系统任务请求: systemId=$systemId")
            
            cronJobSchedulerService.cancelSystemTask(systemId)
            
            logger.info("$logPrefix | 系统任务取消成功: systemId=$systemId")
            
            ResponseEntity.ok(
                ApiResponse.success(
                    data = "SUCCESS",
                    message = "系统任务取消成功"
                )
            )
            
        } catch (e: Exception) {
            logger.error("$logPrefix | 系统任务取消失败: systemId=$systemId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统任务取消失败: ${e.message}"))
        }
    }

    /**
     * 重新初始化所有定时任务 (Reinitialize all scheduled tasks)
     */
    @Operation(summary = "重新初始化定时任务", description = "重新初始化所有定时任务")
    @PostMapping("/reinitialize")
    fun reinitializeScheduledTasks(): ResponseEntity<ApiResponse<String>> {
        val logPrefix = "n6o7p8q9"
        
        return try {
            logger.info("$logPrefix | 接收到重新初始化定时任务请求")
            
            cronJobSchedulerService.initializeScheduledTasks()
            
            logger.info("$logPrefix | 定时任务重新初始化成功")
            
            ResponseEntity.ok(
                ApiResponse.success(
                    data = "SUCCESS",
                    message = "定时任务重新初始化成功"
                )
            )
            
        } catch (e: Exception) {
            logger.error("$logPrefix | 定时任务重新初始化失败", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("定时任务重新初始化失败: ${e.message}"))
        }
    }
}