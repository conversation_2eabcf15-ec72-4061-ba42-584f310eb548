#!/bin/bash

echo "Setting up Database Query Tool..."

# Check if Python 3 is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed"
    exit 1
fi

# Install required Python packages
echo "📦 Installing Python dependencies..."
pip3 install -r db-requirements.txt

# Make the script executable
chmod +x db-query-tool.py

echo "✅ Setup complete!"
echo ""
echo "🚀 Usage examples:"
echo "  python3 db-query-tool.py --list-tables"
echo "  python3 db-query-tool.py --describe lineage"
echo "  python3 db-query-tool.py --query \"SELECT * FROM lineage LIMIT 5\""
echo "  python3 db-query-tool.py --interactive"
echo "  python3 db-query-tool.py --list-tables --db dataexchange"
echo ""
echo "💡 Use --help for more options"