<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
    <pluginGroups></pluginGroups>
    <proxies></proxies>
    <servers>
        <server>
            <id>develop2dep</id>
            <username>urp</username>
            <password>urp</password>
        </server>
    </servers>
    <mirrors>
        <mirror>
            <id>mslife</id>
            <mirrorOf>*</mirrorOf>
            <name>mslife nexus</name>
            <url>https://nexus.minshenglife.com/repository/maven-public/</url>
        </mirror>
    </mirrors>
    <profiles></profiles>
</settings>