package com.datayes.git

import org.eclipse.jgit.api.Git
import org.eclipse.jgit.transport.UsernamePasswordCredentialsProvider
import java.nio.file.Files
import java.nio.file.Path
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.*
import kotlin.io.path.ExperimentalPathApi
import kotlin.io.path.deleteRecursively

/**
 * Git 仓库操作的结果数据类
 *
 * @param result 操作的返回结果
 * @param lastUpdateTime 仓库的最后更新时间
 */
data class RepoOperationResult<T>(
    val result: T,
    val lastUpdateTime: LocalDateTime
)

/**
 * Git 工具类，提供 Git 仓库操作相关功能
 */
object GitUtils {

    private val log = org.slf4j.LoggerFactory.getLogger(this.javaClass)

    /**
     * 克隆 Git 仓库并在临时目录中执行操作
     *
     * @param repoUrl Git 仓库 URL
     * @param username 用户名（可为 null）
     * @param password 密码（可为 null）
     * @param operation 在克隆的仓库目录上执行的操作，接收临时目录路径作为参数
     * @return 包含操作结果和仓库最后更新时间的 RepoOperationResult 对象
     */
    @OptIn(ExperimentalPathApi::class)
    fun <T> withClonedRepo(
        repoUrl: String,
        username: String? = null,
        password: String? = null,
        operation: (Path) -> T
    ): RepoOperationResult<T> {
        // 创建带有唯一标识符的临时目录
        val uniqueId = UUID.randomUUID().toString()
        val tempDir = Files.createTempDirectory("git-clone-$uniqueId-")

        try {
            // 配置克隆命令
            val cloneCommand = Git.cloneRepository()
                .setURI(repoUrl)
                .setDirectory(tempDir.toFile())
                .setCloneAllBranches(false)
                .setDepth(1) // 浅克隆，只获取最新的提交

            // 如果提供了用户名和密码，则设置认证信息
            if (!username.isNullOrBlank() && !password.isNullOrBlank()) {
                val credentialsProvider = UsernamePasswordCredentialsProvider(username, password)
                cloneCommand.setCredentialsProvider(credentialsProvider)
            }

            // 执行克隆操作
            val git = cloneCommand.call()

            try {
                // 获取最后一次提交时间
                val log = git.log()
                val maxCountLog = log.setMaxCount(1)
                val call = maxCountLog.call()
                val iterator = call.iterator()
                val lastCommit = iterator.next()
                val lastUpdateTime =
                    lastCommit.authorIdent.whenAsInstant.atZone(ZoneId.systemDefault()).toLocalDateTime()

                // 执行传入的操作
                val result = operation(tempDir)

                // 返回操作结果和最后更新时间
                return RepoOperationResult(result, lastUpdateTime)
            } finally {
                git.close()
            }
        } finally {
            // 清理临时目录
            try {
                tempDir.deleteRecursively()
            } catch (e: Exception) {
                // 记录删除失败，但不影响主流程
                log.error("Failed to delete temporary directory: ${e.message}", e)
            }
        }
    }

    /**
     * 获取 Git 仓库的最后更新时间
     *
     * @param repoUrl Git 仓库 URL
     * @param username 用户名（可为 null）
     * @param password 密码（可为 null）
     * @return 最后更新时间
     */
    fun getLastUpdateTime(
        repoUrl: String,
        username: String? = null,
        password: String? = null
    ): LocalDateTime {
        val result = withClonedRepo<Unit>(repoUrl, username, password) { _ ->
            // 不需要执行任何操作，只需要获取最后更新时间
        }
        return result.lastUpdateTime
    }
}
