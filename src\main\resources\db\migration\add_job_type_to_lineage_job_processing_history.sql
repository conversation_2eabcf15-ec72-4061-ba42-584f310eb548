-- Migration DDL: Add job_type column and modify existing columns for enhanced lineage_job_processing_history table
-- This migration transforms the table to support both Data Exchange and HDFS shell script jobs

-- Step 1: Add the new job_type column
ALTER TABLE lineage_job_processing_history 
ADD COLUMN job_type ENUM('DATA_EXCHANGE', 'HDFS_SHELL_SCRIPT') NULL COMMENT '作业类型';

-- Step 2: Update existing records to set job_type based on current data patterns
-- Assuming existing records are Data Exchange jobs (since that was the original purpose)
UPDATE lineage_job_processing_history 
SET job_type = 'DATA_EXCHANGE' 
WHERE job_type IS NULL;

-- Step 3: Make job_type NOT NULL after data migration
ALTER TABLE lineage_job_processing_history 
MODIFY COLUMN job_type ENUM('DATA_EXCHANGE', 'HDFS_SHELL_SCRIPT') NOT NULL COMMENT '作业类型';

-- Step 4: Make reader_job_id and write_job_id nullable to support HDFS jobs
ALTER TABLE lineage_job_processing_history 
MODIFY COLUMN reader_job_id VARCHAR(255) NULL COMMENT '数据交换读取作业ID (仅用于DATA_EXCHANGE)';

ALTER TABLE lineage_job_processing_history 
MODIFY COLUMN write_job_id VARCHAR(255) NULL COMMENT '数据交换写入作业ID (仅用于DATA_EXCHANGE)';

-- Step 5: Add new indexes for performance
CREATE INDEX idx_job_type ON lineage_job_processing_history (job_type);
CREATE INDEX idx_job_type_processed_at ON lineage_job_processing_history (job_type, processed_at);

-- Step 6: Add constraints to ensure proper field usage based on job type
ALTER TABLE lineage_job_processing_history 
ADD CONSTRAINT chk_data_exchange_fields 
CHECK (
    (job_type = 'DATA_EXCHANGE' AND reader_job_id IS NOT NULL AND write_job_id IS NOT NULL) OR
    (job_type = 'HDFS_SHELL_SCRIPT' AND reader_job_id IS NULL AND write_job_id IS NULL)
);

-- Step 7: Update table comment
ALTER TABLE lineage_job_processing_history 
COMMENT = '作业处理历史记录 - 支持数据交换和HDFS Shell脚本作业';