{"jobId": "example_manual_import", "jobName": "Example Manual Lineage Import", "sourceDatabase": {"dbType": "mysql", "host": "source-server", "port": 3306, "databaseName": "source_db", "originalConnectionString": "*****************************************"}, "targetDatabase": {"dbType": "mysql", "host": "target-server", "port": 3306, "databaseName": "target_db", "originalConnectionString": "*****************************************"}, "tableLineage": {"sourceTables": [{"schema": null, "tableName": "source_table", "database": "source_db"}], "targetTable": {"schema": null, "tableName": "target_table", "database": "target_db"}, "lineageType": "DIRECT_COPY"}, "columnLineages": [{"sourceColumn": {"columnName": "id", "dataType": "BIGINT", "comment": "Primary key", "tableName": "source_table"}, "targetColumn": {"columnName": "id", "dataType": "BIGINT", "comment": "Primary key", "tableName": "target_table"}, "transformation": {"transformationType": "NONE", "description": "Direct copy", "expression": null}}, {"sourceColumn": {"columnName": "name", "dataType": "VARCHAR", "comment": "Name field", "tableName": "source_table"}, "targetColumn": {"columnName": "full_name", "dataType": "VARCHAR", "comment": "Full name field", "tableName": "target_table"}, "transformation": {"transformationType": "FUNCTION", "description": "Convert to uppercase", "expression": "UPPER(name)"}}], "originalSql": "INSERT INTO target_table (id, full_name) SELECT id, UPPER(name) FROM source_table"}