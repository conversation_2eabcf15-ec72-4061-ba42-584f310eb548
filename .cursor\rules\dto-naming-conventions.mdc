---
description: 
globs: 
alwaysApply: true
---
# DTO Naming Conventions

## Overview
This rule enforces proper naming conventions for data transfer objects in the codebase to improve code clarity and follow CQRS (Command Query Responsibility Segregation) principles.

## Naming Rules

### ❌ Avoid: XxxDto Suffix
Do not use the generic `Dto` suffix for data transfer objects.

**Examples of what NOT to do:**
- `CreateManualTableLineageDto`
- `EditManualTableLineageDto`
- `LineageOperationResultDto`
- `ManualLineageDetailsDto`

### ✅ Use: Semantic Suffixes

#### Command - For Modification Operations
Use `Command` suffix for operations that modify state (create, update, delete, edit).

**Examples:**
- `CreateManualTableLineageCommand`
- `EditManualTableLineageCommand` 
- `UpdateManualTableLineageCommand`
- `DeleteLineageCommand`
- `UpsertManualTableLineageCommand`

#### Request - For Query/Read-Only Operations
Use `Request` suffix for operations that query or read data without modification.

**Examples:**
- `GetManualLineageRequest`
- `SearchManualLineageRequest`
- `QueryLineageStatisticsRequest`
- `FindUpstreamLineageRequest`

#### Result - For Return Types
Use `Result` suffix for operation results and response objects.

**Examples:**
- `LineageOperationResult`
- `ManualLineageSearchResult`
- `LineageValidationResult`
- `CreateLineageResult`

## Implementation Examples

### Manual Lineage Management
Reference implementation in [ManualLineageDto.kt](mdc:src/main/kotlin/com/datayes/lineage/ManualLineageDto.kt):

```kotlin
// ✅ Good - Command for modification
data class UpsertManualTableLineageCommand(...)

// ✅ Good - Request for query
data class SearchManualLineageRequest(...)

// ✅ Good - Result for response
data class LineageOperationResult(...)
```

### Controller Methods
Reference implementation in [ManualLineageController.kt](mdc:src/main/kotlin/com/datayes/lineage/ManualLineageController.kt):

```kotlin
// ✅ Good - Command parameter
fun upsertManualTableLineage(
    @RequestBody command: UpsertManualTableLineageCommand
): ResponseEntity<LineageOperationResult>

// ✅ Good - Request parameter  
fun searchManualLineage(
    @RequestBody request: SearchManualLineageRequest
): ResponseEntity<List<ManualLineageDetails>>
```

## Benefits

1. **Clarity**: The purpose of each object is immediately clear from its name
2. **CQRS Alignment**: Distinguishes between commands (write) and queries (read)
3. **Consistency**: Uniform naming across the entire codebase
4. **Maintainability**: Easier to understand and modify code

## Enforcement

- All new DTOs must follow these conventions
- Existing DTOs should be gradually refactored to follow these patterns
- Code reviews should check for compliance with these naming rules

## Related Files

- [ManualLineageDto.kt](mdc:src/main/kotlin/com/datayes/lineage/ManualLineageDto.kt) - Example implementation
- [ManualLineageController.kt](mdc:src/main/kotlin/com/datayes/lineage/ManualLineageController.kt) - Usage examples
- [ManualLineageService.kt](mdc:src/main/kotlin/com/datayes/lineage/ManualLineageService.kt) - Service layer implementation

