package com.datayes.lineage

import com.datayes.dataexchange.DataExchangeJob
import com.datayes.metadata.MetadataDataSourceDto
import com.datayes.metadata.MetadataSystemInfoDto
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.jdbc.core.RowMapper
import org.springframework.jdbc.support.GeneratedKeyHolder
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional
import java.sql.Statement
import java.time.LocalDateTime
import com.datayes.util.CronExpressionUtil
import org.slf4j.LoggerFactory

/**
 * 血缘数据访问层 (Lineage Data Access Layer)
 *
 * 负责血缘信息的持久化和查询操作
 */
@Repository
class LineageRepository(private val jdbcTemplate: JdbcTemplate) {

    private val logger = LoggerFactory.getLogger(LineageRepository::class.java)

    /**
     * 更新数据库中的血缘信息
     */
    @Transactional
    fun updateLineageInDatabase(
        jobKey: String,
        dataLineage: DataLineage,
        taskId: Long? = null
    ): List<Long> {
        // 1. 标记旧的血缘关系为非活跃
        deactivateLineageByJobKey(jobKey)

        // 2. 保存新的血缘信息（传入taskId和jobKey）
        val relationshipIds = saveDataLineage(dataLineage, taskId, jobKey)

        // 3. 更新引用计数
        updateReferenceCounts(dataLineage)

        return relationshipIds
    }

    /**
     * 更新表和列的引用计数
     */
    fun updateReferenceCounts(dataLineage: DataLineage) {
        // 增加新引用的计数
        dataLineage.tableLineage.sourceTables.forEach { table ->
            incrementTableReferenceCount(table)
        }
        incrementTableReferenceCount(dataLineage.tableLineage.targetTable)

        dataLineage.columnLineages.forEach { columnLineage ->
            incrementColumnReferenceCount(columnLineage.sourceColumn)
            incrementColumnReferenceCount(columnLineage.targetColumn)
        }
    }

    /**
     * 增加表引用计数
     */
    fun incrementTableReferenceCount(tableInfo: TableInfo) {
        val sql = """
            UPDATE lineage_tables 
            SET reference_count = reference_count + 1, 
                last_referenced_at = CURRENT_TIMESTAMP
            WHERE datasource_id = (
                SELECT id FROM lineage_datasources 
                WHERE host = ? AND port = ? AND database_name = ?
            ) AND table_name = ? AND (schema_name = ? OR (schema_name IS NULL AND ? IS NULL))
        """.trimIndent()

        jdbcTemplate.update(
            sql,
            tableInfo.database.host,
            tableInfo.database.port,
            tableInfo.database.databaseName,
            tableInfo.tableName,
            tableInfo.schema,
            tableInfo.schema
        )
    }

    /**
     * 增加列引用计数
     */
    fun incrementColumnReferenceCount(columnInfo: ColumnInfo) {
        val sql = """
            UPDATE lineage_columns 
            SET reference_count = reference_count + 1,
                last_referenced_at = CURRENT_TIMESTAMP
            WHERE table_id IN (
                SELECT t.id FROM lineage_tables t
                JOIN lineage_datasources ds ON t.datasource_id = ds.id
                WHERE ds.host = ? AND ds.port = ? AND ds.database_name = ?
                  AND t.table_name = ? AND (t.schema_name = ? OR (t.schema_name IS NULL AND ? IS NULL))
            ) AND column_name = ?
        """.trimIndent()

        jdbcTemplate.update(
            sql,
            columnInfo.table.database.host,
            columnInfo.table.database.port,
            columnInfo.table.database.databaseName,
            columnInfo.table.tableName,
            columnInfo.table.schema,
            columnInfo.table.schema,
            columnInfo.columnName
        )
    }

    /**
     * 根据作业标识停用血缘关系
     */
    @Transactional
    fun deactivateLineageByJobKey(jobKey: String): Int {
        val sql = """
            UPDATE lineage_relationships 
            SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP
            WHERE job_key = ? AND is_active = TRUE
        """.trimIndent()

        return jdbcTemplate.update(sql, jobKey)
    }

    /**
     * 保存数据血缘信息到数据库
     *
     * @param dataLineage 数据血缘对象
     * @param taskId 关联的任务ID
     * @param jobKey 作业唯一标识
     * @return 生成的血缘关系ID列表
     */
    @Transactional
    fun saveDataLineage(dataLineage: DataLineage, taskId: Long?, jobKey: String? = null): List<Long> {
        val relationshipIds = mutableListOf<Long>()

        // 1. 确保数据源存在
        val sourceDataSourceId = getOrCreateDataSource(dataLineage.sourceDatabase)
        val targetDataSourceId = getOrCreateDataSource(dataLineage.targetDatabase)

        // 2. 确保表存在
        val sourceTableIds = dataLineage.tableLineage.sourceTables.map { sourceTable ->
            getOrCreateTable(sourceTable, sourceDataSourceId)
        }
        val targetTableId = getOrCreateTable(dataLineage.tableLineage.targetTable, targetDataSourceId)

        // 3. 计算内容哈希（如果提供了jobKey）
        val contentHash = if (jobKey != null) LineageHashCalculator.calculateHash(dataLineage) else null

        // 4. 保存表级血缘关系
        for (sourceTableId in sourceTableIds) {
            val tableRelationshipId = saveTableLineageRelationship(
                taskId = taskId,
                sourceTableId = sourceTableId,
                targetTableId = targetTableId,
                lineageType = dataLineage.tableLineage.lineageType,
                originalSql = dataLineage.originalSql,
                jobKey = jobKey,
                contentHash = contentHash
            )
            relationshipIds.add(tableRelationshipId)
        }

        // 5. 保存列级血缘关系
        for (columnLineage in dataLineage.columnLineages) {
            // 确保列存在
            val sourceColumnId = getOrCreateColumn(columnLineage.sourceColumn)
            val targetColumnId = getOrCreateColumn(columnLineage.targetColumn)

            // 获取对应的表ID
            val sourceTableId = getOrCreateTable(
                columnLineage.sourceColumn.table,
                getOrCreateDataSource(columnLineage.sourceColumn.table.database)
            )
            val columnTargetTableId = getOrCreateTable(
                columnLineage.targetColumn.table,
                getOrCreateDataSource(columnLineage.targetColumn.table.database)
            )

            // 保存列级血缘关系
            val columnRelationshipId = saveColumnLineageRelationship(
                taskId = taskId,
                sourceTableId = sourceTableId,
                targetTableId = columnTargetTableId,
                sourceColumnId = sourceColumnId,
                targetColumnId = targetColumnId,
                transformation = columnLineage.transformation,
                jobKey = jobKey,
                contentHash = contentHash
            )
            relationshipIds.add(columnRelationshipId)
        }

        return relationshipIds
    }

    /**
     * 根据表名查询上游血缘关系
     */
    fun findUpstreamLineage(
        tableName: String,
        schemaName: String?,
        datasourceName: String,
        maxLevels: Int = 3
    ): List<TableLineageView> {
        val sql = """
            WITH RECURSIVE upstream_lineage AS (
                SELECT 
                    lr.id as relationship_id,
                    lr.source_table_id,
                    lr.target_table_id,
                    st.table_name as source_table,
                    st.schema_name as source_schema,
                    st.chinese_name as source_chinese_name,
                    sds.datasource_name as source_datasource,
                    ssys.system_name as source_system,
                    tt.table_name as target_table,
                    tt.schema_name as target_schema,
                    tds.datasource_name as target_datasource,
                    lr.lineage_type,
                    lr.source_system as lineage_source,
                    lr.confidence_score,
                    lr.created_at,
                    1 as level
                FROM lineage_relationships lr
                JOIN lineage_tables st ON lr.source_table_id = st.id
                JOIN lineage_tables tt ON lr.target_table_id = tt.id
                JOIN lineage_datasources sds ON st.datasource_id = sds.id
                JOIN lineage_datasources tds ON tt.datasource_id = tds.id
                LEFT JOIN lineage_systems ssys ON sds.system_id = ssys.id
                WHERE tt.table_name = ?
                  AND (? IS NULL OR tt.schema_name = ?)
                  AND tds.datasource_name = ?
                  AND lr.relationship_type = 'TABLE_LEVEL'
                  AND lr.is_active = true

                UNION ALL

                SELECT 
                    lr.id,
                    lr.source_table_id,
                    lr.target_table_id,
                    st.table_name,
                    st.schema_name,
                    st.chinese_name,
                    sds.datasource_name,
                    ssys.system_name,
                    tt.table_name,
                    tt.schema_name,
                    tds.datasource_name,
                    lr.lineage_type,
                    lr.source_system,
                    lr.confidence_score,
                    lr.created_at,
                    ul.level + 1
                FROM lineage_relationships lr
                JOIN lineage_tables st ON lr.source_table_id = st.id
                JOIN lineage_tables tt ON lr.target_table_id = tt.id
                JOIN lineage_datasources sds ON st.datasource_id = sds.id
                JOIN lineage_datasources tds ON tt.datasource_id = tds.id
                LEFT JOIN lineage_systems ssys ON sds.system_id = ssys.id
                JOIN upstream_lineage ul ON ul.source_table_id = lr.target_table_id
                WHERE ul.level < ?
                  AND lr.relationship_type = 'TABLE_LEVEL'
                  AND lr.is_active = true
            )
            SELECT * FROM upstream_lineage
            ORDER BY level, source_table
        """.trimIndent()

        return jdbcTemplate.query(
            sql,
            tableLineageViewRowMapper,
            tableName, schemaName, schemaName, datasourceName, maxLevels
        )
    }

    /**
     * 根据表名查询下游血缘关系
     */
    fun findDownstreamLineage(
        tableName: String,
        schemaName: String?,
        datasourceName: String,
        maxLevels: Int = 3
    ): List<TableLineageView> {
        val sql = """
            WITH RECURSIVE downstream_lineage AS (
                SELECT 
                    lr.id as relationship_id,
                    lr.source_table_id,
                    lr.target_table_id,
                    st.table_name as source_table,
                    st.schema_name as source_schema,
                    st.chinese_name as source_chinese_name,
                    sds.datasource_name as source_datasource,
                    ssys.system_name as source_system,
                    tt.table_name as target_table,
                    tt.schema_name as target_schema,
                    tds.datasource_name as target_datasource,
                    lr.lineage_type,
                    lr.source_system as lineage_source,
                    lr.confidence_score,
                    lr.created_at,
                    1 as level
                FROM lineage_relationships lr
                JOIN lineage_tables st ON lr.source_table_id = st.id
                JOIN lineage_tables tt ON lr.target_table_id = tt.id
                JOIN lineage_datasources sds ON st.datasource_id = sds.id
                JOIN lineage_datasources tds ON tt.datasource_id = tds.id
                LEFT JOIN lineage_systems ssys ON sds.system_id = ssys.id
                WHERE st.table_name = ?
                  AND (? IS NULL OR st.schema_name = ?)
                  AND sds.datasource_name = ?
                  AND lr.relationship_type = 'TABLE_LEVEL'
                  AND lr.is_active = true

                UNION ALL

                SELECT 
                    lr.id,
                    lr.source_table_id,
                    lr.target_table_id,
                    st.table_name,
                    st.schema_name,
                    st.chinese_name,
                    sds.datasource_name,
                    ssys.system_name,
                    tt.table_name,
                    tt.schema_name,
                    tds.datasource_name,
                    lr.lineage_type,
                    lr.source_system,
                    lr.confidence_score,
                    lr.created_at,
                    dl.level + 1
                FROM lineage_relationships lr
                JOIN lineage_tables st ON lr.source_table_id = st.id
                JOIN lineage_tables tt ON lr.target_table_id = tt.id
                JOIN lineage_datasources sds ON st.datasource_id = sds.id
                JOIN lineage_datasources tds ON tt.datasource_id = tds.id
                LEFT JOIN lineage_systems ssys ON sds.system_id = ssys.id
                JOIN downstream_lineage dl ON dl.target_table_id = lr.source_table_id
                WHERE dl.level < ?
                  AND lr.relationship_type = 'TABLE_LEVEL'
                  AND lr.is_active = true
            )
            SELECT * FROM downstream_lineage
            ORDER BY level, target_table
        """.trimIndent()

        return jdbcTemplate.query(
            sql,
            tableLineageViewRowMapper,
            tableName, schemaName, schemaName, datasourceName, maxLevels
        )
    }

    /**
     * 查询列级血缘关系
     */
    fun findColumnLineage(
        tableName: String,
        schemaName: String?,
        datasourceName: String
    ): List<ColumnLineageView> {
        val sql = """
            SELECT 
                lr.id as relationship_id,
                sc.column_name as source_column,
                sc.data_type as source_data_type,
                st.table_name as source_table,
                st.schema_name as source_schema,
                tc.column_name as target_column,
                tc.data_type as target_data_type,
                tt.table_name as target_table,
                tt.schema_name as target_schema,
                lr.transformation_type,
                lr.transformation_description,
                lr.transformation_expression,
                lr.confidence_score,
                lr.source_system as lineage_source,
                lr.created_at
            FROM lineage_relationships lr
            JOIN lineage_columns sc ON lr.source_column_id = sc.id
            JOIN lineage_columns tc ON lr.target_column_id = tc.id
            JOIN lineage_tables st ON sc.table_id = st.id
            JOIN lineage_tables tt ON tc.table_id = tt.id
            JOIN lineage_datasources sds ON st.datasource_id = sds.id
            JOIN lineage_datasources tds ON tt.datasource_id = tds.id
            WHERE lr.relationship_type = 'COLUMN_LEVEL'
              AND lr.is_active = true
              AND ((st.table_name = ? AND (? IS NULL OR st.schema_name = ?) AND sds.datasource_name = ?)
                   OR (tt.table_name = ? AND (? IS NULL OR tt.schema_name = ?) AND tds.datasource_name = ?))
            ORDER BY st.table_name, sc.column_name, tt.table_name, tc.column_name
        """.trimIndent()

        return jdbcTemplate.query(
            sql,
            columnLineageViewRowMapper,
            tableName, schemaName, schemaName, datasourceName,
            tableName, schemaName, schemaName, datasourceName
        )
    }

    /**
     * 按系统统计血缘信息
     */
    fun getSystemStatistics(): List<SystemStatistics> {
        val sql = """
            SELECT 
                sys.system_name,
                sys.system_code,
                COUNT(DISTINCT ds.id) as datasource_count,
                COUNT(DISTINCT t.id) as table_count,
                COUNT(DISTINCT c.id) as column_count,
                COUNT(DISTINCT CASE WHEN lr.relationship_type = 'TABLE_LEVEL' THEN lr.id END) as table_lineage_count,
                COUNT(DISTINCT CASE WHEN lr.relationship_type = 'COLUMN_LEVEL' THEN lr.id END) as column_lineage_count,
                MAX(lr.created_at) as last_update_time
            FROM lineage_systems sys
            LEFT JOIN lineage_datasources ds ON sys.id = ds.system_id AND ds.status = 'ACTIVE'
            LEFT JOIN lineage_tables t ON ds.id = t.datasource_id AND t.status = 'ACTIVE'
            LEFT JOIN lineage_columns c ON t.id = c.table_id AND c.status = 'ACTIVE'
            LEFT JOIN lineage_relationships lr ON (t.id = lr.source_table_id OR t.id = lr.target_table_id) AND lr.is_active = true
            WHERE sys.status = 'ACTIVE'
            GROUP BY sys.id, sys.system_name, sys.system_code
            ORDER BY sys.system_name
        """.trimIndent()

        return jdbcTemplate.query(sql, systemStatisticsRowMapper)
    }

    // 私有辅助方法

    private fun getOrCreateDataSource(databaseInfo: DatabaseInfo): Long {
        // 首先查询是否已存在
        val existingId = jdbcTemplate.queryForList(
            """
            SELECT id FROM lineage_datasources 
            WHERE db_type = ? AND host = ? AND port = ? AND database_name = ?
            """.trimIndent(),
            Long::class.java,
            databaseInfo.dbType, databaseInfo.host, databaseInfo.port, databaseInfo.databaseName
        ).firstOrNull() ?: run {
            // 不存在则创建
            val keyHolder = GeneratedKeyHolder()
            jdbcTemplate.update({ connection ->
                val ps = connection.prepareStatement(
                    """
                    INSERT INTO lineage_datasources (datasource_name, db_type, host, port, database_name, connection_string)
                    VALUES (?, ?, ?, ?, ?, ?)
                    """.trimIndent(),
                    Statement.RETURN_GENERATED_KEYS
                )
                ps.setString(1, "${databaseInfo.dbType}-${databaseInfo.host}-${databaseInfo.databaseName}")
                ps.setString(2, databaseInfo.dbType)
                ps.setString(3, databaseInfo.host)
                ps.setInt(4, databaseInfo.port)
                ps.setString(5, databaseInfo.databaseName)
                ps.setString(6, databaseInfo.originalConnectionString)
                ps
            }, keyHolder)
            keyHolder.key!!.toLong()
        }
        return existingId
    }

    private fun getOrCreateTable(tableInfo: TableInfo, datasourceId: Long): Long {
        // 首先查询是否已存在
        val existingId = jdbcTemplate.queryForList(
            """
            SELECT id FROM lineage_tables 
            WHERE datasource_id = ? AND table_name = ? AND (schema_name = ? OR (schema_name IS NULL AND ? IS NULL))
            """.trimIndent(),
            Long::class.java,
            datasourceId, tableInfo.tableName, tableInfo.schema, tableInfo.schema
        ).singleOrNull() ?: run {
            // 不存在则创建
            val keyHolder = GeneratedKeyHolder()
            jdbcTemplate.update({ connection ->
                val ps = connection.prepareStatement(
                    """
                    INSERT INTO lineage_tables (datasource_id, schema_name, table_name)
                    VALUES (?, ?, ?)
                    """.trimIndent(),
                    Statement.RETURN_GENERATED_KEYS
                )
                ps.setLong(1, datasourceId)
                ps.setString(2, tableInfo.schema)
                ps.setString(3, tableInfo.tableName)
                ps
            }, keyHolder)
            keyHolder.key!!.toLong()
        }
        return existingId
    }

    private fun getOrCreateColumn(columnInfo: ColumnInfo): Long {

        logger.info("c174b425 | Processing columnInfo: $columnInfo")

        // 首先获取表ID
        val tableId = getOrCreateTable(
            columnInfo.table,
            getOrCreateDataSource(columnInfo.table.database)
        )

        // 查询是否已存在列
        val existingId = jdbcTemplate.queryForList(
            """
            SELECT id FROM lineage_columns 
            WHERE table_id = ? AND column_name = ?
            """.trimIndent(),
            Long::class.java,
            tableId, columnInfo.columnName
        ).singleOrNull() ?: run {
            // 不存在则创建
            val keyHolder = GeneratedKeyHolder()
            jdbcTemplate.update({ connection ->
                val ps = connection.prepareStatement(
                    """
                    INSERT INTO lineage_columns (table_id, column_name, data_type, column_comment)
                    VALUES (?, ?, ?, ?)
                    """.trimIndent(),
                    Statement.RETURN_GENERATED_KEYS
                )
                ps.setLong(1, tableId)
                ps.setString(2, columnInfo.columnName)
                ps.setString(3, columnInfo.dataType)
                ps.setString(4, columnInfo.comment)
                ps
            }, keyHolder)
            keyHolder.key!!.toLong()
        }
        return existingId
    }

    private fun saveTableLineageRelationship(
        taskId: Long?,
        sourceTableId: Long,
        targetTableId: Long,
        lineageType: LineageType,
        originalSql: String?,
        jobKey: String? = null,
        contentHash: String? = null
    ): Long {
        val keyHolder = GeneratedKeyHolder()
        jdbcTemplate.update({ connection ->
            val ps = connection.prepareStatement(
                """
                INSERT INTO lineage_relationships 
                (task_id, relationship_type, source_table_id, target_table_id, lineage_type, transformation_description, job_key, content_hash)
                VALUES (?, 'TABLE_LEVEL', ?, ?, ?, ?, ?, ?)
                """.trimIndent(),
                Statement.RETURN_GENERATED_KEYS
            )
            ps.setObject(1, taskId)
            ps.setLong(2, sourceTableId)
            ps.setLong(3, targetTableId)
            ps.setString(4, lineageType.name)
            ps.setString(5, originalSql)
            ps.setString(6, jobKey)
            ps.setString(7, contentHash)
            ps
        }, keyHolder)
        return keyHolder.key!!.toLong()
    }

    private fun saveColumnLineageRelationship(
        taskId: Long?,
        sourceTableId: Long,
        targetTableId: Long,
        sourceColumnId: Long,
        targetColumnId: Long,
        transformation: DataTransformation?,
        jobKey: String? = null,
        contentHash: String? = null
    ): Long {
        val keyHolder = GeneratedKeyHolder()
        jdbcTemplate.update({ connection ->
            val ps = connection.prepareStatement(
                """
                INSERT INTO lineage_relationships 
                (task_id, relationship_type, source_table_id, target_table_id, source_column_id, target_column_id, 
                 transformation_type, transformation_description, transformation_expression, job_key, content_hash)
                VALUES (?, 'COLUMN_LEVEL', ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """.trimIndent(),
                Statement.RETURN_GENERATED_KEYS
            )
            ps.setObject(1, taskId)
            ps.setLong(2, sourceTableId)
            ps.setLong(3, targetTableId)
            ps.setLong(4, sourceColumnId)
            ps.setLong(5, targetColumnId)
            ps.setString(6, transformation?.transformationType?.name)
            ps.setString(7, transformation?.description)
            ps.setString(8, transformation?.expression)
            ps.setString(9, jobKey)
            ps.setString(10, contentHash)
            ps
        }, keyHolder)
        return keyHolder.key!!.toLong()
    }

    // RowMapper 定义
    private val tableLineageViewRowMapper = RowMapper<TableLineageView> { rs, _ ->
        TableLineageView(
            relationshipId = rs.getLong("relationship_id"),
            sourceTableId = rs.getLong("source_table_id"),
            targetTableId = rs.getLong("target_table_id"),
            sourceTable = rs.getString("source_table"),
            sourceSchema = rs.getString("source_schema"),
            sourceChineseName = rs.getString("source_chinese_name"),
            sourceDatasource = rs.getString("source_datasource"),
            sourceSystem = rs.getString("source_system"),
            targetTable = rs.getString("target_table"),
            targetSchema = rs.getString("target_schema"),
            targetDatasource = rs.getString("target_datasource"),
            lineageType = rs.getString("lineage_type"),
            lineageSource = rs.getString("lineage_source"),
            confidenceScore = rs.getBigDecimal("confidence_score"),
            createdAt = rs.getTimestamp("created_at")?.toLocalDateTime(),
            level = rs.getInt("level")
        )
    }

    private val columnLineageViewRowMapper = RowMapper<ColumnLineageView> { rs, _ ->
        ColumnLineageView(
            relationshipId = rs.getLong("relationship_id"),
            sourceColumn = rs.getString("source_column"),
            sourceDataType = rs.getString("source_data_type"),
            sourceTable = rs.getString("source_table"),
            sourceSchema = rs.getString("source_schema"),
            sourceTableId = try {
                rs.getLong("source_table_id")
            } catch (e: Exception) {
                null
            },
            sourceDatasource = try {
                rs.getString("source_datasource")
            } catch (e: Exception) {
                null
            },
            sourceSystem = try {
                rs.getString("source_system")
            } catch (e: Exception) {
                null
            },
            sourceChineseName = try {
                rs.getString("source_chinese_name")
            } catch (e: Exception) {
                null
            },
            targetColumn = rs.getString("target_column"),
            targetDataType = rs.getString("target_data_type"),
            targetTable = rs.getString("target_table"),
            targetSchema = rs.getString("target_schema"),
            targetTableId = try {
                rs.getLong("target_table_id")
            } catch (e: Exception) {
                null
            },
            targetDatasource = try {
                rs.getString("target_datasource")
            } catch (e: Exception) {
                null
            },
            transformationType = rs.getString("transformation_type"),
            transformationDescription = rs.getString("transformation_description"),
            transformationExpression = rs.getString("transformation_expression"),
            confidenceScore = rs.getBigDecimal("confidence_score"),
            lineageSource = rs.getString("lineage_source"),
            createdAt = rs.getTimestamp("created_at")?.toLocalDateTime(),
            lastUpdated = try {
                rs.getTimestamp("updated_at")?.toLocalDateTime()
            } catch (e: Exception) {
                null
            }
        )
    }

    private val systemStatisticsRowMapper = RowMapper<SystemStatistics> { rs, _ ->
        SystemStatistics(
            systemName = rs.getString("system_name"),
            systemCode = rs.getString("system_code"),
            datasourceCount = rs.getInt("datasource_count"),
            tableCount = rs.getInt("table_count"),
            columnCount = rs.getInt("column_count"),
            tableLineageCount = rs.getInt("table_lineage_count"),
            columnLineageCount = rs.getInt("column_lineage_count"),
            lastUpdateTime = rs.getTimestamp("last_update_time")?.toLocalDateTime()
        )
    }

    private val systemInfoRowMapper = RowMapper<SystemInfo> { rs, _ ->
        val cronExpressionStr = rs.getString("cron_expression")
        val nextScheduleTime = CronExpressionUtil.calculateNextScheduleTime(cronExpressionStr)

        SystemInfo(
            id = rs.getLong("id"),
            systemName = rs.getString("system_name"),
            systemCode = rs.getString("system_code"),
            description = rs.getString("description"),
            contactPerson = rs.getString("contact_person"),
            status = rs.getString("status"),
            systemStatus = rs.getString("status"), // Add systemStatus field mapped from status
            createdAt = rs.getTimestamp("created_at").toLocalDateTime(),
            updatedAt = rs.getTimestamp("updated_at").toLocalDateTime(),
            cronExpression = cronExpressionStr,
            scheduleTime = nextScheduleTime
        )
    }

    /**
     * 根据表ID查询上游血缘关系
     *
     * @param tableId 表ID
     * @param maxLevels 最大查询层级
     * @return 表血缘视图列表
     */
    fun findUpstreamLineageByTableId(
        tableId: Long,
        maxLevels: Int = 3
    ): List<TableLineageView> {
        // 使用迭代方式查询上游血缘关系，避免使用 WITH RECURSIVE
        val result = mutableListOf<TableLineageView>()
        val processedTableIds = mutableSetOf<Long>()
        val tablesToProcess = mutableListOf<Pair<Long, Int>>() // 表ID和层级

        // 初始化待处理表列表
        tablesToProcess.add(Pair(tableId, 1))

        while (tablesToProcess.isNotEmpty() && processedTableIds.size <= 1000) { // 设置安全上限
            val (currentTableId, currentLevel) = tablesToProcess.removeAt(0)

            if (currentLevel > maxLevels) {
                continue
            }

            if (processedTableIds.contains(currentTableId)) {
                continue
            }

            processedTableIds.add(currentTableId)

            // 查询当前表的直接上游关系
            val sql = """
                SELECT 
                    lr.id as relationship_id,
                    lr.source_table_id,
                    lr.target_table_id,
                    st.table_name as source_table,
                    st.schema_name as source_schema,
                    st.chinese_name as source_chinese_name,
                    sds.datasource_name as source_datasource,
                    ssys.system_name as source_system,
                    tt.table_name as target_table,
                    tt.schema_name as target_schema,
                    tds.datasource_name as target_datasource,
                    lr.lineage_type,
                    lr.source_system as lineage_source,
                    lr.confidence_score,
                    lr.created_at,
                    ? as level
                FROM lineage_relationships lr
                JOIN lineage_tables st ON lr.source_table_id = st.id
                JOIN lineage_tables tt ON lr.target_table_id = tt.id
                JOIN lineage_datasources sds ON st.datasource_id = sds.id
                JOIN lineage_datasources tds ON tt.datasource_id = tds.id
                LEFT JOIN lineage_systems ssys ON sds.system_id = ssys.id
                WHERE tt.id = ?
                  AND lr.relationship_type = 'TABLE_LEVEL'
                  AND lr.is_active = true
            """.trimIndent()

            val currentLevelResults = jdbcTemplate.query(
                sql,
                tableLineageViewRowMapper,
                currentLevel, currentTableId
            )

            result.addAll(currentLevelResults)

            // 将上游表添加到待处理列表
            currentLevelResults.forEach { lineageView ->
                tablesToProcess.add(Pair(lineageView.sourceTableId, currentLevel + 1))
            }
        }

        // 按层级和表名排序
        return result.sortedWith(compareBy({ it.level }, { it.sourceTable }))
    }

    /**
     * 根据表ID查询下游血缘关系
     *
     * @param tableId 表ID
     * @param maxLevels 最大查询层级
     * @return 表血缘视图列表
     */
    fun findDownstreamLineageByTableId(
        tableId: Long,
        maxLevels: Int = 3
    ): List<TableLineageView> {
        // 使用迭代方式查询下游血缘关系，避免使用 WITH RECURSIVE
        val result = mutableListOf<TableLineageView>()
        val processedTableIds = mutableSetOf<Long>()
        val tablesToProcess = mutableListOf<Pair<Long, Int>>() // 表ID和层级

        // 初始化待处理表列表
        tablesToProcess.add(Pair(tableId, 1))

        while (tablesToProcess.isNotEmpty() && processedTableIds.size <= 1000) { // 设置安全上限
            val (currentTableId, currentLevel) = tablesToProcess.removeAt(0)

            if (currentLevel > maxLevels) {
                continue
            }

            if (processedTableIds.contains(currentTableId)) {
                continue
            }

            processedTableIds.add(currentTableId)

            // 查询当前表的直接下游关系
            val sql = """
                SELECT 
                    lr.id as relationship_id,
                    lr.source_table_id,
                    lr.target_table_id,
                    st.table_name as source_table,
                    st.schema_name as source_schema,
                    st.chinese_name as source_chinese_name,
                    sds.datasource_name as source_datasource,
                    ssys.system_name as source_system,
                    tt.table_name as target_table,
                    tt.schema_name as target_schema,
                    tds.datasource_name as target_datasource,
                    lr.lineage_type,
                    lr.source_system as lineage_source,
                    lr.confidence_score,
                    lr.created_at,
                    ? as level
                FROM lineage_relationships lr
                JOIN lineage_tables st ON lr.source_table_id = st.id
                JOIN lineage_tables tt ON lr.target_table_id = tt.id
                JOIN lineage_datasources sds ON st.datasource_id = sds.id
                JOIN lineage_datasources tds ON tt.datasource_id = tds.id
                LEFT JOIN lineage_systems ssys ON sds.system_id = ssys.id
                WHERE st.id = ?
                  AND lr.relationship_type = 'TABLE_LEVEL'
                  AND lr.is_active = true
            """.trimIndent()

            val currentLevelResults = jdbcTemplate.query(
                sql,
                tableLineageViewRowMapper,
                currentLevel, currentTableId
            )

            result.addAll(currentLevelResults)

            // 将下游表添加到待处理列表
            currentLevelResults.forEach { lineageView ->
                tablesToProcess.add(Pair(lineageView.targetTableId, currentLevel + 1))
            }
        }

        // 按层级和表名排序
        return result.sortedWith(compareBy({ it.level }, { it.targetTable }))
    }

    /**
     * 根据表ID查询列级血缘关系
     *
     * @param tableId 表ID
     * @return 列级血缘视图列表
     */
    fun findColumnLineageByTableId(tableId: Long): List<ColumnLineageView> {
        val sql = """
            SELECT 
                lr.id as relationship_id,
                sc.column_name as source_column,
                sc.data_type as source_data_type,
                st.table_name as source_table,
                st.schema_name as source_schema,
                st.id as source_table_id,
                st_ds.datasource_name as source_datasource,
                st_sys.system_name as source_system,
                st.chinese_name as source_chinese_name,
                tc.column_name as target_column,
                tc.data_type as target_data_type,
                tt.table_name as target_table,
                tt.schema_name as target_schema,
                tt.id as target_table_id,
                tt_ds.datasource_name as target_datasource,
                lr.transformation_type,
                lr.transformation_description,
                lr.transformation_expression,
                lr.confidence_score,
                lr.source_system as lineage_source,
                lr.created_at,
                lr.updated_at
            FROM lineage_relationships lr
            JOIN lineage_columns sc ON lr.source_column_id = sc.id
            JOIN lineage_columns tc ON lr.target_column_id = tc.id
            JOIN lineage_tables st ON sc.table_id = st.id
            JOIN lineage_tables tt ON tc.table_id = tt.id
            JOIN lineage_datasources st_ds ON st.datasource_id = st_ds.id
            JOIN lineage_datasources tt_ds ON tt.datasource_id = tt_ds.id
            LEFT JOIN lineage_systems st_sys ON st_ds.system_id = st_sys.id
            WHERE lr.relationship_type = 'COLUMN_LEVEL'
              AND lr.is_active = true
              AND (st.id = ? OR tt.id = ?)
            ORDER BY st.table_name, sc.column_name, tt.table_name, tc.column_name
        """.trimIndent()

        return jdbcTemplate.query(
            sql,
            columnLineageViewRowMapper,
            tableId, tableId
        )
    }

    /**
     * 根据表ID查询上游列级血缘关系
     */
    fun findUpstreamColumnLineageByTableId(tableId: Long, maxLevels: Int = 3): List<ColumnLineageView> {
        val sql = """
            WITH RECURSIVE upstream_column_lineage AS (
                -- 基础案例：直接的上游列级血缘
                SELECT 
                    lr.id as relationship_id,
                    sc.column_name as source_column,
                    sc.data_type as source_data_type,
                    st.table_name as source_table,
                    st.schema_name as source_schema,
                    st.id as source_table_id,
                    st_ds.datasource_name as source_datasource,
                    st_sys.system_name as source_system,
                    st.chinese_name as source_chinese_name,
                    tc.column_name as target_column,
                    tc.data_type as target_data_type,
                    tt.table_name as target_table,
                    tt.schema_name as target_schema,
                    tt.id as target_table_id,
                    tt_ds.datasource_name as target_datasource,
                    lr.transformation_type,
                    lr.transformation_description,
                    lr.transformation_expression,
                    lr.confidence_score,
                    lr.source_system as lineage_source,
                    lr.created_at,
                    lr.last_updated,
                    1 as level
                FROM lineage_relationships lr
                JOIN lineage_columns sc ON lr.source_column_id = sc.id
                JOIN lineage_columns tc ON lr.target_column_id = tc.id
                JOIN lineage_tables st ON sc.table_id = st.id
                JOIN lineage_tables tt ON tc.table_id = tt.id
                JOIN lineage_datasources st_ds ON st.datasource_id = st_ds.id
                JOIN lineage_datasources tt_ds ON tt.datasource_id = tt_ds.id
                LEFT JOIN lineage_systems st_sys ON st_ds.system_id = st_sys.id
                WHERE lr.relationship_type = 'COLUMN_LEVEL'
                  AND lr.is_active = true
                  AND tt.id = ?

                UNION ALL

                -- 递归案例：通过中间表继续向上查找
                SELECT 
                    lr.id as relationship_id,
                    sc.column_name as source_column,
                    sc.data_type as source_data_type,
                    st.table_name as source_table,
                    st.schema_name as source_schema,
                    st.id as source_table_id,
                    st_ds.datasource_name as source_datasource,
                    st_sys.system_name as source_system,
                    st.chinese_name as source_chinese_name,
                    tc.column_name as target_column,
                    tc.data_type as target_data_type,
                    tt.table_name as target_table,
                    tt.schema_name as target_schema,
                    tt.id as target_table_id,
                    tt_ds.datasource_name as target_datasource,
                    lr.transformation_type,
                    lr.transformation_description,
                    lr.transformation_expression,
                    lr.confidence_score,
                    lr.source_system as lineage_source,
                    lr.created_at,
                    lr.last_updated,
                    ucl.level + 1
                FROM lineage_relationships lr
                JOIN lineage_columns sc ON lr.source_column_id = sc.id
                JOIN lineage_columns tc ON lr.target_column_id = tc.id
                JOIN lineage_tables st ON sc.table_id = st.id
                JOIN lineage_tables tt ON tc.table_id = tt.id
                JOIN lineage_datasources st_ds ON st.datasource_id = st_ds.id
                JOIN lineage_datasources tt_ds ON tt.datasource_id = tt_ds.id
                LEFT JOIN lineage_systems st_sys ON st_ds.system_id = st_sys.id
                JOIN upstream_column_lineage ucl ON tt.id = ucl.source_table_id
                WHERE lr.relationship_type = 'COLUMN_LEVEL'
                  AND lr.is_active = true
                  AND ucl.level < ?
            )
            SELECT * FROM upstream_column_lineage
            ORDER BY level, source_table, source_column
        """.trimIndent()

        return jdbcTemplate.query(
            sql,
            columnLineageViewRowMapper,
            tableId, maxLevels
        )
    }

    /**
     * 根据表ID查询下游列级血缘关系
     */
    fun findDownstreamColumnLineageByTableId(tableId: Long, maxLevels: Int = 3): List<ColumnLineageView> {
        val sql = """
            WITH RECURSIVE downstream_column_lineage AS (
                -- 基础案例：直接的下游列级血缘
                SELECT 
                    lr.id as relationship_id,
                    sc.column_name as source_column,
                    sc.data_type as source_data_type,
                    st.table_name as source_table,
                    st.schema_name as source_schema,
                    st.id as source_table_id,
                    st_ds.datasource_name as source_datasource,
                    st_sys.system_name as source_system,
                    st.chinese_name as source_chinese_name,
                    tc.column_name as target_column,
                    tc.data_type as target_data_type,
                    tt.table_name as target_table,
                    tt.schema_name as target_schema,
                    tt.id as target_table_id,
                    tt_ds.datasource_name as target_datasource,
                    lr.transformation_type,
                    lr.transformation_description,
                    lr.transformation_expression,
                    lr.confidence_score,
                    lr.source_system as lineage_source,
                    lr.created_at,
                    lr.last_updated,
                    1 as level
                FROM lineage_relationships lr
                JOIN lineage_columns sc ON lr.source_column_id = sc.id
                JOIN lineage_columns tc ON lr.target_column_id = tc.id
                JOIN lineage_tables st ON sc.table_id = st.id
                JOIN lineage_tables tt ON tc.table_id = tt.id
                JOIN lineage_datasources st_ds ON st.datasource_id = st_ds.id
                JOIN lineage_datasources tt_ds ON tt.datasource_id = tt_ds.id
                LEFT JOIN lineage_systems st_sys ON st_ds.system_id = st_sys.id
                WHERE lr.relationship_type = 'COLUMN_LEVEL'
                  AND lr.is_active = true
                  AND st.id = ?

                UNION ALL

                -- 递归案例：通过中间表继续向下查找
                SELECT 
                    lr.id as relationship_id,
                    sc.column_name as source_column,
                    sc.data_type as source_data_type,
                    st.table_name as source_table,
                    st.schema_name as source_schema,
                    st.id as source_table_id,
                    st_ds.datasource_name as source_datasource,
                    st_sys.system_name as source_system,
                    st.chinese_name as source_chinese_name,
                    tc.column_name as target_column,
                    tc.data_type as target_data_type,
                    tt.table_name as target_table,
                    tt.schema_name as target_schema,
                    tt.id as target_table_id,
                    tt_ds.datasource_name as target_datasource,
                    lr.transformation_type,
                    lr.transformation_description,
                    lr.transformation_expression,
                    lr.confidence_score,
                    lr.source_system as lineage_source,
                    lr.created_at,
                    lr.last_updated,
                    dcl.level + 1
                FROM lineage_relationships lr
                JOIN lineage_columns sc ON lr.source_column_id = sc.id
                JOIN lineage_columns tc ON lr.target_column_id = tc.id
                JOIN lineage_tables st ON sc.table_id = st.id
                JOIN lineage_tables tt ON tc.table_id = tt.id
                JOIN lineage_datasources st_ds ON st.datasource_id = st_ds.id
                JOIN lineage_datasources tt_ds ON tt.datasource_id = tt_ds.id
                LEFT JOIN lineage_systems st_sys ON st_ds.system_id = st_sys.id
                JOIN downstream_column_lineage dcl ON st.id = dcl.target_table_id
                WHERE lr.relationship_type = 'COLUMN_LEVEL'
                  AND lr.is_active = true
                  AND dcl.level < ?
            )
            SELECT * FROM downstream_column_lineage
            ORDER BY level, target_table, target_column
        """.trimIndent()

        return jdbcTemplate.query(
            sql,
            columnLineageViewRowMapper,
            tableId, maxLevels
        )
    }

    /**
     * 查询所有系统信息 (Query all system information with filtering)
     *
     * @param systemName 系统名称模糊搜索 (optional)
     * @param systemStatus 系统状态过滤 (optional)
     */
    fun findAllSystems(systemName: String? = null, systemStatus: String? = null): List<SystemInfo> {
        val conditions = mutableListOf<String>()
        val parameters = mutableListOf<Any>()

        // 构建WHERE条件
        if (!systemName.isNullOrBlank()) {
            conditions.add("system_name LIKE ?")
            parameters.add("%${systemName.trim()}%")
        }

        if (!systemStatus.isNullOrBlank()) {
            conditions.add("status = ?")
            parameters.add(systemStatus.trim().uppercase())
        }

        val whereClause = if (conditions.isNotEmpty()) {
            "WHERE ${conditions.joinToString(" AND ")}"
        } else {
            ""
        }

        val sql = """
            SELECT id, system_name, system_code, description, contact_person,
                   status, created_at, updated_at, cron_expression
            FROM lineage_systems
            $whereClause
            ORDER BY system_name
        """.trimIndent()

        return jdbcTemplate.query(sql, systemInfoRowMapper, *parameters.toTypedArray())
    }

    /**
     * 更新系统信息 (Update system information)
     *
     * @param systemId 系统ID
     * @param status 系统状态 (可选)
     * @param cronExpression Cron表达式 (可选)
     * @return 更新成功返回true，系统不存在返回false
     */
    fun updateSystem(systemId: Long, status: String? = null, cronExpression: String? = null): Boolean {
        val updates = mutableListOf<String>()
        val parameters = mutableListOf<Any>()

        status?.let {
            updates.add("status = ?")
            parameters.add(it.uppercase())
        }

        cronExpression?.let {
            updates.add("cron_expression = ?")
            parameters.add(it)
        }

        if (updates.isEmpty()) {
            return false // No updates to perform
        }

        updates.add("updated_at = ?")
        parameters.add(LocalDateTime.now())
        parameters.add(systemId)

        val sql = """
            UPDATE lineage_systems 
            SET ${updates.joinToString(", ")}
            WHERE id = ?
        """.trimIndent()

        val rowsAffected = jdbcTemplate.update(sql, *parameters.toTypedArray())
        return rowsAffected > 0
    }

    /**
     * 根据ID查找系统信息 (Find system by ID)
     *
     * @param systemId 系统ID
     * @return 系统信息，如果未找到则返回null
     */
    fun findSystemById(systemId: Long): SystemInfo? {
        val sql = """
            SELECT id, system_name, system_code, description, contact_person,
                   status, created_at, updated_at, cron_expression
            FROM lineage_systems
            WHERE id = ?
        """.trimIndent()

        return try {
            jdbcTemplate.queryForObject(sql, systemInfoRowMapper, systemId)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 根据ID查找血缘数据源详情 (Find lineage datasource details by ID)
     *
     * @param datasourceId 数据源ID
     * @return 血缘数据源详情，如果未找到则返回null
     */
    fun findDatasourceById(datasourceId: Long): com.datayes.dataexchange.LineageDatasourceDetails? {
        val sql = """
            SELECT id, datasource_name, db_type, host, port, database_name, 
                   connection_string, status, system_id
            FROM lineage_datasources
            WHERE id = ?
        """.trimIndent()

        return try {
            jdbcTemplate.queryForObject(sql, { rs, _ ->
                com.datayes.dataexchange.LineageDatasourceDetails(
                    id = rs.getLong("id"),
                    datasourceName = rs.getString("datasource_name"),
                    dbType = rs.getString("db_type"),
                    host = rs.getString("host"),
                    port = rs.getInt("port"),
                    databaseName = rs.getString("database_name"),
                    connectionString = rs.getString("connection_string"),
                    status = rs.getString("status"),
                    systemId = rs.getLong("system_id").takeIf { !rs.wasNull() }
                )
            }, datasourceId)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 根据数据源名称查找血缘数据源ID (Find lineage datasource ID by name)
     *
     * @param datasourceName 数据源名称
     * @return 血缘数据源ID，如果未找到则返回null
     */
    fun findLineageDatasourceIdByName(datasourceName: String): Long? {
        val sql = """
            SELECT id
            FROM lineage_datasources
            WHERE datasource_name = ? AND status = 'ACTIVE'
            LIMIT 1
        """.trimIndent()

        return try {
            jdbcTemplate.queryForObject(sql, Long::class.java, datasourceName)
        } catch (e: Exception) {
            // 如果没有找到记录，返回null
            null
        }
    }

    /**
     * 根据数据源名称查找血缘数据源完整信息 (Find lineage datasource info by name)
     *
     * @param datasourceName 数据源名称
     * @return 血缘数据源DTO，如果未找到则返回null
     */
    fun findLineageDatasourceByName(datasourceName: String): LineageDatasourceDto? {
        val sql = """
            SELECT id, datasource_name, db_type, host, port, database_name, status, system_id, connection_string
            FROM lineage_datasources
            WHERE datasource_name = ? AND status = 'ACTIVE'
        """.trimIndent()

        return try {
            val results = jdbcTemplate.query(sql, { rs, _ ->
                LineageDatasourceDto(
                    id = rs.getLong("id"),
                    datasourceName = rs.getString("datasource_name"),
                    dbType = rs.getString("db_type"),
                    host = rs.getString("host"),
                    port = rs.getInt("port"),
                    databaseName = rs.getString("database_name"),
                    status = rs.getString("status"),
                    systemId = rs.getObject("system_id") as? Long,
                    connectionString = rs.getString("connection_string")
                )
            }, datasourceName)

            when {
                results.isEmpty() -> {
                    logger.debug("3f7d9b2e | 未找到数据源: datasourceName=$datasourceName")
                    null
                }
                results.size > 1 -> {
                    logger.warn("5c8a1d4f | 发现多个同名数据源，返回第一个: datasourceName=$datasourceName, count=${results.size}, ids=${results.map { it.id }}")
                    results.first()
                }
                else -> {
                    results.first()
                }
            }
        } catch (e: Exception) {
            logger.error("7e2b6f9c | 查询数据源时发生错误: datasourceName=$datasourceName", e)
            null
        }
    }

    /**
     * 根据数据库信息和表名查询表ID (Find table ID by database info and table name)
     *
     * 支持大小写不敏感匹配，支持 'hive' 与 'hive2' 的映射
     * 查询逻辑：先根据数据库信息找到匹配的数据源，再根据表名和schema查找表
     *
     * @param host 主机地址
     * @param port 端口号
     * @param databaseName 数据库名称
     * @param dbType 数据库类型（支持大小写不敏感，hive/hive2映射）
     * @param tableName 表名称
     * @param schema 模式名称，可选
     * @return 表ID，如果未找到则返回null
     */
    fun findTableIdByDatabaseInfo(
        host: String,
        port: Int,
        databaseName: String,
        dbType: String,
        tableName: String,
        schema: String? = null
    ): Long? {
        logger.info("a9b8c7d6 | 根据数据库信息查询表ID: host=$host, port=$port, databaseName=$databaseName, dbType=$dbType, tableName=$tableName, schema=$schema")

        // 构建数据源匹配的SQL，使用与MetadataService相同的逻辑
        val datasourceSql = """
            SELECT lds.id as datasource_id
            FROM lineage_datasources lds
            WHERE lds.status = 'ACTIVE'
            AND (
                -- 精确匹配：DB类型、主机、端口、数据库名
                (LOWER(lds.db_type) = LOWER(?) 
                 AND lds.host = ? 
                 AND lds.port = ? 
                 AND lds.database_name = ?)
                OR
                -- 支持hive2映射到hive
                (? = 'hive2' AND LOWER(lds.db_type) = 'hive' 
                 AND lds.host = ? 
                 AND lds.port = ? 
                 AND lds.database_name = ?)
                OR
                -- 支持hive映射到hive2
                (? = 'hive' AND LOWER(lds.db_type) = 'hive2' 
                 AND lds.host = ? 
                 AND lds.port = ? 
                 AND lds.database_name = ?)
                OR
                -- 其他类型的大小写不敏感匹配
                (? = 'mysql' AND lds.db_type IN ('Mysql', 'MYSQL', 'mysql')
                 AND lds.host = ? 
                 AND lds.port = ? 
                 AND lds.database_name = ?)
                OR
                (? = 'oracle' AND lds.db_type IN ('Oracle', 'ORACLE', 'oracle')
                 AND lds.host = ? 
                 AND lds.port = ? 
                 AND lds.database_name = ?)
            )
            LIMIT 1
        """.trimIndent()

        return try {
            // 查找匹配的数据源ID
            val datasourceResults = jdbcTemplate.query(
                datasourceSql,
                { rs, _ -> rs.getLong("datasource_id") },
                // 第一组参数：精确匹配
                dbType, host, port, databaseName,
                // 第二组参数：hive2 -> hive
                dbType, host, port, databaseName,
                // 第三组参数：hive -> hive2
                dbType, host, port, databaseName,
                // 第四组参数：mysql大小写
                dbType, host, port, databaseName,
                // 第五组参数：oracle大小写
                dbType, host, port, databaseName
            )

            if (datasourceResults.isEmpty()) {
                logger.debug("e5f4g3h2 | 未找到匹配的数据源: host=$host, port=$port, databaseName=$databaseName, dbType=$dbType")
                return null
            }

            val datasourceId = datasourceResults.first()
            logger.debug("i1j0k9l8 | 找到匹配的数据源: datasourceId=$datasourceId")

            // 根据数据源ID和表名查找表ID
            val tableSql = if (schema != null) {
                """
                SELECT lt.id
                FROM lineage_tables lt
                WHERE lt.datasource_id = ?
                AND LOWER(lt.table_name) = LOWER(?)
                AND (lt.schema_name IS NULL OR LOWER(lt.schema_name) = LOWER(?))
                AND lt.status = 'ACTIVE'
                LIMIT 1
                """.trimIndent()
            } else {
                """
                SELECT lt.id
                FROM lineage_tables lt
                WHERE lt.datasource_id = ?
                AND LOWER(lt.table_name) = LOWER(?)
                AND lt.status = 'ACTIVE'
                LIMIT 1
                """.trimIndent()
            }

            val tableResults = if (schema != null) {
                jdbcTemplate.query(
                    tableSql,
                    { rs, _ -> rs.getLong("id") },
                    datasourceId, tableName, schema
                )
            } else {
                jdbcTemplate.query(
                    tableSql,
                    { rs, _ -> rs.getLong("id") },
                    datasourceId, tableName
                )
            }

            val result = tableResults.firstOrNull()
            if (result != null) {
                logger.info("m7n6o5p4 | 表ID查询成功: tableId=$result")
            } else {
                logger.debug("q3r2s1t0 | 未找到匹配的表: datasourceId=$datasourceId, tableName=$tableName, schema=$schema")
            }

            result

        } catch (e: Exception) {
            logger.error("u9v8w7x6 | 查询表ID时发生错误: host=$host, port=$port, databaseName=$databaseName, dbType=$dbType, tableName=$tableName, schema=$schema", e)
            null
        }
    }

    /**
     * 根据系统ID查询关联的元数据数据源 (Find metadata data sources by system ID)
     *
     * UC-02: 查看系统关联的MetadataDataSourceDto记录
     *
     * @param systemId 系统ID
     * @return 元数据数据源列表
     */
    fun findMetadataDataSourcesBySystemId(systemId: Long): List<MetadataDataSourceDto> {
        val sql = """
            SELECT DISTINCT
                mds.ID, mds.SOURCE_NAME, mds.DB_TYPE, mds.DB_DRIVER, mds.DB_NAME,
                mds.DB_URL, mds.DB_PORT, mds.DB_USERNAME, mds.CUSTOM_JDBC_URL,
                mds.ACTIVE_FLAG, mds.CREATE_BY, mds.CREATE_TIME, mds.UPDATE_BY, 
                mds.UPDATE_TIME, mds.SYSTEM_ID, mds.DESCRIPTION,
                msi.ID as SYS_ID, msi.SYSTEM_NAME, msi.SYSTEM_ABBREVIATION, 
                msi.SYSTEM_TYPE, msi.SYSTEM_MODULE, msi.MODULE_OWNER,
                msi.DEVELOPMENT_DEPARTMENT, msi.ACTIVE_FLAG as SYS_ACTIVE_FLAG,
                msi.CREATE_BY as SYS_CREATE_BY, msi.CREATE_TIME as SYS_CREATE_TIME,
                msi.UPDATE_BY as SYS_UPDATE_BY, msi.UPDATE_TIME as SYS_UPDATE_TIME,
                CASE WHEN ml.DATA_SOURCE_ID IS NOT NULL THEN TRUE ELSE FALSE END as HAS_BEEN_COLLECTED
            FROM metadata_data_source mds
            LEFT JOIN metadata_system_info msi ON mds.SYSTEM_ID = msi.ID
            LEFT JOIN metadata_latest ml ON mds.ID = ml.DATA_SOURCE_ID
            WHERE mds.SYSTEM_ID = ? AND mds.ACTIVE_FLAG = 1
            ORDER BY mds.SOURCE_NAME
        """.trimIndent()

        return jdbcTemplate.query(sql, metadataDataSourceRowMapper, systemId)
    }

    /**
     * 根据系统ID查询关联的表级血缘关系 (Find table lineage by system ID)
     *
     * UC-03: 查看系统关联的TableLineageView记录
     *
     * @param systemId 系统ID
     * @return 表级血缘视图列表
     */
    fun findTableLineageBySystemId(systemId: Long): List<TableLineageView> {
        val sql = """
            SELECT DISTINCT
                lr.id as relationship_id,
                lr.source_table_id,
                lr.target_table_id,
                st.table_name as source_table,
                st.schema_name as source_schema,
                st.chinese_name as source_chinese_name,
                sds.datasource_name as source_datasource,
                ssys.system_name as source_system,
                tt.table_name as target_table,
                tt.schema_name as target_schema,
                tds.datasource_name as target_datasource,
                lr.lineage_type,
                lr.source_system as lineage_source,
                lr.confidence_score,
                lr.created_at,
                1 as level
            FROM lineage_relationships lr
            JOIN lineage_tables st ON lr.source_table_id = st.id
            JOIN lineage_tables tt ON lr.target_table_id = tt.id
            JOIN lineage_datasources sds ON st.datasource_id = sds.id
            JOIN lineage_datasources tds ON tt.datasource_id = tds.id
            LEFT JOIN lineage_systems ssys ON sds.system_id = ssys.id
            LEFT JOIN lineage_systems tsys ON tds.system_id = tsys.id
            WHERE lr.relationship_type = 'TABLE_LEVEL'
              AND lr.is_active = true
              AND (sds.system_id = ? OR tds.system_id = ?)
            ORDER BY lr.created_at DESC, st.table_name, tt.table_name
        """.trimIndent()

        return jdbcTemplate.query(sql, tableLineageViewRowMapper, systemId, systemId)
    }

    /**
     * MetadataDataSourceDto 行映射器
     */
    private val metadataDataSourceRowMapper = RowMapper<MetadataDataSourceDto> { rs, _ ->
        MetadataDataSourceDto(
            id = rs.getLong("ID"),
            sourceName = rs.getString("SOURCE_NAME"),
            dbType = rs.getString("DB_TYPE"),
            dbDriver = rs.getString("DB_DRIVER"),
            dbName = rs.getString("DB_NAME"),
            dbUrl = rs.getString("DB_URL"),
            dbPort = rs.getObject("DB_PORT") as? Int,
            dbUsername = rs.getString("DB_USERNAME"),
            customJdbcUrl = rs.getString("CUSTOM_JDBC_URL"),
            activeFlag = rs.getBoolean("ACTIVE_FLAG"),
            createBy = rs.getString("CREATE_BY"),
            createTime = rs.getTimestamp("CREATE_TIME")?.toLocalDateTime(),
            updateBy = rs.getString("UPDATE_BY"),
            updateTime = rs.getTimestamp("UPDATE_TIME")?.toLocalDateTime(),
            systemId = rs.getObject("SYSTEM_ID") as? Long,
            description = rs.getString("DESCRIPTION"),
            systemInfo = if (rs.getObject("SYS_ID") != null) {
                MetadataSystemInfoDto(
                    id = rs.getLong("SYS_ID"),
                    systemName = rs.getString("SYSTEM_NAME"),
                    systemAbbreviation = rs.getString("SYSTEM_ABBREVIATION"),
                    systemType = rs.getString("SYSTEM_TYPE"),
                    systemModule = rs.getString("SYSTEM_MODULE"),
                    moduleOwner = rs.getString("MODULE_OWNER"),
                    developmentDepartment = rs.getString("DEVELOPMENT_DEPARTMENT"),
                    activeFlag = rs.getBoolean("SYS_ACTIVE_FLAG"),
                    createBy = rs.getString("SYS_CREATE_BY"),
                    createTime = rs.getTimestamp("SYS_CREATE_TIME")?.toLocalDateTime(),
                    updateBy = rs.getString("SYS_UPDATE_BY"),
                    updateTime = rs.getTimestamp("SYS_UPDATE_TIME")?.toLocalDateTime()
                )
            } else null,
            hasBeenCollected = rs.getBoolean("HAS_BEEN_COLLECTED")
        )
    }

    /**
     * 根据元数据数据源ID过滤表级血缘关系 (Find table lineage by metadata data source ID)
     *
     * UC-04: 从数据源快速导航到表
     * 根据元数据数据源的数据库信息，匹配lineage_datasources，并过滤表级血缘关系
     *
     * @param metadataDataSourceId 元数据数据源ID
     * @return 过滤后的表级血缘视图列表
     */
    fun findTableLineageByMetadataDataSource(metadataDataSourceId: Long): List<TableLineageView> {
        val sql = """
            SELECT DISTINCT
                lr.id as relationship_id,
                lr.source_table_id,
                lr.target_table_id,
                st.table_name as source_table,
                st.schema_name as source_schema,
                st.chinese_name as source_chinese_name,
                sds.datasource_name as source_datasource,
                ssys.system_name as source_system,
                tt.table_name as target_table,
                tt.schema_name as target_schema,
                tds.datasource_name as target_datasource,
                lr.lineage_type,
                lr.source_system as lineage_source,
                lr.confidence_score,
                lr.created_at,
                1 as level
            FROM lineage_relationships lr
            JOIN lineage_tables st ON lr.source_table_id = st.id
            JOIN lineage_tables tt ON lr.target_table_id = tt.id
            JOIN lineage_datasources sds ON st.datasource_id = sds.id
            JOIN lineage_datasources tds ON tt.datasource_id = tds.id
            LEFT JOIN lineage_systems ssys ON sds.system_id = ssys.id
            WHERE lr.relationship_type = 'TABLE_LEVEL'
              AND lr.is_active = true
              AND (
                sds.id IN (
                    SELECT lds.id FROM lineage_datasources lds
                    JOIN metadata_data_source mds ON (
                        LOWER(lds.db_type) = LOWER(mds.DB_TYPE)
                        AND lds.database_name = mds.DB_NAME
                        AND (
                            (mds.DB_URL IS NOT NULL AND lds.host LIKE CONCAT('%', SUBSTRING_INDEX(SUBSTRING_INDEX(mds.DB_URL, '://', -1), ':', 1), '%'))
                            OR (mds.CUSTOM_JDBC_URL IS NOT NULL AND mds.CUSTOM_JDBC_URL LIKE CONCAT('%', lds.host, '%'))
                        )
                        AND (mds.DB_PORT IS NULL OR lds.port = mds.DB_PORT)
                    )
                    WHERE mds.ID = ? AND mds.ACTIVE_FLAG = 1
                )
                OR tds.id IN (
                    SELECT lds.id FROM lineage_datasources lds
                    JOIN metadata_data_source mds ON (
                        LOWER(lds.db_type) = LOWER(mds.DB_TYPE)
                        AND lds.database_name = mds.DB_NAME
                        AND (
                            (mds.DB_URL IS NOT NULL AND lds.host LIKE CONCAT('%', SUBSTRING_INDEX(SUBSTRING_INDEX(mds.DB_URL, '://', -1), ':', 1), '%'))
                            OR (mds.CUSTOM_JDBC_URL IS NOT NULL AND mds.CUSTOM_JDBC_URL LIKE CONCAT('%', lds.host, '%'))
                        )
                        AND (mds.DB_PORT IS NULL OR lds.port = mds.DB_PORT)
                    )
                    WHERE mds.ID = ? AND mds.ACTIVE_FLAG = 1
                )
              )
            ORDER BY lr.created_at DESC, st.table_name, tt.table_name
        """.trimIndent()

        return jdbcTemplate.query(sql, tableLineageViewRowMapper, metadataDataSourceId, metadataDataSourceId)
    }

    /**
     * 查询作业处理历史 (Query job processing history)
     *
     * UC-06: 支持查看手动血缘收集的执行结果
     *
     * @param jobKey 作业键，可选过滤条件
     * @param limit 返回记录数限制
     * @return 作业处理历史列表
     */
    fun findJobProcessingHistory(jobKey: String? = null, limit: Int = 50): List<JobProcessingHistory> {
        val sql = if (jobKey != null) {
            """
                SELECT id, job_key, job_type, reader_job_id, write_job_id, processed_at,
                       processing_result, changes_detected, processing_duration_ms, 
                       lineage_hash, error_message
                FROM lineage_job_processing_history 
                WHERE job_key = ?
                ORDER BY processed_at DESC 
                LIMIT ?
            """.trimIndent()
        } else {
            """
                SELECT id, job_key, job_type, reader_job_id, write_job_id, processed_at,
                       processing_result, changes_detected, processing_duration_ms, 
                       lineage_hash, error_message
                FROM lineage_job_processing_history 
                ORDER BY processed_at DESC 
                LIMIT ?
            """.trimIndent()
        }

        return if (jobKey != null) {
            jdbcTemplate.query(sql, jobProcessingHistoryRowMapper, jobKey, limit)
        } else {
            jdbcTemplate.query(sql, jobProcessingHistoryRowMapper, limit)
        }
    }

    /**
     * JobProcessingHistory 行映射器
     */
    private val jobProcessingHistoryRowMapper = RowMapper<JobProcessingHistory> { rs, _ ->
        JobProcessingHistory(
            id = rs.getLong("id"),
            jobKey = rs.getString("job_key"),
            jobType = JobType.valueOf(rs.getString("job_type")),
            readerJobId = rs.getString("reader_job_id"),
            writeJobId = rs.getString("write_job_id"),
            processedAt = rs.getTimestamp("processed_at").toLocalDateTime(),
            processingResult = ProcessingResult.valueOf(rs.getString("processing_result")),
            changesDetected = rs.getBoolean("changes_detected"),
            processingDurationMs = rs.getLong("processing_duration_ms"),
            lineageHash = rs.getString("lineage_hash"),
            errorMessage = rs.getString("error_message")
        )
    }

    // ==================== 手动血缘管理方法 (Manual Lineage Management Methods) ====================

    /**
     * 查找现有的表级血缘关系 (Find existing table relationship)
     *
     * UC-14: 验证是否已存在相同的血缘关系，避免重复创建
     */
    fun findExistingTableRelationship(sourceTableId: Long, targetTableId: Long): Long? {
        val sql = """
            SELECT id FROM lineage_relationships 
            WHERE source_table_id = ? AND target_table_id = ? 
              AND relationship_type = 'TABLE_LEVEL' AND is_active = true
        """.trimIndent()

        return try {
            jdbcTemplate.queryForObject(sql, Long::class.java, sourceTableId, targetTableId)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 创建手动表级血缘关系 (Create manual table lineage)
     *
     * UC-14: 创建新的手动血缘记录
     */
    @Transactional
    fun createManualTableLineage(
        sourceTableId: Long,
        targetTableId: Long,
        lineageType: String,
        description: String?,
        confidenceScore: java.math.BigDecimal,
        createdBy: String
    ): Long {
        val sql = """
            INSERT INTO lineage_relationships (
                source_table_id, target_table_id, relationship_type, lineage_type,
                description, confidence_score, source_system, created_by, created_at, 
                updated_at, is_active
            ) VALUES (?, ?, 'TABLE_LEVEL', ?, ?, ?, 'MANUAL_INPUT', ?, CURRENT_TIMESTAMP, 
                     CURRENT_TIMESTAMP, true)
        """.trimIndent()

        val keyHolder = GeneratedKeyHolder()
        jdbcTemplate.update({ connection ->
            val ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)
            ps.setLong(1, sourceTableId)
            ps.setLong(2, targetTableId)
            ps.setString(3, lineageType)
            ps.setString(4, description)
            ps.setBigDecimal(5, confidenceScore)
            ps.setString(6, createdBy)
            ps
        }, keyHolder)

        return keyHolder.key?.toLong() ?: throw RuntimeException("创建血缘关系失败：无法获取生成的ID")
    }

    /**
     * 更新手动表级血缘关系 (Update manual table lineage)
     *
     * UC-15: 编辑现有的手动血缘记录
     */
    @Transactional
    fun updateManualTableLineage(
        relationshipId: Long,
        lineageType: String?,
        description: String?,
        confidenceScore: java.math.BigDecimal?,
        updatedBy: String
    ): Int {
        val setParts = mutableListOf<String>()
        val parameters = mutableListOf<Any?>()

        lineageType?.let {
            setParts.add("lineage_type = ?")
            parameters.add(it)
        }

        description?.let {
            setParts.add("description = ?")
            parameters.add(it)
        }

        confidenceScore?.let {
            setParts.add("confidence_score = ?")
            parameters.add(it)
        }

        if (setParts.isEmpty()) return 0

        setParts.add("last_updated_by = ?")
        setParts.add("updated_at = CURRENT_TIMESTAMP")
        parameters.add(updatedBy)
        parameters.add(relationshipId)

        val sql = """
            UPDATE lineage_relationships 
            SET ${setParts.joinToString(", ")}
            WHERE id = ? AND source_system = 'MANUAL_INPUT' AND is_active = true
        """.trimIndent()

        return jdbcTemplate.update(sql, *parameters.toTypedArray())
    }

    /**
     * 删除列映射（通过表级关系ID）(Delete column mappings by table relationship ID)
     *
     * UC-16: 删除血缘关系时级联删除列映射 - 软删除相关的COLUMN_LEVEL关系
     */
    @Transactional
    fun deleteColumnMappingsByRelationshipId(tableRelationshipId: Long): Int {
        // 1. 获取表级关系的表ID
        val tableInfo = jdbcTemplate.queryForObject(
            "SELECT source_table_id, target_table_id FROM lineage_relationships WHERE id = ?",
            { rs, _ ->
                Pair(rs.getLong("source_table_id"), rs.getLong("target_table_id"))
            },
            tableRelationshipId
        ) ?: return 0

        // 2. 软删除相关的COLUMN_LEVEL关系
        val sql = """
            UPDATE lineage_relationships 
            SET is_active = false, updated_at = CURRENT_TIMESTAMP
            WHERE relationship_type = 'COLUMN_LEVEL'
              AND source_table_id = ? AND target_table_id = ?
              AND source_system = 'MANUAL_INPUT' AND is_active = true
        """.trimIndent()

        return jdbcTemplate.update(sql, tableInfo.first, tableInfo.second)
    }

    /**
     * 删除手动表级血缘关系 (Delete manual table lineage)
     *
     * UC-16: 删除手动血缘记录
     */
    @Transactional
    fun deleteManualTableLineage(relationshipId: Long, deletedBy: String, reason: String?): Int {
        val sql = """
            UPDATE lineage_relationships 
            SET is_active = false, last_updated_by = ?, updated_at = CURRENT_TIMESTAMP,
                description = CONCAT(COALESCE(description, ''), ' [删除原因: ', COALESCE(?, '无'), ']')
            WHERE id = ? AND source_system = 'MANUAL_INPUT' AND is_active = true
        """.trimIndent()

        return jdbcTemplate.update(sql, deletedBy, reason ?: "无", relationshipId)
    }

    /**
     * 查找手动血缘详情 (Find manual lineage by ID)
     */
    fun findManualLineageById(relationshipId: Long): ManualLineageDetailsDto? {
        val sql = """
            SELECT lr.id as relationship_id, lr.lineage_type, lr.description, 
                   lr.confidence_score, lr.source_system, lr.created_by, lr.created_at,
                   lr.last_updated_by, lr.updated_at,
                   st.id as source_table_id, st.table_name as source_table_name, 
                   st.schema_name as source_schema, st.chinese_name as source_chinese_name,
                   sds.datasource_name as source_datasource, ssys.system_name as source_system_name,
                   tt.id as target_table_id, tt.table_name as target_table_name,
                   tt.schema_name as target_schema, tt.chinese_name as target_chinese_name,
                   tds.datasource_name as target_datasource, tsys.system_name as target_system_name
            FROM lineage_relationships lr
            JOIN lineage_tables st ON lr.source_table_id = st.id
            JOIN lineage_tables tt ON lr.target_table_id = tt.id
            JOIN lineage_datasources sds ON st.datasource_id = sds.id
            JOIN lineage_datasources tds ON tt.datasource_id = tds.id
            LEFT JOIN lineage_systems ssys ON sds.system_id = ssys.id
            LEFT JOIN lineage_systems tsys ON tds.system_id = tsys.id
            WHERE lr.id = ? AND lr.is_active = true
        """.trimIndent()

        return try {
            jdbcTemplate.queryForObject(sql, { rs, _ ->
                ManualLineageDetailsDto(
                    relationshipId = rs.getLong("relationship_id"),
                    sourceTable = TableInfoDto(
                        tableId = rs.getLong("source_table_id"),
                        tableName = rs.getString("source_table_name"),
                        schema = rs.getString("source_schema"),
                        datasource = rs.getString("source_datasource"),
                        system = rs.getString("source_system_name"),
                        chineseName = rs.getString("source_chinese_name"),
                        description = null
                    ),
                    targetTable = TableInfoDto(
                        tableId = rs.getLong("target_table_id"),
                        tableName = rs.getString("target_table_name"),
                        schema = rs.getString("target_schema"),
                        datasource = rs.getString("target_datasource"),
                        system = rs.getString("target_system_name"),
                        chineseName = rs.getString("target_chinese_name"),
                        description = null
                    ),
                    lineageType = rs.getString("lineage_type"),
                    description = rs.getString("description"),
                    confidenceScore = rs.getBigDecimal("confidence_score"),
                    sourceType = rs.getString("source_system"),
                    createdBy = rs.getString("created_by"),
                    createdAt = rs.getTimestamp("created_at")?.toLocalDateTime(),
                    lastUpdatedBy = rs.getString("last_updated_by"),
                    lastUpdatedAt = rs.getTimestamp("updated_at")?.toLocalDateTime(),
                    isEditable = rs.getString("source_system") == "MANUAL_INPUT",
                    columnMappings = emptyList(), // 将在调用处获取
                    editPermissions = EditPermissionsDto(true, true, true, emptyList())
                )
            }, relationshipId)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 查找列映射（通过表级关系ID）(Find column mappings by table relationship ID)
     * 
     * 现在查询COLUMN_LEVEL关系而不是单独的映射表
     */
    fun findColumnMappingsByRelationshipId(tableRelationshipId: Long): List<ColumnMappingDetailsDto> {
        // 1. 获取表级关系的表ID
        val tableInfo = jdbcTemplate.queryForObject(
            "SELECT source_table_id, target_table_id FROM lineage_relationships WHERE id = ?",
            { rs, _ ->
                Pair(rs.getLong("source_table_id"), rs.getLong("target_table_id"))
            },
            tableRelationshipId
        ) ?: return emptyList()

        // 2. 查询相关的COLUMN_LEVEL关系
        val sql = """
            SELECT lr.id as mapping_id, 
                   sc.column_name as source_column_name, sc.data_type as source_data_type,
                   tc.column_name as target_column_name, tc.data_type as target_data_type,
                   lr.transformation_type, lr.transformation_description, lr.transformation_expression,
                   lr.confidence_score
            FROM lineage_relationships lr
            JOIN lineage_columns sc ON lr.source_column_id = sc.id
            JOIN lineage_columns tc ON lr.target_column_id = tc.id
            WHERE lr.relationship_type = 'COLUMN_LEVEL'
              AND lr.source_table_id = ? AND lr.target_table_id = ?
              AND lr.source_system = 'MANUAL_INPUT' AND lr.is_active = true
            ORDER BY sc.column_name, tc.column_name
        """.trimIndent()

        return jdbcTemplate.query(sql, { rs, _ ->
            ColumnMappingDetailsDto(
                relationshipId = rs.getLong("mapping_id"),
                sourceColumn = rs.getString("source_column_name"),
                sourceDataType = rs.getString("source_data_type"),
                targetColumn = rs.getString("target_column_name"),
                targetDataType = rs.getString("target_data_type"),
                transformationType = rs.getString("transformation_type"),
                transformationDescription = rs.getString("transformation_description"),
                transformationExpression = rs.getString("transformation_expression"),
                confidenceScore = rs.getBigDecimal("confidence_score"),
                isEditable = true
            )
        }, tableInfo.first, tableInfo.second)
    }

    /**
     * 搜索手动血缘关系 (Search manual lineage)
     */
    fun searchManualLineage(request: ManualLineageSearchDto): List<ManualLineageDetailsDto> {
        val whereClauses = mutableListOf<String>()
        val parameters = mutableListOf<Any?>()

        whereClauses.add("lr.is_active = true")
        whereClauses.add("lr.source_system = 'MANUAL_INPUT'")

        request.sourceTableName?.let {
            whereClauses.add("st.table_name LIKE ?")
            parameters.add("%$it%")
        }

        request.targetTableName?.let {
            whereClauses.add("tt.table_name LIKE ?")
            parameters.add("%$it%")
        }

        request.lineageType?.let {
            whereClauses.add("lr.lineage_type = ?")
            parameters.add(it)
        }

        request.createdBy?.let {
            whereClauses.add("lr.created_by = ?")
            parameters.add(it)
        }

        request.createdAfter?.let {
            whereClauses.add("lr.created_at >= ?")
            parameters.add(it)
        }

        request.createdBefore?.let {
            whereClauses.add("lr.created_at <= ?")
            parameters.add(it)
        }

        request.minConfidenceScore?.let {
            whereClauses.add("lr.confidence_score >= ?")
            parameters.add(it)
        }

        val sql = """
            SELECT lr.id as relationship_id, lr.lineage_type, lr.description, 
                   lr.confidence_score, lr.source_system, lr.created_by, lr.created_at,
                   lr.last_updated_by, lr.updated_at,
                   st.id as source_table_id, st.table_name as source_table_name, 
                   st.schema_name as source_schema, st.chinese_name as source_chinese_name,
                   sds.datasource_name as source_datasource, ssys.system_name as source_system_name,
                   tt.id as target_table_id, tt.table_name as target_table_name,
                   tt.schema_name as target_schema, tt.chinese_name as target_chinese_name,
                   tds.datasource_name as target_datasource, tsys.system_name as target_system_name
            FROM lineage_relationships lr
            JOIN lineage_tables st ON lr.source_table_id = st.id
            JOIN lineage_tables tt ON lr.target_table_id = tt.id
            JOIN lineage_datasources sds ON st.datasource_id = sds.id
            JOIN lineage_datasources tds ON tt.datasource_id = tds.id
            LEFT JOIN lineage_systems ssys ON sds.system_id = ssys.id
            LEFT JOIN lineage_systems tsys ON tds.system_id = tsys.id
            WHERE ${whereClauses.joinToString(" AND ")}
            ORDER BY ${request.sortBy} ${request.sortDirection}
            LIMIT ? OFFSET ?
        """.trimIndent()

        parameters.add(request.size)
        parameters.add(request.page * request.size)

        return jdbcTemplate.query(sql, { rs, _ ->
            ManualLineageDetailsDto(
                relationshipId = rs.getLong("relationship_id"),
                sourceTable = TableInfoDto(
                    tableId = rs.getLong("source_table_id"),
                    tableName = rs.getString("source_table_name"),
                    schema = rs.getString("source_schema"),
                    datasource = rs.getString("source_datasource"),
                    system = rs.getString("source_system_name"),
                    chineseName = rs.getString("source_chinese_name"),
                    description = null
                ),
                targetTable = TableInfoDto(
                    tableId = rs.getLong("target_table_id"),
                    tableName = rs.getString("target_table_name"),
                    schema = rs.getString("target_schema"),
                    datasource = rs.getString("target_datasource"),
                    system = rs.getString("target_system_name"),
                    chineseName = rs.getString("target_chinese_name"),
                    description = null
                ),
                lineageType = rs.getString("lineage_type"),
                description = rs.getString("description"),
                confidenceScore = rs.getBigDecimal("confidence_score"),
                sourceType = rs.getString("source_system"),
                createdBy = rs.getString("created_by"),
                createdAt = rs.getTimestamp("created_at")?.toLocalDateTime(),
                lastUpdatedBy = rs.getString("last_updated_by"),
                lastUpdatedAt = rs.getTimestamp("updated_at")?.toLocalDateTime(),
                isEditable = true,
                columnMappings = emptyList(), // 在需要时单独查询
                editPermissions = EditPermissionsDto(true, true, true, emptyList())
            )
        }, *parameters.toTypedArray())
    }

    /**
     * 获取手动血缘统计信息 (Get manual lineage statistics)
     */
    fun getManualLineageStatistics(): ManualLineageStatisticsDto {
        // 总手动血缘关系数
        val totalRelationships = jdbcTemplate.queryForObject(
            "SELECT COUNT(*) FROM lineage_relationships WHERE source_system = 'MANUAL_INPUT' AND is_active = true",
            Int::class.java
        ) ?: 0

        // 总列映射数 (现在直接统计COLUMN_LEVEL关系)
        val totalColumnMappings = jdbcTemplate.queryForObject(
            """
            SELECT COUNT(*) FROM lineage_relationships 
            WHERE relationship_type = 'COLUMN_LEVEL' 
              AND source_system = 'MANUAL_INPUT' AND is_active = true
            """.trimIndent(),
            Int::class.java
        ) ?: 0

        // 平均置信度
        val avgConfidence = jdbcTemplate.queryForObject(
            "SELECT AVG(confidence_score) FROM lineage_relationships WHERE source_system = 'MANUAL_INPUT' AND is_active = true",
            java.math.BigDecimal::class.java
        )

        // 顶级创建者
        val topCreators = jdbcTemplate.query(
            """
            SELECT created_by, COUNT(*) as count, MAX(created_at) as last_activity
            FROM lineage_relationships 
            WHERE source_system = 'MANUAL_INPUT' AND is_active = true AND created_by IS NOT NULL
            GROUP BY created_by 
            ORDER BY count DESC 
            LIMIT 10
            """.trimIndent()
        ) { rs, _ ->
            CreatorStatDto(
                createdBy = rs.getString("created_by"),
                relationshipCount = rs.getInt("count"),
                lastActivity = rs.getTimestamp("last_activity")?.toLocalDateTime()
            )
        }

        // 血缘类型分布
        val typeDistribution = jdbcTemplate.query(
            """
            SELECT lineage_type, COUNT(*) as count
            FROM lineage_relationships 
            WHERE source_system = 'MANUAL_INPUT' AND is_active = true
            GROUP BY lineage_type
            """.trimIndent()
        ) { rs, _ ->
            rs.getString("lineage_type") to rs.getInt("count")
        }.toMap()

        // 最近活动
        val recentActivity = jdbcTemplate.query(
            """
            SELECT lr.id, lr.created_by, lr.created_at, lr.last_updated_by, lr.updated_at,
                   st.table_name as source_table, tt.table_name as target_table
            FROM lineage_relationships lr
            JOIN lineage_tables st ON lr.source_table_id = st.id
            JOIN lineage_tables tt ON lr.target_table_id = tt.id
            WHERE lr.source_system = 'MANUAL_INPUT' AND lr.is_active = true
            ORDER BY GREATEST(lr.created_at, COALESCE(lr.updated_at, lr.created_at)) DESC
            LIMIT 20
            """.trimIndent()
        ) { rs, _ ->
            val createdAt = rs.getTimestamp("created_at")?.toLocalDateTime()
            val updatedAt = rs.getTimestamp("updated_at")?.toLocalDateTime()
            val lastModified = if (updatedAt != null && updatedAt.isAfter(createdAt)) updatedAt else createdAt
            val action = if (updatedAt != null && updatedAt.isAfter(createdAt)) "UPDATE" else "CREATE"
            val performer = rs.getString("last_updated_by") ?: rs.getString("created_by")

            RecentActivityDto(
                relationshipId = rs.getLong("id"),
                action = action,
                performer = performer ?: "unknown",
                timestamp = lastModified ?: LocalDateTime.now(),
                description = "${action.lowercase()}: ${rs.getString("source_table")} → ${rs.getString("target_table")}"
            )
        }

        return ManualLineageStatisticsDto(
            totalManualRelationships = totalRelationships,
            totalColumnMappings = totalColumnMappings,
            avgConfidenceScore = avgConfidence,
            topCreators = topCreators,
            lineageTypeDistribution = typeDistribution,
            recentActivity = recentActivity
        )
    }

    /**
     * 查找血缘关系基础信息 (Find lineage relationship basic info)
     */
    fun findLineageRelationshipById(relationshipId: Long): LineageRelationshipInfo? {
        val sql = """
            SELECT id, source_system, created_by, created_at
            FROM lineage_relationships 
            WHERE id = ? AND is_active = true
        """.trimIndent()

        return try {
            jdbcTemplate.queryForObject(sql, { rs, _ ->
                LineageRelationshipInfo(
                    relationshipId = rs.getLong("id"),
                    sourceType = rs.getString("source_system"),
                    createdBy = rs.getString("created_by"),
                    createdAt = rs.getTimestamp("created_at")?.toLocalDateTime()
                )
            }, relationshipId)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 创建手动列映射 (Create manual column mapping)
     *
     * UC-14: 创建列级血缘映射 - 使用COLUMN_LEVEL关系而不是单独的映射表
     */
    @Transactional
    fun createManualColumnMapping(
        tableRelationshipId: Long,
        sourceColumnName: String,
        targetColumnName: String,
        transformationType: String?,
        transformationDescription: String?,
        transformationExpression: String?,
        confidenceScore: java.math.BigDecimal
    ): Long {
        // 1. 获取表级关系的表ID
        val tableInfo = jdbcTemplate.queryForObject(
            "SELECT source_table_id, target_table_id FROM lineage_relationships WHERE id = ?",
            { rs, _ ->
                Pair(rs.getLong("source_table_id"), rs.getLong("target_table_id"))
            },
            tableRelationshipId
        ) ?: throw RuntimeException("找不到表级血缘关系: $tableRelationshipId")

        // 2. 获取或创建源列和目标列ID
        val sourceColumnId = getOrCreateColumnByTableIdAndName(tableInfo.first, sourceColumnName)
        val targetColumnId = getOrCreateColumnByTableIdAndName(tableInfo.second, targetColumnName)

        // 3. 创建COLUMN_LEVEL血缘关系
        val sql = """
            INSERT INTO lineage_relationships (
                relationship_type, source_table_id, target_table_id, 
                source_column_id, target_column_id,
                transformation_type, transformation_description, transformation_expression,
                confidence_score, source_system, is_active, created_at
            ) VALUES ('COLUMN_LEVEL', ?, ?, ?, ?, ?, ?, ?, ?, 'MANUAL_INPUT', true, CURRENT_TIMESTAMP)
        """.trimIndent()

        val keyHolder = GeneratedKeyHolder()
        jdbcTemplate.update({ connection ->
            val ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)
            ps.setLong(1, tableInfo.first)
            ps.setLong(2, tableInfo.second)
            ps.setLong(3, sourceColumnId)
            ps.setLong(4, targetColumnId)
            ps.setString(5, transformationType)
            ps.setString(6, transformationDescription)
            ps.setString(7, transformationExpression)
            ps.setBigDecimal(8, confidenceScore)
            ps
        }, keyHolder)

        return keyHolder.key?.toLong() ?: throw RuntimeException("创建列映射失败：无法获取生成的ID")
    }

    /**
     * 更新手动列映射 (Update manual column mapping)
     *
     * UC-15: 编辑列级血缘映射 - 更新COLUMN_LEVEL关系
     */
    @Transactional
    fun updateManualColumnMapping(
        relationshipId: Long,
        transformationType: String?,
        transformationDescription: String?,
        transformationExpression: String?,
        confidenceScore: java.math.BigDecimal?
    ): Int {
        val setParts = mutableListOf<String>()
        val parameters = mutableListOf<Any?>()

        transformationType?.let {
            setParts.add("transformation_type = ?")
            parameters.add(it)
        }

        transformationDescription?.let {
            setParts.add("transformation_description = ?")
            parameters.add(it)
        }

        transformationExpression?.let {
            setParts.add("transformation_expression = ?")
            parameters.add(it)
        }

        confidenceScore?.let {
            setParts.add("confidence_score = ?")
            parameters.add(it)
        }

        if (setParts.isEmpty()) return 0

        setParts.add("updated_at = CURRENT_TIMESTAMP")
        parameters.add(relationshipId)

        val sql = """
            UPDATE lineage_relationships 
            SET ${setParts.joinToString(", ")}
            WHERE id = ? AND relationship_type = 'COLUMN_LEVEL' 
              AND source_system = 'MANUAL_INPUT' AND is_active = true
        """.trimIndent()

        return jdbcTemplate.update(sql, *parameters.toTypedArray())
    }

    /**
     * 删除列映射 (Delete column mapping)
     *
     * UC-16: 删除单个列映射 - 软删除COLUMN_LEVEL关系
     */
    @Transactional
    fun deleteColumnMapping(mappingId: Long): Int {
        val sql = """
            UPDATE lineage_relationships 
            SET is_active = false, updated_at = CURRENT_TIMESTAMP
            WHERE id = ? AND relationship_type = 'COLUMN_LEVEL' 
              AND source_system = 'MANUAL_INPUT' AND is_active = true
        """.trimIndent()
        return jdbcTemplate.update(sql, mappingId)
    }

    /**
     * 手动血缘管理：获取或创建数据源 (Manual Lineage Management: Get or create datasource)
     * 
     * 提供给手动血缘服务使用的公共方法
     */
    fun getOrCreateDatasourceForManualLineage(databaseInfo: DatabaseInfo): Long {
        return getOrCreateDataSource(databaseInfo)
    }

    /**
     * 手动血缘管理：获取或创建表 (Manual Lineage Management: Get or create table)
     * 
     * 提供给手动血缘服务使用的公共方法
     */
    fun getOrCreateTableForManualLineage(tableInfo: TableInfo, datasourceId: Long): Long {
        return getOrCreateTable(tableInfo, datasourceId)
    }

    /**
     * 手动血缘管理：获取或创建列 (Manual Lineage Management: Get or create column)
     * 
     * 提供给手动血缘服务使用的公共方法
     */
    fun getOrCreateColumnForManualLineage(columnInfo: ColumnInfo): Long {
        return getOrCreateColumn(columnInfo)
    }

    /**
     * 通过表ID和列名获取或创建列 (Get or create column by table ID and column name)
     * 
     * 用于列映射的简化方法
     */
    fun getOrCreateColumnByTableIdAndName(tableId: Long, columnName: String): Long {
        // 查询是否已存在列
        val existingId = jdbcTemplate.queryForList(
            """
            SELECT id FROM lineage_columns 
            WHERE table_id = ? AND column_name = ?
            """.trimIndent(),
            Long::class.java,
            tableId, columnName
        ).singleOrNull() ?: run {
            // 不存在则创建（使用默认值）
            val keyHolder = GeneratedKeyHolder()
            jdbcTemplate.update({ connection ->
                val ps = connection.prepareStatement(
                    """
                    INSERT INTO lineage_columns (table_id, column_name, data_type, column_comment)
                    VALUES (?, ?, 'VARCHAR', '手动创建的列')
                    """.trimIndent(),
                    Statement.RETURN_GENERATED_KEYS
                )
                ps.setLong(1, tableId)
                ps.setString(2, columnName)
                ps
            }, keyHolder)
            keyHolder.key!!.toLong()
        }
        return existingId
    }

    // ====================================================================
    // 简化的CRUD操作方法 (Simplified CRUD Operations Repository Methods)
    // ====================================================================

    /**
     * 创建简化的表级血缘关系 (Create simplified table lineage)
     */
    @Transactional
    fun createSimplifiedTableLineage(
        sourceTableId: Long,
        targetTableId: Long,
        createdBy: String
    ): Long {
        val sql = """
            INSERT INTO lineage_relationships (
                relationship_type, source_table_id, target_table_id,
                lineage_type, source_system, confidence_score, is_active, 
                created_by, created_at, job_key
            ) VALUES ('TABLE_LEVEL', ?, ?, 'DIRECT_COPY', 'MANUAL_INPUT', 1.0, true, ?, CURRENT_TIMESTAMP, ?)
        """.trimIndent()

        val keyHolder = GeneratedKeyHolder()
        jdbcTemplate.update({ connection ->
            val ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)
            ps.setLong(1, sourceTableId)
            ps.setLong(2, targetTableId)
            ps.setString(3, createdBy)
            ps.setString(4, ManualLineageService.MANUAL_INPUT_JOB_KEY)
            ps
        }, keyHolder)

        return keyHolder.key?.toLong() ?: throw RuntimeException("创建表级血缘关系失败：无法获取生成的ID")
    }

    /**
     * 创建简化的列级血缘关系 (Create simplified column lineage)
     */
    @Transactional
    fun createSimplifiedColumnLineage(
        sourceTableId: Long,
        targetTableId: Long,
        sourceColumnId: Long,
        targetColumnId: Long,
        createdBy: String
    ): Long {
        val sql = """
            INSERT INTO lineage_relationships (
                relationship_type, source_table_id, target_table_id,
                source_column_id, target_column_id,
                transformation_type, source_system, confidence_score, is_active,
                created_by, created_at, job_key
            ) VALUES ('COLUMN_LEVEL', ?, ?, ?, ?, 'NONE', 'MANUAL_INPUT', 1.0, true, ?, CURRENT_TIMESTAMP, ?)
        """.trimIndent()

        val keyHolder = GeneratedKeyHolder()
        jdbcTemplate.update({ connection ->
            val ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)
            ps.setLong(1, sourceTableId)
            ps.setLong(2, targetTableId)
            ps.setLong(3, sourceColumnId)
            ps.setLong(4, targetColumnId)
            ps.setString(5, createdBy)
            ps.setString(6, ManualLineageService.MANUAL_INPUT_JOB_KEY)
            ps
        }, keyHolder)

        return keyHolder.key?.toLong() ?: throw RuntimeException("创建列级血缘关系失败：无法获取生成的ID")
    }

    /**
     * 获取或创建列（带详细信息）(Get or create column with details)
     */
    fun getOrCreateColumnWithDetails(
        tableId: Long,
        columnName: String,
        dataType: String,
        comment: String? = null,
        isPrimaryKey: Boolean = false,
        isNullable: Boolean = true,
        defaultValue: String? = null,
        columnOrder: Int? = null
    ): Long {
        // 查询是否已存在列
        val existingId = jdbcTemplate.queryForList(
            """
            SELECT id FROM lineage_columns 
            WHERE table_id = ? AND column_name = ?
            """.trimIndent(),
            Long::class.java,
            tableId, columnName
        ).singleOrNull() ?: run {
            // 不存在则创建
            val keyHolder = GeneratedKeyHolder()
            jdbcTemplate.update({ connection ->
                val ps = connection.prepareStatement(
                    """
                    INSERT INTO lineage_columns (
                        table_id, column_name, data_type, column_comment,
                        is_primary_key, is_nullable, default_value, column_order
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """.trimIndent(),
                    Statement.RETURN_GENERATED_KEYS
                )
                ps.setLong(1, tableId)
                ps.setString(2, columnName)
                ps.setString(3, dataType)
                ps.setString(4, comment)
                ps.setBoolean(5, isPrimaryKey)
                ps.setBoolean(6, isNullable)
                ps.setString(7, defaultValue)
                if (columnOrder != null) {
                    ps.setInt(8, columnOrder)
                } else {
                    ps.setNull(8, java.sql.Types.INTEGER)
                }
                ps
            }, keyHolder)
            keyHolder.key!!.toLong()
        }
        return existingId
    }

    /**
     * 查找表级血缘关系 (Find table relationship by ID)
     */
    fun findTableRelationshipById(tableRelationshipId: Long): SimpleTableRelationshipInfo? {
        val sql = """
            SELECT id, source_table_id, target_table_id, source_system, created_by, created_at
            FROM lineage_relationships 
            WHERE id = ? AND relationship_type = 'TABLE_LEVEL' AND is_active = true
        """.trimIndent()

        return jdbcTemplate.query(sql, { rs, _ ->
            SimpleTableRelationshipInfo(
                relationshipId = rs.getLong("id"),
                sourceTableId = rs.getLong("source_table_id"),
                targetTableId = rs.getLong("target_table_id"),
                sourceSystem = rs.getString("source_system"),
                createdBy = rs.getString("created_by"),
                createdAt = rs.getTimestamp("created_at")?.toLocalDateTime()
            )
        }, tableRelationshipId).singleOrNull()
    }

    /**
     * 删除列级血缘关系（通过表关系）(Delete column relationships by table relationship)
     */
    @Transactional
    fun deleteColumnRelationshipsByTableRelationship(
        sourceTableId: Long,
        targetTableId: Long
    ): Int {
        val sql = """
            UPDATE lineage_relationships 
            SET is_active = false, updated_at = CURRENT_TIMESTAMP
            WHERE relationship_type = 'COLUMN_LEVEL'
              AND source_table_id = ? AND target_table_id = ?
              AND source_system = 'MANUAL_INPUT' AND is_active = true
        """.trimIndent()

        return jdbcTemplate.update(sql, sourceTableId, targetTableId)
    }

    /**
     * 删除表级血缘关系 (Delete table relationship by ID)
     */
    @Transactional
    fun deleteTableRelationshipById(tableRelationshipId: Long): Int {
        val sql = """
            UPDATE lineage_relationships 
            SET is_active = false, updated_at = CURRENT_TIMESTAMP
            WHERE id = ? AND relationship_type = 'TABLE_LEVEL' 
              AND source_system = 'MANUAL_INPUT' AND is_active = true
        """.trimIndent()

        return jdbcTemplate.update(sql, tableRelationshipId)
    }

    /**
     * 获取简化的血缘关系详情 (Get simplified lineage details)
     */
    fun getSimplifiedLineageDetails(tableRelationshipId: Long): LineageRelationshipDetails? {
        // 1. 获取表级关系信息
        val tableRelationship = findTableRelationshipById(tableRelationshipId) ?: return null

        // 2. 获取源表和目标表信息
        val tableInfoSql = """
            SELECT 
                st.table_name as source_table_name, st.schema_name as source_schema_name,
                sds.datasource_name as source_datasource_name,
                tt.table_name as target_table_name, tt.schema_name as target_schema_name,
                tds.datasource_name as target_datasource_name,
                lr.created_by, lr.created_at, lr.last_updated_by, lr.updated_at
            FROM lineage_relationships lr
            JOIN lineage_tables st ON lr.source_table_id = st.id
            JOIN lineage_tables tt ON lr.target_table_id = tt.id
            JOIN lineage_datasources sds ON st.datasource_id = sds.id
            JOIN lineage_datasources tds ON tt.datasource_id = tds.id
            WHERE lr.id = ?
        """.trimIndent()

        val tableInfo = jdbcTemplate.query(tableInfoSql, { rs, _ ->
            mapOf(
                "sourceTableName" to rs.getString("source_table_name"),
                "sourceSchemaName" to rs.getString("source_schema_name"),
                "sourceDatasourceName" to rs.getString("source_datasource_name"),
                "targetTableName" to rs.getString("target_table_name"),
                "targetSchemaName" to rs.getString("target_schema_name"),
                "targetDatasourceName" to rs.getString("target_datasource_name"),
                "createdBy" to rs.getString("created_by"),
                "createdAt" to rs.getTimestamp("created_at")?.toLocalDateTime()?.toString(),
                "lastUpdatedBy" to rs.getString("last_updated_by"),
                "lastUpdatedAt" to rs.getTimestamp("updated_at")?.toLocalDateTime()?.toString()
            )
        }, tableRelationshipId).singleOrNull() ?: return null

        // 3. 获取列级关系信息
        val columnsSql = """
            SELECT 
                lr.id as column_relationship_id,
                sc.column_name as source_column_name, sc.data_type as source_data_type,
                tc.column_name as target_column_name, tc.data_type as target_data_type
            FROM lineage_relationships lr
            JOIN lineage_columns sc ON lr.source_column_id = sc.id
            JOIN lineage_columns tc ON lr.target_column_id = tc.id
            WHERE lr.relationship_type = 'COLUMN_LEVEL'
              AND lr.source_table_id = ? AND lr.target_table_id = ?
              AND lr.source_system = 'MANUAL_INPUT' AND lr.is_active = true
            ORDER BY sc.column_name, tc.column_name
        """.trimIndent()

        val columns = jdbcTemplate.query(columnsSql, { rs, _ ->
            ColumnMappingDetails(
                columnRelationshipId = rs.getLong("column_relationship_id"),
                sourceColumnName = rs.getString("source_column_name"),
                sourceDataType = rs.getString("source_data_type"),
                targetColumnName = rs.getString("target_column_name"),
                targetDataType = rs.getString("target_data_type")
            )
        }, tableRelationship.sourceTableId, tableRelationship.targetTableId)

        return LineageRelationshipDetails(
            tableRelationshipId = tableRelationshipId,
            sourceDatasourceName = tableInfo["sourceDatasourceName"] as String,
            sourceTableName = tableInfo["sourceTableName"] as String,
            sourceSchemaName = tableInfo["sourceSchemaName"] as? String,
            targetDatasourceName = tableInfo["targetDatasourceName"] as String,
            targetTableName = tableInfo["targetTableName"] as String,
            targetSchemaName = tableInfo["targetSchemaName"] as? String,
            columns = columns,
            createdBy = tableInfo["createdBy"] as? String,
            createdAt = tableInfo["createdAt"] as? String,
            lastUpdatedBy = tableInfo["lastUpdatedBy"] as? String,
            lastUpdatedAt = tableInfo["lastUpdatedAt"] as? String
        )
    }

    /**
     * 手动血缘管理：更新表元数据 (Manual Lineage Management: Update table metadata)
     */
    fun updateTableMetadataForManualLineage(
        tableId: Long,
        tableType: String?,
        chineseName: String?,
        description: String?
    ): Int {
        val updates = mutableListOf<String>()
        val params = mutableListOf<Any>()

        if (tableType != null) {
            updates.add("table_type = ?")
            params.add(tableType)
        }
        if (chineseName != null) {
            updates.add("chinese_name = ?")
            params.add(chineseName)
        }
        if (description != null) {
            updates.add("description = ?")
            params.add(description)
        }

        if (updates.isEmpty()) {
            return 0
        }

        updates.add("updated_at = CURRENT_TIMESTAMP")
        params.add(tableId)

        val sql = """
            UPDATE lineage_tables 
            SET ${updates.joinToString(", ")}
            WHERE id = ?
        """.trimIndent()

        return jdbcTemplate.update(sql, *params.toTypedArray())
    }

    /**
     * 手动血缘管理：确保列存在 (Manual Lineage Management: Ensure columns exist)
     */
    fun ensureColumnsExistForManualLineage(
        tableId: Long,
        columnMappings: List<ColumnMappingWithLineage>,
        isSource: Boolean,
        databaseInfo: DatabaseInfo,
        tableInfo: TableInfo
    ): List<Long> {
        val columnIds = mutableListOf<Long>()

        for (mapping in columnMappings) {
            val columnName = if (isSource) mapping.sourceColumnName else mapping.targetColumnName
            val dataType = if (isSource) mapping.sourceDataType else mapping.targetDataType
            val comment = if (isSource) mapping.sourceColumnComment else mapping.targetColumnComment
            val isPrimaryKey = if (isSource) mapping.sourceIsPrimaryKey else mapping.targetIsPrimaryKey
            val isNullable = if (isSource) mapping.sourceIsNullable else mapping.targetIsNullable
            val defaultValue = if (isSource) mapping.sourceDefaultValue else mapping.targetDefaultValue
            val columnOrder = if (isSource) mapping.sourceColumnOrder else mapping.targetColumnOrder

            // 创建 ColumnInfo 对象
            val columnInfo = ColumnInfo(
                columnName = columnName,
                dataType = dataType,
                comment = comment,
                table = tableInfo
            )

            // 获取或创建列
            val columnId = getOrCreateColumn(columnInfo)

            // 更新列的额外元数据
            updateColumnMetadataForManualLineage(
                columnId = columnId,
                isPrimaryKey = isPrimaryKey,
                isNullable = isNullable,
                defaultValue = defaultValue,
                columnOrder = columnOrder
            )

            columnIds.add(columnId)
        }

        return columnIds
    }

    /**
     * 手动血缘管理：更新列元数据 (Manual Lineage Management: Update column metadata)
     */
    private fun updateColumnMetadataForManualLineage(
        columnId: Long,
        isPrimaryKey: Boolean?,
        isNullable: Boolean?,
        defaultValue: String?,
        columnOrder: Int?
    ): Int {
        val updates = mutableListOf<String>()
        val params = mutableListOf<Any>()

        if (isPrimaryKey != null) {
            updates.add("is_primary_key = ?")
            params.add(isPrimaryKey)
        }
        if (isNullable != null) {
            updates.add("is_nullable = ?")
            params.add(isNullable)
        }
        if (defaultValue != null) {
            updates.add("default_value = ?")
            params.add(defaultValue)
        }
        if (columnOrder != null) {
            updates.add("column_order = ?")
            params.add(columnOrder)
        }

        if (updates.isEmpty()) {
            return 0
        }

        updates.add("updated_at = CURRENT_TIMESTAMP")
        params.add(columnId)

        val sql = """
            UPDATE lineage_columns 
            SET ${updates.joinToString(", ")}
            WHERE id = ?
        """.trimIndent()

        return jdbcTemplate.update(sql, *params.toTypedArray())
    }

    /**
     * 查询所有具有cron表达式的活跃系统 (Find all active systems with cron expression)
     *
     * @return 系统调度信息列表
     */
    fun findSystemsWithCronExpression(): List<com.datayes.scheduler.SystemScheduleInfo> {
        val sql = """
            SELECT id, system_name, system_code, status, cron_expression, description
            FROM lineage_systems 
            WHERE status = 'ACTIVE' 
              AND cron_expression IS NOT NULL 
              AND cron_expression != ''
            ORDER BY id
        """.trimIndent()

        return jdbcTemplate.query(sql) { rs, _ ->
            com.datayes.scheduler.SystemScheduleInfo(
                id = rs.getLong("id"),
                systemName = rs.getString("system_name"),
                systemCode = rs.getString("system_code"),
                status = rs.getString("status"),
                cronExpression = rs.getString("cron_expression"),
                description = rs.getString("description")
            )
        }
    }

    /**
     * 根据系统ID查询系统调度信息 (Find system schedule info by system ID)
     *
     * @param systemId 系统ID
     * @return 系统调度信息，如果不存在则返回null
     */
    fun findSystemScheduleInfo(systemId: Long): com.datayes.scheduler.SystemScheduleInfo? {
        val sql = """
            SELECT id, system_name, system_code, status, cron_expression, description
            FROM lineage_systems 
            WHERE id = ?
        """.trimIndent()

        return try {
            jdbcTemplate.queryForObject(sql, { rs, _ ->
                com.datayes.scheduler.SystemScheduleInfo(
                    id = rs.getLong("id"),
                    systemName = rs.getString("system_name"),
                    systemCode = rs.getString("system_code"),
                    status = rs.getString("status"),
                    cronExpression = rs.getString("cron_expression"),
                    description = rs.getString("description")
                )
            }, systemId)
        } catch (e: Exception) {
            null
        }
    }
}

// 数据传输对象

data class TableLineageView(
    val relationshipId: Long,
    val sourceTableId: Long,
    val targetTableId: Long,
    val sourceTable: String,
    val sourceSchema: String?,
    val sourceChineseName: String?,
    val sourceDatasource: String,
    val sourceSystem: String?,
    val targetTable: String,
    val targetSchema: String?,
    val targetDatasource: String,
    val lineageType: String,
    val lineageSource: String,
    val confidenceScore: java.math.BigDecimal?,
    val createdAt: LocalDateTime?,
    val level: Int = 1
)

data class ColumnLineageView(
    val relationshipId: Long,
    val sourceColumn: String,
    val sourceDataType: String,
    val sourceTable: String,
    val sourceSchema: String?,
    val sourceTableId: Long?,
    val sourceDatasource: String?,
    val sourceSystem: String?,
    val sourceChineseName: String?,
    val targetColumn: String,
    val targetDataType: String,
    val targetTable: String,
    val targetSchema: String?,
    val targetTableId: Long?,
    val targetDatasource: String?,
    val transformationType: String?,
    val transformationDescription: String?,
    val transformationExpression: String?,
    val confidenceScore: java.math.BigDecimal?,
    val lineageSource: String,
    val createdAt: LocalDateTime?,
    val lastUpdated: LocalDateTime?
)

data class SystemStatistics(
    val systemName: String,
    val systemCode: String,
    val datasourceCount: Int,
    val tableCount: Int,
    val columnCount: Int,
    val tableLineageCount: Int,
    val columnLineageCount: Int,
    val lastUpdateTime: LocalDateTime?
)

/**
 * 系统信息数据类 (System Info Data Class)
 */
data class SystemInfo(
    val id: Long,
    val systemName: String,
    val systemCode: String,
    val description: String?,
    val contactPerson: String?,
    val status: String,
    val systemStatus: String, // Add systemStatus field for API response
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
    val cronExpression: String?,
    val scheduleTime: LocalDateTime?
)
