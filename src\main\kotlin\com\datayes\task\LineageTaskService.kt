package com.datayes.task

import com.datayes.dataexchange.*
import com.datayes.hdfs.HdfsShellScriptJob
import com.datayes.hdfs.HdfsShellScriptService
import com.datayes.lineage.ProcessingResult
import com.datayes.lineage.JobProcessingHistory
import com.datayes.lineage.JobProcessingHistoryRepository
import org.slf4j.LoggerFactory
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.multipart.MultipartFile
import java.time.LocalDateTime
import java.util.*

/**
 * 血缘任务服务 (Lineage Task Service)
 *
 * 实现血缘任务的核心业务逻辑，包括批量处理、重跑和查询功能
 * 遵循函数式核心，命令式外壳的架构原则
 */
@Service
@Transactional
class LineageTaskService(
    private val lineageTaskRepository: LineageTaskRepository,
    private val lineageTaskCustomRepository: LineageTaskCustomRepository,
    private val enhancedDataExchangeJobService: DataExchangeJobService,
    private val dataExchangeJobRepository: DataExchangeJobRepository,
    private val jobProcessingHistoryRepository: JobProcessingHistoryRepository,
    private val manualLineageImportService: ManualLineageImportService,
    private val executionLogService: ExecutionLogService,
    private val hdfsShellScriptService: HdfsShellScriptService
) {

    private val logger = LoggerFactory.getLogger(LineageTaskService::class.java)

    /**
     * 批量处理所有活跃作业的血缘 (Process all active jobs lineage)
     */
    fun processAllActiveJobs(request: BatchProcessRequest): BatchProcessResult {
        val startTime = System.currentTimeMillis()
        val batchId = request.batchId ?: generateBatchId()

        logger.info("01173f | 开始批量处理血缘任务，批次ID: $batchId")

        try {
            // 1. 获取所有活跃的数据交换作业
            val activeJobs = dataExchangeJobRepository.listActiveDataExchangeJobs()
            logger.info("fcfdaa | 获取到 ${activeJobs.size} 个活跃的数据交换作业")

            // 创建活跃作业的键集合，用于后续检查
            val activeJobKeys = activeJobs.map { "${it.readerJobId}_${it.writeJobId}" }.toSet()

            // 2. 处理每个作业的血缘
            val processedTasks = mutableListOf<TaskProcessResult>()
            var successCount = 0
            var failedCount = 0
            var unchangedCount = 0
            var updatedCount = 0
            var deactivatedCount = 0

            for (job in activeJobs) {
                try {
                    val taskResult = processJobLineageWithTask(job, request.executedBy, batchId)
                    processedTasks.add(taskResult)

                    when (taskResult.processingResult) {
                        ProcessingResult.UPDATED -> {
                            successCount++
                            updatedCount++
                        }

                        ProcessingResult.NO_CHANGE -> {
                            successCount++
                            unchangedCount++
                        }

                        ProcessingResult.FAILED -> failedCount++
                    }

                } catch (e: Exception) {
                    logger.error("ebf263 | 处理作业血缘时发生异常: ${job.readerJobId}_${job.writeJobId}", e)
                    failedCount++
                    processedTasks.add(createFailedTaskResult(job, e))
                }
            }

            // 3. 处理不再活跃的作业对应的血缘任务
            val existingTasks = lineageTaskRepository.findAllByTaskTypeAndIsEnabled(
                TaskType.DATA_EXCHANGE_PLATFORM,
                true
            )

            for (task in existingTasks) {
                // 如果任务的jobKey不在活跃作业列表中，则停用该任务
                if (task.jobKey != null && !activeJobKeys.contains(task.jobKey)) {
                    try {
                        logger.info("a8f23c | 停用不再活跃的血缘任务: id=${task.id}, jobKey=${task.jobKey}")

                        // 更新任务为停用状态
                        lineageTaskCustomRepository.updateTaskStatus(
                            taskId = task.id,
                            isEnabled = false,
                            updatedBy = request.executedBy ?: "system",  // 这个参数会被忽略，但保留以便将来可能的表结构变更
                            reason = "对应的数据交换作业不再活跃"
                        )

                        deactivatedCount++

                        // 添加到处理结果中
                        processedTasks.add(
                            TaskProcessResult(
                                taskId = task.id,
                                jobKey = task.jobKey,
                                taskName = task.taskName,
                                status = TaskStatus.DISABLED,
                                processingTimeMs = 0,
                                hasChanges = true,
                                processingResult = ProcessingResult.UPDATED,
                                errorMessage = null
                            )
                        )

                    } catch (e: Exception) {
                        logger.error("c4e1d9 | 停用不再活跃的血缘任务时发生异常: id=${task.id}", e)
                    }
                }
            }

            val totalProcessingTime = System.currentTimeMillis() - startTime

            logger.info(
                "5d81d671 | 批量处理完成: 总计=${activeJobs.size}, 成功=$successCount, 失败=$failedCount, " +
                        "更新=$updatedCount, 无变更=$unchangedCount, 停用=$deactivatedCount, 耗时=${totalProcessingTime}ms"
            )

            return BatchProcessResult(
                batchId = batchId,
                totalJobs = activeJobs.size,
                processedTasks = processedTasks,
                summary = ProcessingSummary(
                    successful = successCount,
                    failed = failedCount,
                    unchanged = unchangedCount,
                    updated = updatedCount,
                    deactivated = deactivatedCount
                ),
                processingTimeMs = totalProcessingTime
            )

        } catch (e: Exception) {
            logger.error("7d69158f | 批量处理血缘任务时发生致命错误", e)
            throw LineageTaskProcessingException("批量处理失败", e)
        }
    }

    /**
     * 处理指定系统类型的血缘收集 (Process system-specific lineage collection)
     */
    fun processSystemLineageCollection(
        systemType: TaskType,
        request: SystemLineageCollectionRequest
    ): SystemLineageCollectionResponse {
        val startTime = System.currentTimeMillis()
        val batchId = request.batchId ?: generateBatchId()

        logger.info("开始处理系统血缘收集: systemType=$systemType, batchId=$batchId")

        return try {
            // 1. 获取系统的活跃任务
            val activeJobs = getActiveJobsBySystemType(systemType)
            logger.info("获取到 ${activeJobs.size} 个活跃任务: systemType=$systemType")

            // 2. 批量处理系统任务
            val taskResults = processBatchSystemTasks(activeJobs, request)

            // 3. 生成执行摘要
            val executionSummary = generateExecutionSummary(taskResults, startTime)

            // 4. 分离成功和失败的任务
            val (successfulTasks, failedTasks) = separateTaskResults(taskResults)

            logger.info("系统血缘收集完成: systemType=$systemType, 成功=${successfulTasks.size}, 失败=${failedTasks.size}")

            SystemLineageCollectionResponse(
                systemType = systemType,
                batchId = batchId,
                executionSummary = executionSummary,
                taskResults = successfulTasks,
                failedTasks = failedTasks
            )

        } catch (e: Exception) {
            logger.error("系统血缘收集时发生致命错误: systemType=$systemType", e)
            throw LineageTaskProcessingException("系统血缘收集失败: ${e.message}", e)
        }
    }

    /**
     * 获取系统的活跃任务 (Get active jobs by system type)
     */
    private fun getActiveJobsBySystemType(systemType: TaskType): List<DataExchangeJob> {
        return when (systemType) {
            TaskType.DATA_EXCHANGE_PLATFORM -> dataExchangeJobRepository.listActiveDataExchangeJobs()
            else -> throw UnsupportedOperationException("不支持的系统类型: $systemType")
        }
    }

    /**
     * 批量处理系统任务 (Process batch system tasks)
     */
    private fun processBatchSystemTasks(
        jobs: List<DataExchangeJob>,
        request: SystemLineageCollectionRequest
    ): List<TaskProcessResult> {
        val results = mutableListOf<TaskProcessResult>()

        for (job in jobs) {
            try {
                val result = processJobLineageWithTask(job, request.executedBy, request.batchId ?: generateBatchId())
                results.add(result)
            } catch (e: Exception) {
                logger.error("处理作业血缘时发生异常: ${job.readerJobId}_${job.writeJobId}", e)
                results.add(createFailedTaskResult(job, e))
            }
        }

        return results
    }

    /**
     * 生成执行摘要 (Generate execution summary)
     */
    private fun generateExecutionSummary(taskResults: List<TaskProcessResult>, startTime: Long): ExecutionSummary {
        val totalProcessingTime = System.currentTimeMillis() - startTime

        return ExecutionSummary(
            totalJobs = taskResults.size,
            processedTasks = taskResults.size,
            successful = taskResults.count { it.status == TaskStatus.SUCCESS },
            failed = taskResults.count { it.status == TaskStatus.FAILED },
            unchanged = taskResults.count { !it.hasChanges && it.status == TaskStatus.SUCCESS },
            updated = taskResults.count { it.hasChanges && it.status == TaskStatus.SUCCESS },
            processingTimeMs = totalProcessingTime
        )
    }

    /**
     * 分离任务结果 (Separate task results)
     */
    private fun separateTaskResults(taskResults: List<TaskProcessResult>): Pair<List<TaskProcessResult>, List<FailedTaskInfo>> {
        val successfulTasks = taskResults.filter { it.status == TaskStatus.SUCCESS }
        val failedTasks = taskResults.filter { it.status == TaskStatus.FAILED }
            .map { FailedTaskInfo(it.jobKey, it.errorMessage ?: "未知错误", "PROCESSING_ERROR") }

        return Pair(successfulTasks, failedTasks)
    }

    /**
     * 处理单个作业的血缘并创建/更新任务 (Process single job lineage with task management)
     */
    private fun processJobLineageWithTask(
        job: DataExchangeJob,
        executedBy: String?,
        batchId: String
    ): TaskProcessResult {
        val jobKey = "${job.readerJobId}_${job.writeJobId}"
        val startTime = System.currentTimeMillis()
        val executionId = UUID.randomUUID().toString()

        // 1. 查找或创建血缘任务
        val task = findOrCreateLineageTask(job, jobKey, executedBy, batchId)

        // 2. 更新任务状态为执行中
        updateTaskStatus(task.id, TaskStatus.RUNNING, LocalDateTime.now())

        // 3. 记录任务执行开始日志
        executionLogService.logTaskExecutionStart(task.id, executionId, job)

        try {
            // 4. 执行血缘处理
            val lineageResult = enhancedDataExchangeJobService.processJobLineageWithChangeDetection(job, task.id)
            val processingTime = System.currentTimeMillis() - startTime

            // 5. 更新任务执行结果
            updateTaskExecutionResult(
                taskId = task.id,
                lineageResult = lineageResult,
                processingTime = processingTime,
                executionId = executionId
            )

            // 6. 记录任务执行完成日志
            val additionalInfo = mapOf(
                "has_changes" to lineageResult.hasChanges,
                "processing_result" to lineageResult.processingResult.name,
                "lineage_count" to (lineageResult.lineageResult.lineage?.columnLineages?.size ?: 0)
            )
            executionLogService.logTaskExecutionCompletion(
                executionId = executionId,
                taskStatus = TaskStatus.SUCCESS,
                processingTimeMs = processingTime,
                additionalInfo = additionalInfo
            )

            return TaskProcessResult(
                taskId = task.id,
                jobKey = jobKey,
                taskName = task.taskName,
                status = TaskStatus.SUCCESS,
                processingTimeMs = processingTime,
                hasChanges = lineageResult.hasChanges,
                processingResult = lineageResult.processingResult
            )

        } catch (e: Exception) {
            // 7. 处理失败，更新任务状态
            val processingTime = System.currentTimeMillis() - startTime
            updateTaskStatus(
                taskId = task.id,
                status = TaskStatus.FAILED,
                completedAt = LocalDateTime.now(),
                errorMessage = e.message,
                processingTimeMs = processingTime
            )

            // 8. 记录任务执行失败日志
            executionLogService.logTaskExecutionCompletion(
                executionId = executionId,
                taskStatus = TaskStatus.FAILED,
                processingTimeMs = processingTime,
                errorMessage = e.message
            )

            return TaskProcessResult(
                taskId = task.id,
                jobKey = jobKey,
                taskName = task.taskName,
                status = TaskStatus.FAILED,
                processingTimeMs = processingTime,
                hasChanges = false,
                processingResult = ProcessingResult.FAILED,
                errorMessage = e.message
            )
        }
    }

    /**
     * 查找或创建血缘任务 (Find or create lineage task)
     */
    private fun findOrCreateLineageTask(
        job: DataExchangeJob,
        jobKey: String,
        executedBy: String?,
        batchId: String
    ): LineageTask {
        return lineageTaskRepository.findByJobKey(jobKey) ?: run {
            val taskName = "数据同步任务-${job.readerJobName}到${job.writeJobName}"
            val request = CreateLineageTaskRequest(
                taskName = taskName,
                taskType = TaskType.DATA_EXCHANGE_PLATFORM,
                sourceType = SourceType.DATA_EXCHANGE_JOB,
                sourceIdentifier = jobKey,
                sourceContent = job.readerSql,
                jobKey = jobKey,
                createdBy = executedBy,
                batchId = batchId
            )
            lineageTaskRepository.save(request.toLineageTask())
        }
    }

    /**
     * 重跑血缘任务 (Rerun lineage task)
     */
    fun rerunTask(taskId: Long, request: RerunTaskRequest): TaskExecutionResult {
        logger.info("61812a | 开始重跑血缘任务: $taskId")

        // 1. 获取任务信息
        val task = lineageTaskRepository.findById(taskId)
            .orElseThrow { LineageTaskNotFoundException("任务不存在: $taskId") }

        // 2. 检查任务状态
        if (task.taskStatus == TaskStatus.RUNNING) {
            throw IllegalStateException("任务正在执行中，无法重跑: $taskId")
        }

        if (task.taskStatus == TaskStatus.DISABLED) {
            throw IllegalStateException("任务已禁用，无法重跑: $taskId")
        }

        val jobKey = task.jobKey ?: throw IllegalStateException("任务缺少作业键: $taskId")

        // 3. 根据任务类型（task type）选择相应的处理逻辑
        return when (task.taskType) {
            TaskType.DATA_EXCHANGE_PLATFORM -> rerunDataExchangeTask(task, request, jobKey)
            TaskType.BASH_SCRIPT -> rerunHdfsScriptTask(task, request, jobKey)
            else -> throw IllegalStateException("不支持的任务类型重跑: ${task.taskType}")
        }
    }

    /**
     * 重跑数据交换任务 (Rerun data exchange task)
     */
    private fun rerunDataExchangeTask(
        task: LineageTask,
        request: RerunTaskRequest,
        jobKey: String
    ): TaskExecutionResult {
        // 解析数据交换作业键 (格式: readerJobId_writeJobId)
        val jobKeyParts = jobKey.split("_")
        if (jobKeyParts.size < 2) {
            throw IllegalStateException("数据交换作业键格式错误: $jobKey")
        }

        val job = dataExchangeJobRepository.findDataExchangeJobByIds(jobKeyParts[0], jobKeyParts[1])
            ?: throw IllegalStateException("找不到对应的数据交换作业: $jobKey")

        val executionId = UUID.randomUUID().toString()
        updateTaskStatus(task.id, TaskStatus.RUNNING, LocalDateTime.now())

        // 记录任务重跑开始日志
        executionLogService.logTaskExecutionStart(task.id, executionId, job)

        return try {
            val startTime = System.currentTimeMillis()
            val lineageResult = enhancedDataExchangeJobService.processJobLineageWithChangeDetection(job, task.id)
            val processingTime = System.currentTimeMillis() - startTime

            updateTaskExecutionResult(
                taskId = task.id,
                lineageResult = lineageResult,
                processingTime = processingTime,
                executionId = executionId
            )

            // 记录任务重跑成功日志
            val additionalInfo = mapOf(
                "rerun_reason" to (request.reason ?: "手动重跑"),
                "executed_by" to request.executedBy,
                "has_changes" to lineageResult.hasChanges,
                "processing_result" to lineageResult.processingResult.name,
                "task_type" to "DATA_EXCHANGE"
            )
            executionLogService.logTaskExecutionCompletion(
                executionId = executionId,
                taskStatus = TaskStatus.SUCCESS,
                processingTimeMs = processingTime,
                additionalInfo = additionalInfo
            )

            logger.info("09e066 | 数据交换任务重跑成功: ${task.id}, 执行ID: $executionId")

            TaskExecutionResult(
                taskId = task.id,
                executionId = executionId,
                status = TaskStatus.SUCCESS,
                message = "数据交换任务重跑成功"
            )

        } catch (e: Exception) {
            handleTaskExecutionFailure(task, executionId, e, "数据交换任务重跑失败")
        }
    }

    /**
     * 重跑HDFS脚本任务 (Rerun HDFS script task)
     */
    private fun rerunHdfsScriptTask(
        task: LineageTask,
        request: RerunTaskRequest,
        jobKey: String
    ): TaskExecutionResult {
        // 构建HDFS Shell脚本作业对象
        val hdfsJob = reconstructHdfsShellScriptJob(task, jobKey)

        val executionId = UUID.randomUUID().toString()
        updateTaskStatus(task.id, TaskStatus.RUNNING, LocalDateTime.now())

        // 记录任务重跑开始日志
        executionLogService.logTaskExecutionStart(task.id, executionId, hdfsJob)

        return try {
            val startTime = System.currentTimeMillis()
            val lineageResult = hdfsShellScriptService.processJobLineageWithChangeDetection(hdfsJob, task.id)
            val processingTime = System.currentTimeMillis() - startTime

            // 转换结果格式以兼容updateTaskExecutionResult方法
            val adaptedResult = adaptHdfsResultToDataExchangeFormat(lineageResult)

            updateTaskExecutionResult(
                taskId = task.id,
                lineageResult = adaptedResult,
                processingTime = processingTime,
                executionId = executionId
            )

            // 记录任务重跑成功日志
            val additionalInfo = mapOf(
                "rerun_reason" to (request.reason ?: "手动重跑"),
                "executed_by" to request.executedBy,
                "has_changes" to lineageResult.hasChanges,
                "processing_result" to lineageResult.processingResult.name,
                "task_type" to "HDFS_SCRIPT",
                "zip_file_path" to hdfsJob.zipFilePath,
                "script_name" to hdfsJob.scriptName
            )
            executionLogService.logTaskExecutionCompletion(
                executionId = executionId,
                taskStatus = TaskStatus.SUCCESS,
                processingTimeMs = processingTime,
                additionalInfo = additionalInfo
            )

            logger.info("a3f7b9 | HDFS脚本任务重跑成功: ${task.id}, 执行ID: $executionId")

            TaskExecutionResult(
                taskId = task.id,
                executionId = executionId,
                status = TaskStatus.SUCCESS,
                message = "HDFS脚本任务重跑成功"
            )

        } catch (e: Exception) {
            handleTaskExecutionFailure(task, executionId, e, "HDFS脚本任务重跑失败")
        }
    }

    /**
     * 从LineageTask重构HdfsShellScriptJob对象 (Reconstruct HdfsShellScriptJob from LineageTask)
     */
    private fun reconstructHdfsShellScriptJob(task: LineageTask, jobKey: String): HdfsShellScriptJob {
        // 从任务信息中提取脚本内容和源标识符
        val scriptContent = task.sourceContent ?: throw IllegalStateException("HDFS脚本任务缺少脚本内容: ${task.id}")
        val sourceIdentifier = task.sourceIdentifier ?: jobKey

        // 从jobKey或其他信息中尝试解析文件路径和脚本名
        // jobKey格式通常为: hdfs_<hash> (来自HdfsShellScriptJob.generateJobId)
        val scriptName = extractScriptNameFromTask(task)
        val zipFilePath = extractZipFilePathFromTask(task)

        return HdfsShellScriptJob(
            jobId = jobKey,
            jobName = task.taskName.removePrefix("HDFS脚本任务-"),
            zipFilePath = zipFilePath,
            scriptName = scriptName,
            scriptContent = scriptContent,
            scriptSizeBytes = scriptContent.toByteArray(Charsets.UTF_8).size,
            extractedAt = task.createdAt,
            lastModified = task.updatedAt
        )
    }

    /**
     * 从任务信息中提取脚本名称 (Extract script name from task)
     */
    private fun extractScriptNameFromTask(task: LineageTask): String {
        // 优先从源标识符中提取
        task.sourceIdentifier?.let { identifier ->
            if (identifier.endsWith(".sh")) {
                return identifier.substringAfterLast("/")
            }
        }

        // 从任务名称中提取
        val baseName = task.taskName.removePrefix("HDFS脚本任务-")
        return if (baseName.endsWith(".sh")) baseName else "${baseName}.sh"
    }

    /**
     * 从任务信息中提取ZIP文件路径 (Extract ZIP file path from task)
     */
    private fun extractZipFilePathFromTask(task: LineageTask): String {
        // 从源标识符中提取ZIP文件路径（如果可用）
        task.sourceIdentifier?.let { identifier ->
            if (identifier.contains(".zip")) {
                val zipPath = identifier.substringBeforeLast("/")
                if (zipPath.contains(".zip")) {
                    return zipPath.substringBeforeLast("/") + "/" + zipPath.substringAfterLast("/").substringBefore("!")
                }
            }
        }

        // 默认返回通用HDFS路径
        return "/share_ftp/unknown_${task.id}.zip"
    }

    /**
     * 适配HDFS结果格式为血缘处理结果格式 (Adapt HDFS result to LineageProcessResult format)
     */
    private fun adaptHdfsResultToDataExchangeFormat(hdfsResult: com.datayes.hdfs.HdfsLineageProcessResult): LineageProcessResult {
        // 创建一个虚拟的DataExchangeJob对象用于兼容性
        val virtualJob = DataExchangeJob(
            readerJobId = hdfsResult.job.jobId,
            readerJobName = hdfsResult.job.jobName,
            dbReader = hdfsResult.job.zipFilePath,  // 使用ZIP文件路径作为源
            readerTableName = "hdfs_source_${hdfsResult.job.jobId}",  // 虚拟源表名
            readerSql = hdfsResult.job.scriptContent,  // 使用脚本内容
            writeJobId = hdfsResult.job.jobId,
            writeJobName = hdfsResult.job.jobName,
            dbWriter = "hdfs://processed/${hdfsResult.job.jobId}",  // 虚拟目标路径
            columns = emptyList(),  // HDFS任务通常没有列映射
            writerTableName = "hdfs_target_${hdfsResult.job.jobId}"  // 虚拟目标表名
        )

        return LineageProcessResult(
            job = virtualJob,
            lineageResult = hdfsResult.lineageResult,
            processingTimeMs = hdfsResult.processingTimeMs,
            hasChanges = hdfsResult.hasChanges,
            processingResult = when (hdfsResult.processingResult) {
                com.datayes.hdfs.HdfsProcessingResult.UPDATED -> ProcessingResult.UPDATED
                com.datayes.hdfs.HdfsProcessingResult.NO_CHANGE -> ProcessingResult.NO_CHANGE
                com.datayes.hdfs.HdfsProcessingResult.FAILED -> ProcessingResult.FAILED
            }
        )
    }

    /**
     * 处理任务执行失败 (Handle task execution failure)
     */
    private fun handleTaskExecutionFailure(
        task: LineageTask,
        executionId: String,
        exception: Exception,
        baseMessage: String
    ): TaskExecutionResult {
        val processingTime = System.currentTimeMillis() - System.currentTimeMillis() // 这里应该传入startTime，但为了兼容性暂时这样处理
        updateTaskStatus(
            taskId = task.id,
            status = TaskStatus.FAILED,
            completedAt = LocalDateTime.now(),
            errorMessage = exception.message,
            processingTimeMs = processingTime
        )

        // 记录任务重跑失败日志
        executionLogService.logTaskExecutionCompletion(
            executionId = executionId,
            taskStatus = TaskStatus.FAILED,
            processingTimeMs = processingTime,
            errorMessage = exception.message
        )

        logger.error("73a73b | $baseMessage: ${task.id}", exception)

        return TaskExecutionResult(
            taskId = task.id,
            executionId = executionId,
            status = TaskStatus.FAILED,
            message = "$baseMessage: ${exception.message}"
        )
    }

    /**
     * 分页查询血缘任务 (Find tasks with pagination)
     */
    fun findTasks(criteria: LineageTaskCustomRepository.TaskQueryCriteria, pageable: Pageable): Page<LineageTask> {
        return lineageTaskCustomRepository.findTasksWithCriteria(criteria, pageable)
    }
    
    /**
     * 根据ID查找任务 (Find task by ID)
     * 
     * UC-07: 支持查看特定任务的详情
     * 
     * @param taskId 任务ID
     * @return 任务实体，如果不存在则返回null
     */
    fun findById(taskId: Long): LineageTask? {
        return lineageTaskRepository.findById(taskId).orElse(null)
    }
    
    /**
     * 获取任务的处理日志 (Get task processing logs)
     * 
     * UC-07: 查看LineageTask的JobProcessingHistory和失败详情
     * 
     * @param taskId 任务ID
     * @return 任务日志信息，包括处理历史和失败记录
     */
    fun getTaskLogs(taskId: Long): TaskLogsDto? {
        val task = findById(taskId) ?: return null
        
        // 获取该任务对应的作业键
        val jobKey = task.jobKey ?: return TaskLogsDto(
            task = task.toDto(),
            processingHistory = emptyList(),
            failedExecutions = listOf(
                FailedTaskExecutionDto(
                    executionId = task.lastExecutionId,
                    failedAt = task.executedAt?.toString(),
                    errorMessage = task.errorMessage ?: "任务缺少作业键",
                    processingTimeMs = task.processingTimeMs
                )
            )
        )
        
        // 查询处理历史
        val processingHistory = getAllProcessingHistoryForJob(jobKey)
        
        // 构建失败执行记录
        val failedExecutions = mutableListOf<FailedTaskExecutionDto>()
        
        // 添加处理历史中的失败记录
        processingHistory.filter { it.processingResult == ProcessingResult.FAILED }
            .forEach { history ->
                failedExecutions.add(
                    FailedTaskExecutionDto(
                        executionId = history.id.toString(),
                        failedAt = history.processedAt.toString(),
                        errorMessage = history.errorMessage,
                        processingTimeMs = history.processingDurationMs
                    )
                )
            }
        
        // 如果任务当前状态为失败，且不在处理历史中，添加任务本身的错误信息
        if (task.taskStatus == TaskStatus.FAILED && task.errorMessage != null) {
            val isAlreadyInHistory = failedExecutions.any { 
                it.errorMessage == task.errorMessage && it.failedAt == task.completedAt?.toString() 
            }
            
            if (!isAlreadyInHistory) {
                failedExecutions.add(
                    FailedTaskExecutionDto(
                        executionId = task.lastExecutionId,
                        failedAt = task.completedAt?.toString() ?: task.executedAt?.toString(),
                        errorMessage = task.errorMessage,
                        processingTimeMs = task.processingTimeMs
                    )
                )
            }
        }
        
        return TaskLogsDto(
            task = task.toDto(),
            processingHistory = processingHistory.map { it.toDto() },
            failedExecutions = failedExecutions
        )
    }
    
    /**
     * 获取作业的所有处理历史 (Get all processing history for a job)
     */
    private fun getAllProcessingHistoryForJob(jobKey: String): List<JobProcessingHistory> {
        return lineageTaskCustomRepository.findAllProcessingHistoryByJobKey(jobKey)
    }

    /**
     * 处理手动血缘导入 (Process manual lineage import)
     * 
     * UC-08: 处理上传的血缘文件，创建MANUAL_IMPORT任务
     * 
     * @param file 上传的血缘文件
     * @param taskName 任务名称（可选）
     * @param createdBy 创建人
     * @return 手动导入结果
     */
    fun processManualImport(
        file: MultipartFile,
        taskName: String?,
        createdBy: String
    ): ManualImportResult {
        val startTime = System.currentTimeMillis()
        val fileName = file.originalFilename ?: "unknown_file"
        val generatedTaskName = taskName ?: "手动导入-$fileName-${System.currentTimeMillis()}"
        
        logger.info("8d4f7b2e | 开始处理手动血缘导入: fileName=$fileName, taskName=$generatedTaskName, createdBy=$createdBy")

        try {
            // 1. 解析上传的文件
            val lineageData = manualLineageImportService.parseLineageFile(file)
            
            // 2. 验证血缘数据
            manualLineageImportService.validateLineageData(lineageData)
            
            logger.info("3c9e8a75 | 血缘文件解析成功: fileName=$fileName, 血缘记录数=${lineageData.size}")

            // 3. 创建血缘任务
            val task = createManualImportTask(generatedTaskName, fileName, createdBy, file.size)
            
            // 4. 更新任务状态为执行中
            updateTaskStatus(task.id, TaskStatus.RUNNING, LocalDateTime.now())

            // 5. 保存血缘数据（这里暂时返回成功，实际保存逻辑需要根据具体存储需求实现）
            val processingTime = System.currentTimeMillis() - startTime
            
            // TODO: 实际保存血缘数据到数据库的逻辑
            // lineageData.forEach { lineage ->
            //     lineageService.saveLineage(lineage, task.id)
            // }
            
            // 6. 更新任务状态为成功
            updateTaskStatus(
                taskId = task.id,
                status = TaskStatus.SUCCESS,
                completedAt = LocalDateTime.now(),
                processingTimeMs = processingTime
            )

            logger.info("a6b3f928 | 手动血缘导入成功: taskId=${task.id}, 处理时间=${processingTime}ms")

            return ManualImportResult(
                taskId = task.id,
                taskName = generatedTaskName,
                fileName = fileName,
                status = TaskStatus.SUCCESS,
                lineageCount = lineageData.size,
                processingTimeMs = processingTime,
                errorMessage = null,
                createdAt = task.createdAt.toString()
            )

        } catch (e: Exception) {
            logger.error("2f8c5d61 | 手动血缘导入失败: fileName=$fileName", e)
            
            // 如果任务已创建，更新为失败状态
            val existingTask = lineageTaskRepository.findByTaskNameAndTaskType(generatedTaskName, TaskType.MANUAL_IMPORT)
            if (existingTask != null) {
                val processingTime = System.currentTimeMillis() - startTime
                updateTaskStatus(
                    taskId = existingTask.id,
                    status = TaskStatus.FAILED,
                    completedAt = LocalDateTime.now(),
                    errorMessage = e.message,
                    processingTimeMs = processingTime
                )
                
                return ManualImportResult(
                    taskId = existingTask.id,
                    taskName = generatedTaskName,
                    fileName = fileName,
                    status = TaskStatus.FAILED,
                    lineageCount = null,
                    processingTimeMs = processingTime,
                    errorMessage = e.message,
                    createdAt = existingTask.createdAt.toString()
                )
            }
            
            // 如果任务未创建，直接抛出异常
            throw e
        }
    }

    /**
     * 创建手动导入任务 (Create manual import task)
     */
    private fun createManualImportTask(
        taskName: String,
        fileName: String,
        createdBy: String,
        fileSize: Long
    ): LineageTask {
        val request = CreateLineageTaskRequest(
            taskName = taskName,
            taskType = TaskType.MANUAL_IMPORT,
            sourceType = SourceType.MANUAL_INPUT,
            sourceIdentifier = fileName,
            sourceContent = "文件大小: ${fileSize} 字节",
            createdBy = createdBy
        )
        
        return lineageTaskRepository.save(request.toLineageTask())
    }

    /**
     * 更新任务状态 (Update task status)
     */
    private fun updateTaskStatus(
        taskId: Long,
        status: TaskStatus,
        executedAt: LocalDateTime? = null,
        completedAt: LocalDateTime? = null,
        errorMessage: String? = null,
        processingTimeMs: Long? = null
    ) {
        lineageTaskCustomRepository.updateTaskExecution(
            taskId = taskId,
            status = status,
            executedAt = executedAt,
            completedAt = completedAt,
            processingTimeMs = processingTimeMs,
            hasChanges = null,
            errorMessage = errorMessage,
            executionId = null
        )
    }

    /**
     * 更新任务执行结果 (Update task execution result)
     */
    private fun updateTaskExecutionResult(
        taskId: Long,
        lineageResult: LineageProcessResult,
        processingTime: Long,
        executionId: String
    ) {
        val status = if (lineageResult.lineageResult.success) TaskStatus.SUCCESS else TaskStatus.FAILED
        val errorMessage = if (!lineageResult.lineageResult.success) {
            lineageResult.lineageResult.errors.joinToString("; ")
        } else null

        lineageTaskCustomRepository.updateTaskExecution(
            taskId = taskId,
            status = status,
            executedAt = null,
            completedAt = LocalDateTime.now(),
            processingTimeMs = processingTime,
            hasChanges = lineageResult.hasChanges,
            errorMessage = errorMessage,
            executionId = executionId
        )
    }

    /**
     * 生成批次ID (Generate batch ID)
     */
    private fun generateBatchId(): String {
        return "batch_${System.currentTimeMillis()}"
    }

    /**
     * 创建失败的任务结果 (Create failed task result)
     */
    private fun createFailedTaskResult(job: DataExchangeJob, exception: Exception): TaskProcessResult {
        return TaskProcessResult(
            taskId = 0,
            jobKey = "${job.readerJobId}_${job.writeJobId}",
            taskName = "数据同步任务-${job.readerJobName}到${job.writeJobName}",
            status = TaskStatus.FAILED,
            processingTimeMs = 0,
            hasChanges = false,
            processingResult = ProcessingResult.FAILED,
            errorMessage = exception.message
        )
    }

    /**
     * 根据源系统ID和目标系统ID获取表对关系 (Get table pairs by source and target system IDs)
     * 
     * @param sourceSystemId 源系统ID
     * @param targetSystemId 目标系统ID
     * @return 表对关系列表
     */
    fun getTablePairsBySystemIds(sourceSystemId: Long, targetSystemId: Long): List<TablePairDto> {
        logger.info("4e8a2c7f | 开始查询表对关系: sourceSystemId=$sourceSystemId, targetSystemId=$targetSystemId")
        
        return lineageTaskCustomRepository.findTablePairsBySystemIds(sourceSystemId, targetSystemId)
    }
}

/**
 * 批量处理请求 (Batch Process Request)
 */
data class BatchProcessRequest(
    val executedBy: String? = null,
    val batchId: String? = null
)

/**
 * 批量处理结果 (Batch Process Result)
 */
data class BatchProcessResult(
    val batchId: String,
    val totalJobs: Int,
    val processedTasks: List<TaskProcessResult>,
    val summary: ProcessingSummary,
    val processingTimeMs: Long
)

/**
 * 任务处理结果 (Task Process Result)
 */
data class TaskProcessResult(
    val taskId: Long,
    val jobKey: String,
    val taskName: String,
    val status: TaskStatus,
    val processingTimeMs: Long,
    val hasChanges: Boolean,
    val processingResult: ProcessingResult,
    val errorMessage: String? = null
)

/**
 * 处理统计信息 (Processing Summary)
 */
data class ProcessingSummary(
    val successful: Int,
    val failed: Int,
    val unchanged: Int,
    val updated: Int,
    val deactivated: Int
)

/**
 * 重跑任务请求 (Rerun Task Request)
 */
data class RerunTaskRequest(
    val executedBy: String,
    val reason: String? = null
)

/**
 * 任务执行结果 (Task Execution Result)
 */
data class TaskExecutionResult(
    val taskId: Long,
    val executionId: String,
    val status: TaskStatus,
    val message: String
)

/**
 * 血缘任务处理异常 (Lineage Task Processing Exception)
 */
class LineageTaskProcessingException(message: String, cause: Throwable? = null) : Exception(message, cause)

/**
 * 血缘任务未找到异常 (Lineage Task Not Found Exception)
 */
class LineageTaskNotFoundException(message: String) : Exception(message)

/**
 * JobProcessingHistory 转 DTO 扩展函数
 */
fun JobProcessingHistory.toDto(): JobProcessingHistoryDto {
    return JobProcessingHistoryDto(
        id = this.id,
        processedAt = this.processedAt.toString(),
        processingResult = this.processingResult.name,
        changesDetected = this.changesDetected,
        processingDurationMs = this.processingDurationMs,
        lineageHash = this.lineageHash,
        errorMessage = this.errorMessage
    )
} 