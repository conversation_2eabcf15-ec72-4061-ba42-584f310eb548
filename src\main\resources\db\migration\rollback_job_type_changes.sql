-- Rollback DDL: Remove job_type enhancements from lineage_job_processing_history table
-- Use this script if you need to revert the job_type migration

-- Step 1: Drop constraints
ALTER TABLE lineage_job_processing_history 
DROP CONSTRAINT IF EXISTS chk_data_exchange_fields;

-- Step 2: Drop new indexes
DROP INDEX IF EXISTS idx_job_type ON lineage_job_processing_history;
DROP INDEX IF EXISTS idx_job_type_processed_at ON lineage_job_processing_history;

-- Step 3: Restore original column constraints (make NOT NULL again)
-- Note: This will fail if there are NULL values, so clean data first
UPDATE lineage_job_processing_history 
SET reader_job_id = 'unknown_reader', write_job_id = 'unknown_writer' 
WHERE reader_job_id IS NULL OR write_job_id IS NULL;

ALTER TABLE lineage_job_processing_history 
MODIFY COLUMN reader_job_id VARCHAR(255) NOT NULL;

ALTER TABLE lineage_job_processing_history 
MODIFY COLUMN write_job_id VARCHAR(255) NOT NULL;

-- Step 4: Drop the job_type column
ALTER TABLE lineage_job_processing_history 
DROP COLUMN job_type;

-- Step 5: Restore original table comment
ALTER TABLE lineage_job_processing_history 
COMMENT = '作业处理历史记录';