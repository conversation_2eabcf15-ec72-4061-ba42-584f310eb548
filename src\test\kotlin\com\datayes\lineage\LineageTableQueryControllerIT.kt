package com.datayes.lineage

import com.datayes.integration.RestApiIntegrationTestBase
import io.restassured.RestAssured.given
import org.assertj.core.api.Assertions.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.slf4j.LoggerFactory

/**
 * 血缘表查询控制器集成测试 (Lineage Table Query Controller Integration Test)
 * 
 * 测试根据元数据系统信息ID查询血缘表的API功能
 */
@DisplayName("血缘表查询API集成测试")
class LineageTableQueryControllerIT : RestApiIntegrationTestBase() {

    private val logger = LoggerFactory.getLogger(LineageTableQueryControllerIT::class.java)

    @Test
    @DisplayName("查询所有血缘表 - 分页测试")
    fun `should return paged lineage tables when query all tables`() {
        logger.info("e1f2g3h4 | 开始测试查询所有血缘表")

        val response = given()
            .`when`()
            .get("/api/lineage/tables?page=1&size=10")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("message", equalTo("血缘表查询成功"))
            .body("data", notNullValue())
            .body("data.content", notNullValue())
            .body("data.page", equalTo(1))
            .body("data.size", equalTo(10))
            .body("data.totalElements", greaterThanOrEqualTo(0))
            .body("data.totalPages", greaterThanOrEqualTo(0))
            .body("data.hasNext", anyOf(equalTo(true), equalTo(false)))
            .body("data.hasPrevious", equalTo(false))
            .body("data.isFirst", equalTo(true))
            .extract()

        logger.info("i5j6k7l8 | 查询所有血缘表测试完成，总记录数: ${response.path<Int>("data.totalElements")}")
    }

    @Test
    @DisplayName("根据系统ID查询血缘表 - 有效系统ID")
    fun `should return lineage tables when query by valid system id`() {
        logger.info("m9n0o1p2 | 开始测试根据系统ID查询血缘表")

        // 使用系统ID 1进行测试（根据之前的数据库查询，这个ID存在）
        val response = given()
            .`when`()
            .get("/api/lineage/tables?metadataSystemId=1&page=1&size=5")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("message", equalTo("血缘表查询成功"))
            .body("data", notNullValue())
            .body("data.content", notNullValue())
            .body("data.page", equalTo(1))
            .body("data.size", equalTo(5))
            .body("data.totalElements", greaterThanOrEqualTo(0))
            .extract()

        logger.info("q3r4s5t6 | 根据系统ID查询血缘表测试完成，系统ID=1的记录数: ${response.path<Int>("data.totalElements")}")
    }

    @Test
    @DisplayName("分页参数验证 - 无效页码")
    fun `should handle invalid page parameters gracefully`() {
        logger.info("u7v8w9x0 | 开始测试分页参数验证")

        // 测试页码为0的情况（应该自动调整为1）
        given()
            .`when`()
            .get("/api/lineage/tables?page=0&size=10")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data.page", equalTo(1)) // 应该被调整为1

        // 测试页面大小超过限制的情况（应该被限制到100）
        given()
            .`when`()
            .get("/api/lineage/tables?page=1&size=200")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data.size", equalTo(100)) // 应该被限制到100

        logger.info("y1z2a3b4 | 分页参数验证测试完成")
    }

    @Test
    @DisplayName("查询不存在的系统ID")
    fun `should return empty result when query by non-existent system id`() {
        logger.info("c5d6e7f8 | 开始测试查询不存在的系统ID")

        // 使用一个不太可能存在的系统ID
        val response = given()
            .`when`()
            .get("/api/lineage/tables?metadataSystemId=99999&page=1&size=10")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data.totalElements", equalTo(0))
            .body("data.content.size()", equalTo(0))
            .extract()

        logger.info("g9h0i1j2 | 查询不存在的系统ID测试完成")
    }

    @Test
    @DisplayName("测试响应数据结构完整性")
    fun `should return complete data structure`() {
        logger.info("k3l4m5n6 | 开始测试响应数据结构完整性")

        val response = given()
            .`when`()
            .get("/api/lineage/tables?page=1&size=5")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data.content", notNullValue())
            .body("data.page", notNullValue())
            .body("data.size", notNullValue())
            .body("data.totalElements", notNullValue())
            .body("data.totalPages", notNullValue())
            .body("data.hasNext", anyOf(equalTo(true), equalTo(false)))
            .body("data.hasPrevious", anyOf(equalTo(true), equalTo(false)))
            .body("data.isFirst", anyOf(equalTo(true), equalTo(false)))
            .body("data.isLast", anyOf(equalTo(true), equalTo(false)))
            .body("data.numberOfElements", notNullValue())
            .extract()

        // 如果有数据，验证数据结构
        val totalElements = response.path<Int>("data.totalElements")
        if (totalElements > 0) {
            assertThat(response.path<Any>("data.content[0].id")).isNotNull()
            assertThat(response.path<String>("data.content[0].tableName")).isNotNull()
            assertThat(response.path<String>("data.content[0].status")).isNotNull()
            assertThat(response.path<Any>("data.content[0].datasource")).isNotNull()
            assertThat(response.path<Any>("data.content[0].datasource.id")).isNotNull()
            assertThat(response.path<String>("data.content[0].datasource.datasourceName")).isNotNull()
            assertThat(response.path<String>("data.content[0].datasource.dbType")).isNotNull()
        }

        logger.info("o7p8q9r0 | 响应数据结构完整性测试完成")
    }

    @Test
    @DisplayName("Host模糊匹配过滤测试")
    fun `should filter by host fuzzy match`() {
        logger.info("a1b2c3d4 | 开始测试Host模糊匹配过滤")

        // 测试Host模糊匹配（使用常见的内网IP段）
        val response = given()
            .`when`()
            .get("/api/lineage/tables?host=10.&page=1&size=10")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data", notNullValue())
            .extract()

        val totalElements = response.path<Int>("data.totalElements")
        logger.info("e5f6g7h8 | Host模糊匹配过滤测试完成，匹配到${totalElements}条记录")

        // 如果有匹配结果，验证host确实包含指定字符串
        if (totalElements > 0) {
            val hosts = response.path<List<String>>("data.content.datasource.host")
            hosts.forEach { host ->
                assertThat(host).contains("10.")
            }
            logger.info("i9j0k1l2 | Host过滤结果验证通过")
        }
    }

    @Test
    @DisplayName("DatabaseName模糊匹配过滤测试")
    fun `should filter by database name fuzzy match`() {
        logger.info("m3n4o5p6 | 开始测试DatabaseName模糊匹配过滤")

        // 测试DatabaseName模糊匹配（使用常见的数据库名称片段）
        val response = given()
            .`when`()
            .get("/api/lineage/tables?databaseName=db&page=1&size=10")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data", notNullValue())
            .extract()

        val totalElements = response.path<Int>("data.totalElements")
        logger.info("q7r8s9t0 | DatabaseName模糊匹配过滤测试完成，匹配到${totalElements}条记录")

        // 如果有匹配结果，验证databaseName确实包含指定字符串
        if (totalElements > 0) {
            val databaseNames = response.path<List<String>>("data.content.datasource.databaseName")
            databaseNames.forEach { dbName ->
                assertThat(dbName.lowercase()).contains("db")
            }
            logger.info("u1v2w3x4 | DatabaseName过滤结果验证通过")
        }
    }

    @Test
    @DisplayName("Schema模糊匹配过滤测试")
    fun `should filter by schema fuzzy match`() {
        logger.info("y5z6a7b8 | 开始测试Schema模糊匹配过滤")

        // 测试Schema模糊匹配（使用常见的schema名称）
        val response = given()
            .`when`()
            .get("/api/lineage/tables?schema=dw&page=1&size=10")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data", notNullValue())
            .extract()

        val totalElements = response.path<Int>("data.totalElements")
        logger.info("c9d0e1f2 | Schema模糊匹配过滤测试完成，匹配到${totalElements}条记录")

        // 如果有匹配结果，验证schema确实包含指定字符串
        if (totalElements > 0) {
            val schemas = response.path<List<String>>("data.content.schemaName")
            schemas.forEach { schema ->
                assertThat(schema.lowercase()).contains("dw")
            }
            logger.info("g3h4i5j6 | Schema过滤结果验证通过")
        }
    }

    @Test
    @DisplayName("组合过滤条件测试")
    fun `should filter by multiple conditions`() {
        logger.info("k7l8m9n0 | 开始测试组合过滤条件")

        // 测试组合过滤条件：系统ID + Host + DatabaseName
        val response = given()
            .`when`()
            .get("/api/lineage/tables?metadataSystemId=1&host=10.&databaseName=db&page=1&size=5")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data", notNullValue())
            .extract()

        val totalElements = response.path<Int>("data.totalElements")
        logger.info("o1p2q3r4 | 组合过滤条件测试完成，匹配到${totalElements}条记录")

        // 验证分页信息
        assertThat(response.path<Int>("data.page")).isEqualTo(1)
        assertThat(response.path<Int>("data.size")).isEqualTo(5)
    }

    @Test
    @DisplayName("空字符串过滤条件测试")
    fun `should handle empty string filter parameters`() {
        logger.info("s5t6u7v8 | 开始测试空字符串过滤条件")

        // 测试空字符串过滤条件（应该被忽略）
        val response = given()
            .`when`()
            .get("/api/lineage/tables?host=&databaseName=&schema=&page=1&size=10")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data", notNullValue())
            .extract()

        val totalElements = response.path<Int>("data.totalElements")
        logger.info("w9x0y1z2 | 空字符串过滤条件测试完成，返回${totalElements}条记录")

        // 空字符串应该被忽略，结果应该等同于没有过滤条件
        val baselineResponse = given()
            .`when`()
            .get("/api/lineage/tables?page=1&size=10")
            .then()
            .statusCode(200)
            .extract()

        val baselineTotalElements = baselineResponse.path<Int>("data.totalElements")
        assertThat(totalElements).isEqualTo(baselineTotalElements)
        logger.info("a3b4c5d6 | 空字符串过滤条件验证通过")
    }

    @Test
    @DisplayName("过滤条件大小写不敏感测试")
    fun `should be case insensitive for filter parameters`() {
        logger.info("e7f8g9h0 | 开始测试过滤条件大小写不敏感")

        // 测试大写字母过滤
        val upperCaseResponse = given()
            .`when`()
            .get("/api/lineage/tables?databaseName=DB&page=1&size=10")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .extract()

        // 测试小写字母过滤
        val lowerCaseResponse = given()
            .`when`()
            .get("/api/lineage/tables?databaseName=db&page=1&size=10")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .extract()

        val upperCaseTotal = upperCaseResponse.path<Int>("data.totalElements")
        val lowerCaseTotal = lowerCaseResponse.path<Int>("data.totalElements")

        logger.info("i1j2k3l4 | 过滤条件大小写不敏感测试完成，大写结果: $upperCaseTotal，小写结果: $lowerCaseTotal")

        // 两种情况应该返回相同的结果（如果数据库中有匹配的记录）
        assertThat(upperCaseTotal).isEqualTo(lowerCaseTotal)
        logger.info("m5n6o7p8 | 大小写不敏感验证通过")
    }
}