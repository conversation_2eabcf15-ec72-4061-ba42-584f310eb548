# Manual Lineage Import File Format Examples

UC-08: Manual Lineage Import supports uploading files to create lineage tasks with `MANUAL_IMPORT` task type and `MANUAL_INPUT` source type.

## Supported File Formats

- JSON (.json)
- CSV (.csv)

## JSON Format

### Single Lineage Object

```json
{
  "jobId": "manual_import_001",
  "jobName": "User Table to Customer Table",
  "sourceDatabase": {
    "dbType": "mysql",
    "host": "***********",
    "port": 3306,
    "databaseName": "source_db",
    "originalConnectionString": "***************************************"
  },
  "targetDatabase": {
    "dbType": "mysql", 
    "host": "***********",
    "port": 3306,
    "databaseName": "target_db",
    "originalConnectionString": "***************************************"
  },
  "tableLineage": {
    "sourceTables": [
      {
        "schema": null,
        "tableName": "users",
        "database": "source_db"
      }
    ],
    "targetTable": {
      "schema": null,
      "tableName": "customers",
      "database": "target_db"
    },
    "lineageType": "DIRECT_COPY"
  },
  "columnLineages": [
    {
      "sourceColumn": {
        "columnName": "id",
        "dataType": "BIGINT",
        "comment": "User ID",
        "tableName": "users"
      },
      "targetColumn": {
        "columnName": "customer_id",
        "dataType": "BIGINT", 
        "comment": "Customer ID",
        "tableName": "customers"
      },
      "transformation": {
        "transformationType": "NONE",
        "description": "Direct copy",
        "expression": null
      }
    },
    {
      "sourceColumn": {
        "columnName": "name",
        "dataType": "VARCHAR",
        "comment": "User name",
        "tableName": "users"
      },
      "targetColumn": {
        "columnName": "customer_name",
        "dataType": "VARCHAR",
        "comment": "Customer name", 
        "tableName": "customers"
      },
      "transformation": {
        "transformationType": "FUNCTION",
        "description": "Uppercase transformation",
        "expression": "UPPER(name)"
      }
    }
  ],
  "originalSql": "INSERT INTO customers (customer_id, customer_name) SELECT id, UPPER(name) FROM users"
}
```

### Multiple Lineage Objects Array

```json
[
  {
    "jobId": "manual_import_001",
    "jobName": "Users to Customers",
    // ... lineage object as above
  },
  {
    "jobId": "manual_import_002", 
    "jobName": "Orders to Sales",
    // ... another lineage object
  }
]
```

### Wrapper Object with Lineages Array

```json
{
  "lineages": [
    {
      "jobId": "manual_import_001",
      // ... lineage object
    },
    {
      "jobId": "manual_import_002",
      // ... lineage object  
    }
  ]
}
```

## CSV Format

### Required Columns
- `source_table` (or `sourceTable`) - Source table name
- `target_table` (or `targetTable`) - Target table name

### Optional Columns
- `job_id` / `jobId` - Job identifier
- `job_name` / `jobName` - Job name
- `source_database` / `sourceDatabase` - Source database name
- `target_database` / `targetDatabase` - Target database name
- `source_schema` - Source table schema
- `target_schema` - Target table schema
- `source_db_type` - Source database type (default: mysql)
- `target_db_type` - Target database type (default: mysql)
- `source_host` - Source database host
- `target_host` - Target database host
- `source_port` - Source database port
- `target_port` - Target database port
- `source_column` / `sourceColumn` - Source column name
- `target_column` / `targetColumn` - Target column name
- `source_column_type` - Source column data type
- `target_column_type` - Target column data type
- `sql` / `original_sql` - Original SQL statement

### Example CSV

```csv
job_id,job_name,source_database,target_database,source_table,target_table,source_column,target_column,sql
manual_001,Users to Customers,source_db,target_db,users,customers,id,customer_id,"INSERT INTO customers SELECT * FROM users"
manual_002,Orders to Sales,source_db,target_db,orders,sales,order_id,sale_id,"INSERT INTO sales SELECT * FROM orders"
manual_003,Products Import,external_db,main_db,products,inventory,product_code,item_code,"INSERT INTO inventory (item_code) SELECT product_code FROM products"
```

## Field Mapping

### Database Info Fields
Both snake_case and camelCase are supported:
- `db_type` / `dbType`
- `database_name` / `databaseName` 
- `original_connection_string` / `originalConnectionString`

### Table Lineage Fields
- `source_tables` / `sourceTables`
- `target_table` / `targetTable`
- `lineage_type` / `lineageType`

### Column Lineage Fields  
- `source_column` / `sourceColumn`
- `target_column` / `targetColumn`
- `column_name` / `columnName`
- `data_type` / `dataType`
- `table_name` / `tableName`

### Transformation Fields
- `transformation_type` / `transformationType`

## Lineage Types
- `DIRECT_COPY` (default)
- `SQL_QUERY`
- `AGGREGATION`
- `JOIN`
- `FILTER`
- `COMPLEX_TRANSFORMATION`
- `DATA_MOVEMENT`
- `SCRIPT_PROCESSING`

## Transformation Types
- `NONE` (default)
- `TYPE_CAST`
- `FUNCTION`
- `EXPRESSION`
- `CONSTANT`
- `AGGREGATION`
- `CONDITIONAL`

## File Size Limits
- Maximum file size: 10MB
- Maximum request size: 10MB

## Validation Rules
1. Files must not be empty
2. JSON files must be valid JSON format
3. CSV files must have header row and at least one data row
4. CSV files must contain required columns: `source_table`, `target_table`
5. All lineage objects must have non-empty `jobId` and `jobName`
6. Source tables list cannot be empty
7. Target table name cannot be empty
8. Column lineages must reference tables that exist in table lineage

## Error Handling
- Invalid file format: Returns 400 Bad Request
- File parsing errors: Returns 422 Unprocessable Entity with detailed error message
- File too large: Returns 400 Bad Request
- Validation errors: Returns 422 Unprocessable Entity with validation details