#!/bin/bash

# 获取今天的提交哈希
COMMITS=$(git log --since="midnight" --author="$(git config user.name)" --pretty=%H)

# 初始化计数器
ADDED=0
DELETED=0

# 遍历每个提交
for COMMIT in $COMMITS; do
    # 获取提交的统计信息
    STATS=$(git show --stat --oneline $COMMIT | grep -E '[0-9]+ insertion|deletion' | awk '{added+=$4; deleted+=$6} END {print added, deleted}')
    # 累加添加和删除的行数
    if [ ! -z "$STATS" ]; then
        read ADD DEL <<< "$STATS"
        ADDED=$((ADDED + ADD))
        DELETED=$((DELETED + DEL))
    fi
done

# 输出结果
echo "今天添加的行数 (Lines Added): $ADDED"
echo "今天删除的行数 (Lines Deleted): $DELETED"
