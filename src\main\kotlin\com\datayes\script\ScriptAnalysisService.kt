package com.datayes.script

import com.datayes.lineage.LineageService
import com.datayes.lineage.TableLineageView
import com.datayes.shell.ShellScriptParser
import com.datayes.sql.SqlParser
import com.datayes.sql.SqlParsingException
import com.datayes.sql.TableReference
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import java.util.UUID

/**
 * 脚本分析服务 (Script Analysis Service)
 * 
 * UC-3: 实现脚本影响分析的核心逻辑
 */
@Service
class ScriptAnalysisService(
    private val scriptRepository: ScriptRepository,
    private val lineageService: LineageService,
    private val objectMapper: ObjectMapper
) {

    private val logger = LoggerFactory.getLogger(ScriptAnalysisService::class.java)

    /**
     * 异步触发脚本分析 (Trigger script analysis asynchronously)
     * 
     * 在脚本上传成功后调用，异步处理分析任务
     */
    @Async
    fun triggerScriptAnalysis(scriptId: Long) {
        logger.info("3a7f8b2e | 开始异步脚本分析: scriptId=$scriptId")
        analyzeScript(scriptId)
    }

    /**
     * 分析脚本并更新结果 (Analyze script and update results)
     * 
     * UC-3: 核心分析逻辑
     */
    fun analyzeScript(scriptId: Long): ScriptAnalysisResult {
        logger.info("b9d4c2f1 | 开始分析脚本: scriptId=$scriptId")

        try {
            // 1. 获取脚本信息
            val script = scriptRepository.findById(scriptId)
            if (script == null) {
                logger.error("f2e8a5c7 | 脚本不存在: scriptId=$scriptId")
                return ScriptAnalysisResult(
                    success = false,
                    message = "脚本不存在 (Script not found)",
                    scriptId = scriptId
                )
            }

            // 2. 更新状态为分析中
            val statusUpdated = scriptRepository.updateAnalysisStatus(scriptId, AnalysisStatus.ANALYZING)
            if (!statusUpdated) {
                logger.error("e6f9b3a8 | 更新脚本状态失败: scriptId=$scriptId")
                return ScriptAnalysisResult(
                    success = false,
                    message = "更新脚本状态失败 (Failed to update script status)",
                    scriptId = scriptId
                )
            }

            // 3. 解析脚本内容
            val scriptTables = parseScriptContent(script)

            // 4. 查询血缘库获取相关依赖关系
            val lineageData = queryLineageDatabase(scriptTables)

            // 5. 合并分析结果
            val analysisResult = mergeAnalysisResults(scriptTables, lineageData)

            // 6. 生成临时血缘ID
            val temporaryLineageId = generateTemporaryLineageId()

            // 7. 序列化分析结果为JSON
            val analysisResultJson = serializeAnalysisResult(analysisResult)

            // 8. 更新数据库
            val finalUpdateResult = updateAnalysisResult(scriptId, analysisResultJson, temporaryLineageId)

            if (finalUpdateResult) {
                logger.info("c4d1e9a2 | 脚本分析完成: scriptId=$scriptId, temporaryLineageId=$temporaryLineageId")
                return ScriptAnalysisResult(
                    success = true,
                    message = "脚本分析完成 (Script analysis completed)",
                    scriptId = scriptId,
                    analysisResult = analysisResult,
                    temporaryLineageId = temporaryLineageId
                )
            } else {
                logger.error("a8b5d2c9 | 保存分析结果失败: scriptId=$scriptId")
                scriptRepository.updateAnalysisStatus(scriptId, AnalysisStatus.FAILED, "保存分析结果失败")
                return ScriptAnalysisResult(
                    success = false,
                    message = "保存分析结果失败 (Failed to save analysis result)",
                    scriptId = scriptId
                )
            }

        } catch (e: Exception) {
            logger.error("f7b4e1a3 | 脚本分析过程中发生错误: scriptId=$scriptId", e)
            scriptRepository.updateAnalysisStatus(scriptId, AnalysisStatus.FAILED, "分析失败: ${e.message}")
            return ScriptAnalysisResult(
                success = false,
                message = "分析失败: ${e.message} (Analysis failed)",
                scriptId = scriptId
            )
        }
    }

    /**
     * 解析脚本内容 (Parse script content)
     * 
     * 根据脚本类型调用相应的解析器
     */
    private fun parseScriptContent(script: UploadedScript): List<ScriptTableReference> {
        logger.info("d5e2f8a1 | 解析脚本内容: scriptType=${script.scriptType}, scriptName=${script.scriptName}")

        return when (script.scriptType) {
            ScriptType.SQL -> parseSqlScript(script.scriptContent)
            ScriptType.SHELL -> parseShellScript(script.scriptContent)
        }
    }

    /**
     * 解析SQL脚本 (Parse SQL script)
     */
    private fun parseSqlScript(scriptContent: String): List<ScriptTableReference> {
        logger.info("e8a4b7d2 | 解析SQL脚本内容")

        val scriptTables = mutableListOf<ScriptTableReference>()

        try {
            // 按分号分隔SQL语句
            val sqlStatements = scriptContent.split(";")
                .map { it.trim() }
                .filter { it.isNotBlank() && !it.startsWith("--") }

            for ((index, sqlStatement) in sqlStatements.withIndex()) {
                try {
                    // 尝试解析SELECT语句
                    val parseResult = SqlParser.parse(sqlStatement)
                    parseResult.tables.forEach { table ->
                        scriptTables.add(
                            ScriptTableReference(
                                schema = table.schema,
                                tableName = table.name,
                                alias = table.alias,
                                statementType = "SELECT",
                                statementIndex = index,
                                isSource = true,
                                isTarget = false
                            )
                        )
                    }
                } catch (e: SqlParsingException) {
                    // 尝试解析数据修改语句
                    try {
                        val dataModResult = SqlParser.parseDataModification(sqlStatement)
                        
                        // 目标表
                        scriptTables.add(
                            ScriptTableReference(
                                schema = dataModResult.targetTable.schema,
                                tableName = dataModResult.targetTable.name,
                                alias = null,
                                statementType = "INSERT",
                                statementIndex = index,
                                isSource = false,
                                isTarget = true
                            )
                        )

                        // 源表
                        dataModResult.sourceTables.forEach { sourceTable ->
                            scriptTables.add(
                                ScriptTableReference(
                                    schema = sourceTable.schema,
                                    tableName = sourceTable.name,
                                    alias = sourceTable.alias,
                                    statementType = "INSERT",
                                    statementIndex = index,
                                    isSource = true,
                                    isTarget = false
                                )
                            )
                        }
                    } catch (e2: SqlParsingException) {
                        logger.warn("a3b8d5e2 | 无法解析SQL语句 (语句索引: $index): ${e2.message}")
                    }
                }
            }

        } catch (e: Exception) {
            logger.error("f9c6a2d7 | SQL脚本解析失败", e)
            throw ScriptAnalysisException("SQL脚本解析失败: ${e.message}", e)
        }

        logger.info("b2d5f8a4 | SQL脚本解析完成，发现 ${scriptTables.size} 个表引用")
        return scriptTables
    }

    /**
     * 解析Shell脚本 (Parse Shell script)
     */
    private fun parseShellScript(scriptContent: String): List<ScriptTableReference> {
        logger.info("g7h4i1j6 | 解析Shell脚本内容")

        val scriptTables = mutableListOf<ScriptTableReference>()

        try {
            // 从Shell脚本中提取SQL语句
            val extractedSqls = ShellScriptParser.parseShellScriptForSql(scriptContent)

            for ((index, sqlStatement) in extractedSqls.withIndex()) {
                try {
                    // 解析提取的SQL语句
                    val parseResult = SqlParser.parse(sqlStatement)
                    parseResult.tables.forEach { table ->
                        scriptTables.add(
                            ScriptTableReference(
                                schema = table.schema,
                                tableName = table.name,
                                alias = table.alias,
                                statementType = "SELECT",
                                statementIndex = index,
                                isSource = true,
                                isTarget = false
                            )
                        )
                    }
                } catch (e: SqlParsingException) {
                    // 尝试解析数据修改语句
                    try {
                        val dataModResult = SqlParser.parseDataModification(sqlStatement)
                        
                        // 目标表
                        scriptTables.add(
                            ScriptTableReference(
                                schema = dataModResult.targetTable.schema,
                                tableName = dataModResult.targetTable.name,
                                alias = null,
                                statementType = "INSERT",
                                statementIndex = index,
                                isSource = false,
                                isTarget = true
                            )
                        )

                        // 源表
                        dataModResult.sourceTables.forEach { sourceTable ->
                            scriptTables.add(
                                ScriptTableReference(
                                    schema = sourceTable.schema,
                                    tableName = sourceTable.name,
                                    alias = sourceTable.alias,
                                    statementType = "INSERT",
                                    statementIndex = index,
                                    isSource = true,
                                    isTarget = false
                                )
                            )
                        }
                    } catch (e2: SqlParsingException) {
                        logger.warn("d8e5f2a9 | 无法解析从Shell脚本提取的SQL语句 (语句索引: $index): ${e2.message}")
                    }
                }
            }

        } catch (e: Exception) {
            logger.error("h3i6j9k2 | Shell脚本解析失败", e)
            throw ScriptAnalysisException("Shell脚本解析失败: ${e.message}", e)
        }

        logger.info("k5l8m1n4 | Shell脚本解析完成，发现 ${scriptTables.size} 个表引用")
        return scriptTables
    }

    /**
     * 查询血缘数据库 (Query lineage database)
     * 
     * 根据脚本中解析出的表，查询现有血缘库中的上下游依赖关系
     */
    private fun queryLineageDatabase(scriptTables: List<ScriptTableReference>): Map<String, LineageData> {
        logger.info("m7n4o1p8 | 查询血缘数据库，表数量: ${scriptTables.size}")

        val lineageDataMap = mutableMapOf<String, LineageData>()

        // 去重表名
        val uniqueTables = scriptTables
            .map { "${it.schema ?: "default"}.${it.tableName}" }
            .distinct()

        for (tableFullName in uniqueTables) {
            try {
                // 这里简化处理，实际应该根据schema和tableName查询表ID
                // 由于当前血缘服务是基于tableId的，我们需要先查询表ID
                val tableId = findTableIdByName(tableFullName)
                
                if (tableId != null) {
                    val upstreamLineage = lineageService.findUpstreamLineageByTableId(tableId, 3)
                    val downstreamLineage = lineageService.findDownstreamLineageByTableId(tableId, 3)

                    lineageDataMap[tableFullName] = LineageData(
                        tableId = tableId,
                        tableFullName = tableFullName,
                        upstreamTables = upstreamLineage,
                        downstreamTables = downstreamLineage
                    )

                    logger.info("p2q5r8s1 | 查询到血缘数据: table=$tableFullName, upstreamCount=${upstreamLineage.size}, downstreamCount=${downstreamLineage.size}")
                } else {
                    logger.warn("s4t7u0v3 | 在血缘库中未找到表: $tableFullName")
                    lineageDataMap[tableFullName] = LineageData(
                        tableId = null,
                        tableFullName = tableFullName,
                        upstreamTables = emptyList(),
                        downstreamTables = emptyList()
                    )
                }

            } catch (e: Exception) {
                logger.warn("v6w9x2y5 | 查询表血缘数据时发生错误: table=$tableFullName", e)
                lineageDataMap[tableFullName] = LineageData(
                    tableId = null,
                    tableFullName = tableFullName,
                    upstreamTables = emptyList(),
                    downstreamTables = emptyList()
                )
            }
        }

        logger.info("y8z1a4b7 | 血缘数据库查询完成，查询到 ${lineageDataMap.size} 个表的血缘信息")
        return lineageDataMap
    }

    /**
     * 根据表名查找表ID (Find table ID by name)
     * 
     * 这是一个简化的实现，实际上需要根据具体的血缘数据库结构来实现
     */
    private fun findTableIdByName(tableFullName: String): Long? {
        // TODO: 实现根据表名查找表ID的逻辑
        // 这里需要根据实际的血缘数据库表结构来实现
        // 可能需要查询元数据表或直接查询血缘表来获取tableId
        logger.debug("b0c3d6e9 | 查找表ID: tableFullName=$tableFullName")
        
        // 临时返回null，表示未找到
        // 在真实实现中，这里应该查询数据库
        return null
    }

    /**
     * 合并分析结果 (Merge analysis results)
     * 
     * 将脚本解析结果和血缘查询结果合并
     */
    private fun mergeAnalysisResults(
        scriptTables: List<ScriptTableReference>,
        lineageData: Map<String, LineageData>
    ): ScriptImpactAnalysisResult {
        logger.info("e2f5g8h1 | 合并分析结果")

        val directRelationships = scriptTables.map { scriptTable ->
            val tableFullName = "${scriptTable.schema ?: "default"}.${scriptTable.tableName}"
            val relatedLineageData = lineageData[tableFullName]

            DirectTableRelationship(
                schema = scriptTable.schema,
                tableName = scriptTable.tableName,
                alias = scriptTable.alias,
                statementType = scriptTable.statementType,
                statementIndex = scriptTable.statementIndex,
                isSource = scriptTable.isSource,
                isTarget = scriptTable.isTarget,
                hasLineageData = relatedLineageData != null && relatedLineageData.tableId != null
            )
        }

        val extendedLineage = lineageData.values.toList()

        val analysisResult = ScriptImpactAnalysisResult(
            scriptId = null, // 将在上层设置
            analyzedAt = LocalDateTime.now(),
            directRelationships = directRelationships,
            extendedLineage = extendedLineage,
            summary = ScriptAnalysisSummary(
                totalTablesFound = scriptTables.size,
                uniqueTablesFound = scriptTables.map { "${it.schema ?: "default"}.${it.tableName}" }.distinct().size,
                tablesWithLineageData = lineageData.values.count { it.tableId != null },
                totalUpstreamCount = lineageData.values.sumOf { it.upstreamTables.size },
                totalDownstreamCount = lineageData.values.sumOf { it.downstreamTables.size }
            )
        )

        logger.info("h4i7j0k3 | 分析结果合并完成: directRelationships=${directRelationships.size}, extendedLineage=${extendedLineage.size}")
        return analysisResult
    }

    /**
     * 生成临时血缘ID (Generate temporary lineage ID)
     */
    private fun generateTemporaryLineageId(): String {
        return "temp_lineage_${UUID.randomUUID().toString().replace("-", "")}"
    }

    /**
     * 序列化分析结果为JSON (Serialize analysis result to JSON)
     */
    private fun serializeAnalysisResult(analysisResult: ScriptImpactAnalysisResult): String {
        return try {
            objectMapper.writeValueAsString(analysisResult)
        } catch (e: Exception) {
            logger.error("k6l9m2n5 | 序列化分析结果失败", e)
            throw ScriptAnalysisException("序列化分析结果失败: ${e.message}", e)
        }
    }

    /**
     * 更新分析结果到数据库 (Update analysis result to database)
     */
    private fun updateAnalysisResult(scriptId: Long, analysisResultJson: String, temporaryLineageId: String): Boolean {
        return try {
            scriptRepository.updateAnalysisStatus(scriptId, AnalysisStatus.COMPLETED, analysisResultJson, temporaryLineageId)
        } catch (e: Exception) {
            logger.error("n8o1p4q7 | 更新分析结果到数据库失败: scriptId=$scriptId", e)
            false
        }
    }
}

/**
 * 脚本表引用 (Script Table Reference)
 * 
 * 从脚本中解析出的表引用信息
 */
data class ScriptTableReference(
    val schema: String?,
    val tableName: String,
    val alias: String?,
    val statementType: String, // SELECT, INSERT, UPDATE, DELETE等
    val statementIndex: Int,   // 在脚本中的语句序号
    val isSource: Boolean,     // 是否为源表
    val isTarget: Boolean      // 是否为目标表
)

/**
 * 血缘数据 (Lineage Data)
 * 
 * 从血缘库中查询到的表血缘信息
 */
data class LineageData(
    val tableId: Long?,
    val tableFullName: String,
    val upstreamTables: List<TableLineageView>,
    val downstreamTables: List<TableLineageView>
)

/**
 * 脚本影响分析结果 (Script Impact Analysis Result)
 * 
 * UC-3: 完整的影响分析结果，包含直接关系和扩展血缘
 */
data class ScriptImpactAnalysisResult(
    val scriptId: Long?,
    val analyzedAt: LocalDateTime,
    val directRelationships: List<DirectTableRelationship>,
    val extendedLineage: List<LineageData>,
    val summary: ScriptAnalysisSummary
)

/**
 * 直接表关系 (Direct Table Relationship)
 * 
 * 从脚本中直接解析出的表关系
 */
data class DirectTableRelationship(
    val schema: String?,
    val tableName: String,
    val alias: String?,
    val statementType: String,
    val statementIndex: Int,
    val isSource: Boolean,
    val isTarget: Boolean,
    val hasLineageData: Boolean // 是否在血缘库中找到相关数据
)

/**
 * 脚本分析摘要 (Script Analysis Summary)
 * 
 * 分析结果的统计摘要
 */
data class ScriptAnalysisSummary(
    val totalTablesFound: Int,          // 脚本中发现的表总数
    val uniqueTablesFound: Int,         // 脚本中唯一表数量
    val tablesWithLineageData: Int,     // 在血缘库中找到数据的表数量
    val totalUpstreamCount: Int,        // 上游表总数
    val totalDownstreamCount: Int       // 下游表总数
)

/**
 * 脚本分析结果 (Script Analysis Result)
 * 
 * 分析操作的执行结果
 */
data class ScriptAnalysisResult(
    val success: Boolean,
    val message: String,
    val scriptId: Long,
    val analysisResult: ScriptImpactAnalysisResult? = null,
    val temporaryLineageId: String? = null
)

/**
 * 脚本分析异常 (Script Analysis Exception)
 */
class ScriptAnalysisException(message: String, cause: Throwable? = null) : RuntimeException(message, cause)