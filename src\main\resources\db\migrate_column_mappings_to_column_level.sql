-- ====================================================================
-- 数据迁移：将 lineage_column_mappings 表的数据迁移到 lineage_relationships 表
-- Migration: Move data from lineage_column_mappings table to lineage_relationships table
-- ====================================================================

-- 1. 备份现有的 lineage_column_mappings 数据
-- Backup existing lineage_column_mappings data
CREATE TABLE IF NOT EXISTS lineage_column_mappings_backup AS
SELECT * FROM lineage_column_mappings;

-- 2. 迁移数据：将列映射转换为 COLUMN_LEVEL 关系
-- Migrate data: Convert column mappings to COLUMN_LEVEL relationships
INSERT INTO lineage_relationships (
    relationship_type,
    source_table_id,
    target_table_id,
    source_column_id,
    target_column_id,
    transformation_type,
    transformation_description,
    transformation_expression,
    source_system,
    confidence_score,
    is_active,
    created_at,
    updated_at
)
SELECT 
    'COLUMN_LEVEL' as relationship_type,
    lr.source_table_id,
    lr.target_table_id,
    -- 尝试通过列名查找列ID，如果找不到则使用NULL
    COALESCE(sc.id, (
        SELECT lc.id FROM lineage_columns lc 
        WHERE lc.table_id = lr.source_table_id 
        AND lc.column_name = cm.source_column_name 
        LIMIT 1
    )) as source_column_id,
    COALESCE(tc.id, (
        SELECT lc.id FROM lineage_columns lc 
        WHERE lc.table_id = lr.target_table_id 
        AND lc.column_name = cm.target_column_name 
        LIMIT 1
    )) as target_column_id,
    cm.transformation_type,
    cm.transformation_description,
    cm.transformation_expression,
    'MANUAL_INPUT' as source_system,
    cm.confidence_score,
    true as is_active,
    cm.created_at,
    cm.updated_at
FROM lineage_column_mappings cm
JOIN lineage_relationships lr ON cm.table_relationship_id = lr.id
LEFT JOIN lineage_columns sc ON cm.source_column_id = sc.id
LEFT JOIN lineage_columns tc ON cm.target_column_id = tc.id
WHERE lr.source_system = 'MANUAL_INPUT' AND lr.is_active = true;

-- 3. 为缺失的列创建记录（如果需要的话）
-- Create missing column records if needed
INSERT IGNORE INTO lineage_columns (table_id, column_name, data_type, column_comment)
SELECT DISTINCT
    lr.source_table_id as table_id,
    cm.source_column_name as column_name,
    'VARCHAR' as data_type,
    '从列映射迁移创建' as column_comment
FROM lineage_column_mappings cm
JOIN lineage_relationships lr ON cm.table_relationship_id = lr.id
LEFT JOIN lineage_columns sc ON sc.table_id = lr.source_table_id AND sc.column_name = cm.source_column_name
WHERE lr.source_system = 'MANUAL_INPUT' 
  AND lr.is_active = true 
  AND sc.id IS NULL;

INSERT IGNORE INTO lineage_columns (table_id, column_name, data_type, column_comment)
SELECT DISTINCT
    lr.target_table_id as table_id,
    cm.target_column_name as column_name,
    'VARCHAR' as data_type,
    '从列映射迁移创建' as column_comment
FROM lineage_column_mappings cm
JOIN lineage_relationships lr ON cm.table_relationship_id = lr.id
LEFT JOIN lineage_columns tc ON tc.table_id = lr.target_table_id AND tc.column_name = cm.target_column_name
WHERE lr.source_system = 'MANUAL_INPUT' 
  AND lr.is_active = true 
  AND tc.id IS NULL;

-- 4. 更新新创建的 COLUMN_LEVEL 关系的列ID（如果之前是NULL）
-- Update column IDs for newly created COLUMN_LEVEL relationships
UPDATE lineage_relationships lr
JOIN lineage_columns sc ON sc.table_id = lr.source_table_id
JOIN lineage_columns tc ON tc.table_id = lr.target_table_id
SET lr.source_column_id = sc.id,
    lr.target_column_id = tc.id
WHERE lr.relationship_type = 'COLUMN_LEVEL'
  AND lr.source_system = 'MANUAL_INPUT'
  AND lr.is_active = true
  AND (lr.source_column_id IS NULL OR lr.target_column_id IS NULL)
  AND lr.created_at >= (SELECT MIN(created_at) FROM lineage_column_mappings_backup);

-- 5. 验证迁移结果
-- Verify migration results
SELECT 
    '迁移前列映射总数' as description,
    COUNT(*) as count
FROM lineage_column_mappings_backup
UNION ALL
SELECT 
    '迁移后COLUMN_LEVEL关系数' as description,
    COUNT(*) as count
FROM lineage_relationships 
WHERE relationship_type = 'COLUMN_LEVEL' 
  AND source_system = 'MANUAL_INPUT' 
  AND is_active = true;

-- 6. 检查是否有列ID为NULL的记录（需要手动处理）
-- Check for records with NULL column IDs (need manual handling)
SELECT 
    '缺失列ID的COLUMN_LEVEL关系数' as description,
    COUNT(*) as count
FROM lineage_relationships 
WHERE relationship_type = 'COLUMN_LEVEL'
  AND source_system = 'MANUAL_INPUT'
  AND is_active = true
  AND (source_column_id IS NULL OR target_column_id IS NULL);

-- ====================================================================
-- 清理步骤（在确认迁移成功后执行）
-- Cleanup steps (execute after confirming successful migration)
-- ====================================================================

-- 注意：以下清理步骤仅在确认迁移成功后执行
-- Note: Execute the following cleanup steps only after confirming successful migration

-- 1. 删除外键约束（如果存在）
-- Drop foreign key constraints (if exist)
-- ALTER TABLE lineage_column_mappings DROP FOREIGN KEY IF EXISTS lineage_column_mappings_ibfk_1;

-- 2. 删除 lineage_column_mappings 表
-- Drop lineage_column_mappings table
-- DROP TABLE IF EXISTS lineage_column_mappings;

-- 3. 删除备份表（可选，建议保留一段时间）
-- Drop backup table (optional, recommend keeping for a while)
-- DROP TABLE IF EXISTS lineage_column_mappings_backup;

-- ====================================================================
-- 验证查询
-- Verification queries
-- ====================================================================

-- 查看迁移后的数据示例
-- View migrated data examples
SELECT 
    lr.id,
    lr.relationship_type,
    st.table_name as source_table,
    sc.column_name as source_column,
    tt.table_name as target_table,
    tc.column_name as target_column,
    lr.transformation_type,
    lr.confidence_score
FROM lineage_relationships lr
LEFT JOIN lineage_tables st ON lr.source_table_id = st.id
LEFT JOIN lineage_tables tt ON lr.target_table_id = tt.id
LEFT JOIN lineage_columns sc ON lr.source_column_id = sc.id
LEFT JOIN lineage_columns tc ON lr.target_column_id = tc.id
WHERE lr.relationship_type = 'COLUMN_LEVEL'
  AND lr.source_system = 'MANUAL_INPUT'
  AND lr.is_active = true
ORDER BY lr.created_at DESC
LIMIT 10;