# Lineage Enhancement with Metadata Integration

## Overview

The `getTableLineageWithColumnMappings` endpoint in `LineageController` has been enhanced to include metadata information for both source and target databases. This integration provides comprehensive database metadata alongside lineage relationships.

## Enhanced Features

### 1. Extended DTO Structure

**TableRelationshipDto** now includes metadata fields:

```kotlin
data class TableRelationshipDto(
    // ... existing fields
    val sourceMetadata: List<MetadataDataSourceDto> = emptyList(),
    val targetMetadata: List<MetadataDataSourceDto> = emptyList()
)
```

### 2. Enhanced API Response

The `/api/lineage/table/{tableId}/lineage-with-columns` endpoint now returns:

```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "tableId": 1,
    "upstream": [
      {
        "sourceTableId": 2,
        "sourceTable": "source_table",
        "sourceDatasource": "mysql_prod",
        "targetTableId": 1,
        "targetTable": "target_table", 
        "targetDatasource": "mysql_warehouse",
        "columnMappings": [...],
        "sourceMetadata": [
          {
            "id": 1,
            "sourceName": "MySQL生产数据库",
            "dbType": "mysql",
            "dbDriver": "com.mysql.cj.jdbc.Driver",
            "dbName": "production_db",
            "dbUrl": "***********",
            "dbPort": 3306,
            "dbUsername": "admin",
            "systemInfo": {
              "systemName": "数据管理平台",
              "systemType": "数据平台",
              "moduleOwner": "张三"
            }
          }
        ],
        "targetMetadata": [...]
      }
    ],
    "downstream": [...]
  }
}
```

## Implementation Details

### 1. Data Flow Enhancement

```
Original Flow:
LineageController → LineageService → LineageRepository → Response

Enhanced Flow:
LineageController → LineageService → LineageRepository
                 ↓ (enhancement step)
                 MetadataService → metadata_data_source + metadata_system_info
                 ↓
                 Enhanced Response with Metadata
```

### 2. Key Components Modified

#### **TableLineageDto.kt**
- Added `sourceMetadata` and `targetMetadata` fields
- Updated transformation functions
- Added `MetadataDataSourceDto` import

#### **LineageController.kt**
- Injected `MetadataService` dependency
- Added metadata enhancement logic
- Implemented error handling for metadata lookup failures

#### **LineageService.kt**
- Added `findLineageDatasourceIdByName()` method
- Bridge between lineage and metadata services

#### **LineageRepository.kt**  
- Added SQL query for datasource name lookup
- Handles active datasource filtering

### 3. Metadata Matching Logic

The system matches lineage datasources with metadata datasources using:

1. **Datasource Name Lookup**: `lineage_datasources.datasource_name` → `lineage_datasources.id`
2. **Metadata Matching**: Uses `MetadataService.findMatchedMetadataDataSources()`
3. **Case-Insensitive DB Type Matching**: Database types are compared case-insensitively
4. **Host/Port/Database Matching**: Additional criteria for precise matching

## Error Handling

The enhancement is designed to be **resilient**:

- **Metadata Lookup Failures**: If metadata cannot be found, returns empty lists
- **Service Unavailability**: Main lineage functionality continues to work
- **Partial Failures**: Individual relationship metadata failures don't affect other relationships
- **Graceful Degradation**: Original lineage data is always returned

## Usage Examples

### Test the Enhanced Endpoint

```bash
# Get table lineage with metadata
curl -X GET "http://localhost:8080/api/lineage/table/1/lineage-with-columns?maxLevels=3"
```

### Expected Benefits

1. **Comprehensive Context**: Users get both lineage relationships and database metadata
2. **System Integration**: Connects lineage data with operational database information
3. **Enhanced Troubleshooting**: Database connection details available alongside lineage
4. **Audit Capabilities**: System ownership and contact information included

## Configuration Requirements

Ensure both databases are accessible:
- **Main Database**: Contains `lineage_datasources`, `lineage_relationships` tables
- **Metadata Database**: Contains `metadata_data_source`, `metadata_system_info` tables

## Performance Considerations

- **Additional Database Calls**: Each unique datasource triggers metadata lookup
- **Caching Potential**: Consider implementing caching for frequently accessed metadata
- **Concurrent Calls**: Metadata service calls are made sequentially per relationship
- **Fallback Strategy**: Metadata failures don't impact main functionality

## Security Notes

- **Password Exclusion**: DB_PASSWORD fields are never included in responses
- **Read-Only Operations**: Only SELECT queries are performed on metadata tables
- **Error Information**: Detailed error messages are logged but not exposed to clients

## Future Enhancements

Potential improvements:
1. **Caching Layer**: Implement Redis cache for metadata lookups
2. **Batch Processing**: Bundle multiple metadata lookups into single calls
3. **Async Processing**: Make metadata enrichment asynchronous
4. **Configuration Toggle**: Allow enabling/disabling metadata enhancement per environment