# Swagger UI Path Prefix Fix for Kubernetes Ingress

## Problem
When deploying Spring Boot applications with SpringDoc OpenAPI (Swagger UI) behind a Kubernetes ingress with path prefix (e.g., `/dgp-lineage-collector`), Swagger UI fails to work properly because:

1. Swagger UI index page returns 404 - cannot find configuration files
2. API calls from Swagger UI return 404 - missing the path prefix in API requests

## Root Cause
The ingress forwards requests to the application after stripping the path prefix, but Swagger UI doesn't know about the original prefix and makes incorrect API calls.

## Solution

### 1. Configure SpringDoc Properties
Update `application-qa.properties` with path prefix configuration:

```properties
# SpringDoc OpenAPI configuration for Kubernetes deployment with path prefix
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.api-docs.path=/v3/api-docs
springdoc.swagger-ui.config-url=/dgp-lineage-collector/v3/api-docs/swagger-config
springdoc.swagger-ui.url=/dgp-lineage-collector/v3/api-docs
```

**Key Points:**
- `config-url` and `url` must include the full path prefix
- This fixes the 404 when loading Swagger UI index page

### 2. Create OpenAPI Configuration Class
Create `src/main/kotlin/com/datayes/config/OpenApiConfig.kt`:

```kotlin
package com.datayes.config

import io.swagger.v3.oas.models.OpenAPI
import io.swagger.v3.oas.models.info.Info
import io.swagger.v3.oas.models.servers.Server
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Profile

@Configuration
class OpenApiConfig {

    @Bean
    @Profile("qa")
    fun openApiForQa(): OpenAPI {
        return OpenAPI()
            .info(
                Info()
                    .title("DGP Lineage Collector API")
                    .description("Data lineage collection and analysis service")
                    .version("1.0.0")
            )
            .servers(
                listOf(
                    Server()
                        .url("https://dgpsit.minshenglife.com/dgp-lineage-collector")
                        .description("Production Server")
                )
            )
    }

    @Bean
    @Profile("!qa")
    fun openApiForLocal(): OpenAPI {
        return OpenAPI()
            .info(
                Info()
                    .title("DGP Lineage Collector API")
                    .description("Data lineage collection and analysis service")
                    .version("1.0.0")
            )
    }
}
```

**Key Points:**
- Defines server URL in OpenAPI specification with full path prefix
- Uses Spring profiles to separate QA and local configurations
- This fixes API calls from Swagger UI (the "Try it out" functionality)

### 3. Activate Profile in Kubernetes
When deploying to Kubernetes, activate the `qa` profile using environment variables or JVM arguments:

```yaml
# In Kubernetes deployment
env:
  - name: SPRING_PROFILES_ACTIVE
    value: "qa"
```

Or via JVM arguments:
```bash
java -Dspring.profiles.active=qa -jar app.jar
```

## Final Result
- Swagger UI accessible at: `https://dgpsit.minshenglife.com/dgp-lineage-collector/swagger-ui.html`
- All API calls from Swagger UI work correctly with proper path prefix
- Local development remains unaffected

## Important Notes
- Never set `spring.profiles.active` in profile-specific properties files (causes boot error)
- The OpenAPI server configuration is critical for API calls to work
- SpringDoc path configuration is critical for Swagger UI to load
- Profile-based configuration ensures local development isn't affected