package com.datayes.dataexchange

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 数据交互平台作业 (Data Exchange Platform Job) 数据模型
 *
 * 表示数据交互平台中的一个数据交换作业，包含数据读取和写入的完整配置信息
 *
 * @property readerJobId 读取作业ID
 * @property readerJobName 读取作业名称
 * @property dbReader 读取数据库连接字符串
 * @property readerTableName 读取表名
 * @property readerSql 读取SQL语句
 * @property writeJobId 写入作业ID
 * @property writeJobName 写入作业名称
 * @property dbWriter 写入数据库连接字符串
 * @property columns 列映射配置列表
 * @property writerTableName 写入表名
 */
data class DataExchangeJob(

    @JsonProperty("reader_job_id")
    val readerJobId: String,

    @JsonProperty("reader_job_name")
    val readerJobName: String,

    @JsonProperty("db_r")
    val dbReader: String,

    @JsonProperty("reader_table_name")
    val readerTableName: String,

    @JsonProperty("reader_sql")
    val readerSql: String,

    @JsonProperty("write_job_id")
    val writeJobId: String,

    @JsonProperty("write_job_name")
    val writeJobName: String,

    @JsonProperty("db_w")
    val dbWriter: String,

    @JsonProperty("columns")
    val columns: List<DataExchangeColumnMapping>,

    @JsonProperty("writer_table_name")
    val writerTableName: String,
)

/**
 * 列映射配置 (Column Mapping Configuration)
 *
 * 定义源列到目标列的映射关系和类型转换信息
 *
 * @property columnIndex 列索引
 * @property columnRemark 列备注说明
 * @property dstColumnName 目标列名
 * @property dstColumnType 目标列类型
 * @property srcColumnName 源列名
 * @property srcColumnType 源列类型
 */
data class DataExchangeColumnMapping(

    @JsonProperty("columnIndex")
    val columnIndex: Int,

    @JsonProperty("columnRemark")
    val columnRemark: String?,

    @JsonProperty("dstColumnName")
    val dstColumnName: String,

    @JsonProperty("dstColumnType")
    val dstColumnType: String,

    @JsonProperty("srcColumnName")
    val srcColumnName: String,

    @JsonProperty("srcColumnType")
    val srcColumnType: String,
)
