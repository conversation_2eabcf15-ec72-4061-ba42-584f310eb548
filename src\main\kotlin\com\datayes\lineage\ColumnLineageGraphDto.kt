package com.datayes.lineage

import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * 列级血缘图数据传输对象 (Column Lineage Graph DTOs)
 * 
 * UC-11: 用于渲染列级血缘图的数据结构，支持LineageType和DataTransformation的可视化
 */

/**
 * 列血缘图 (Column Lineage Graph)
 * 
 * 包含列节点和边信息，用于前端可视化列级血缘关系
 * UC-12 & UC-13: 支持搜索和系统分组功能
 */
data class ColumnLineageGraphDto(
    val nodes: List<ColumnNodeDto>,
    val edges: List<ColumnEdgeDto>,
    val metadata: ColumnGraphMetadataDto,
    val systemGroups: List<SystemGroupDto> = emptyList(), // UC-13: 系统分组信息
    val searchMetadata: SearchMetadataDto? = null // UC-12: 搜索相关元数据
)

/**
 * 列节点DTO (Column Node DTO)
 * 
 * 表示图中的列节点，包含列信息和所属表信息
 * UC-12 & UC-13: 支持搜索匹配和系统分组
 */
data class ColumnNodeDto(
    val id: String,                        // 节点唯一标识，格式: "column_{tableId}_{columnName}"
    val columnName: String,                // 列名
    val dataType: String,                  // 数据类型
    val tableId: Long?,                    // 表ID（可能为空）
    val tableName: String,                 // 表名
    val schema: String?,                   // 模式名
    val datasource: String?,               // 数据源名称
    val nodeType: ColumnNodeType,          // 节点类型
    val confidenceScore: BigDecimal?,      // 置信度分数
    val comment: String?,                  // 列注释
    val isNullable: Boolean?,              // 是否可为空
    val position: ColumnPositionDto,       // 节点在图中的位置信息
    val systemGroupId: String?,            // UC-13: 所属系统分组ID
    val searchScore: Double? = null,       // UC-12: 搜索匹配分数 (0.0-1.0)
    val highlightTerms: List<String> = emptyList() // UC-12: 高亮显示的搜索词
)

/**
 * 列边DTO (Column Edge DTO)
 * 
 * 表示图中列与列之间的血缘关系，包含LineageType和DataTransformation信息
 */
data class ColumnEdgeDto(
    val id: String,                        // 边唯一标识，格式: "edge_{relationshipId}"
    val relationshipId: Long,              // 数据库中的关系ID
    val sourceNodeId: String,              // 源节点ID
    val targetNodeId: String,              // 目标节点ID
    val sourceColumn: String,              // 源列名
    val targetColumn: String,              // 目标列名
    val lineageType: LineageTypeDto,       // 血缘类型信息
    val transformation: TransformationDto?, // 数据转换信息
    val confidenceScore: BigDecimal?,      // 置信度分数
    val sourceType: SourceTypeDto,         // 血缘来源类型（用于样式区分）
    val visualStyle: EdgeVisualStyleDto,   // 可视化样式信息
    val createdAt: LocalDateTime?          // 创建时间
)

/**
 * 列节点类型枚举 (Column Node Type Enum)
 */
enum class ColumnNodeType {
    SOURCE,          // 源列
    TARGET,          // 目标列
    INTERMEDIATE,    // 中间列
    ROOT             // 根列（查询的起始列）
}

/**
 * 血缘类型DTO (Lineage Type DTO)
 * 
 * 包含血缘类型和显示信息
 */
data class LineageTypeDto(
    val type: String,                      // 血缘类型（DIRECT_COPY, SQL_QUERY等）
    val displayName: String,               // 显示名称
    val description: String,               // 描述
    val color: String,                     // 颜色代码
    val style: String                      // 样式类型（solid, dashed, dotted等）
)

/**
 * 数据转换DTO (Data Transformation DTO)
 * 
 * 包含转换类型和详细信息，支持FUNCTION和EXPRESSION的可视化
 */
data class TransformationDto(
    val type: String,                      // 转换类型（FUNCTION, EXPRESSION等）
    val displayName: String,               // 显示名称
    val description: String?,              // 转换描述
    val expression: String?,               // 转换表达式
    val icon: String?,                     // 图标类型
    val complexity: TransformationComplexity // 复杂度级别
)

/**
 * 转换复杂度枚举 (Transformation Complexity Enum)
 */
enum class TransformationComplexity {
    SIMPLE,          // 简单转换（如直接复制）
    MODERATE,        // 中等复杂度（如类型转换）
    COMPLEX          // 复杂转换（如复杂表达式）
}

/**
 * 来源类型DTO (Source Type DTO)
 * 
 * 用于区分SCRIPT_ANALYSIS vs MANUAL_INPUT的样式
 */
data class SourceTypeDto(
    val type: String,                      // 来源类型（SCRIPT_ANALYSIS, MANUAL_INPUT）
    val displayName: String,               // 显示名称
    val reliability: SourceReliability,    // 可靠性级别
    val color: String,                     // 颜色代码
    val pattern: String?                   // 图案类型（实线、虚线等）
)

/**
 * 来源可靠性枚举 (Source Reliability Enum)
 */
enum class SourceReliability {
    HIGH,            // 高可靠性（脚本分析）
    MEDIUM,          // 中等可靠性
    LOW,             // 低可靠性
    MANUAL           // 手动输入
}

/**
 * 边可视化样式DTO (Edge Visual Style DTO)
 * 
 * 综合LineageType、DataTransformation和SourceType的样式信息
 */
data class EdgeVisualStyleDto(
    val strokeColor: String,               // 线条颜色
    val strokeWidth: Int,                  // 线条宽度
    val strokeStyle: String,               // 线条样式（solid, dashed, dotted）
    val arrowType: String,                 // 箭头类型
    val label: String?,                    // 边标签
    val labelPosition: String,             // 标签位置
    val opacity: Double,                   // 透明度
    val zIndex: Int                        // 层级
)

/**
 * 列位置信息DTO (Column Position DTO)
 * 
 * 用于图布局的位置信息
 */
data class ColumnPositionDto(
    val tableGroup: String,                // 表分组标识
    val columnIndex: Int,                  // 列在表中的索引
    val level: Int,                        // 在血缘图中的层级
    val suggested: SuggestedPositionDto?   // 建议的布局位置
)

/**
 * 建议位置DTO (Suggested Position DTO)
 */
data class SuggestedPositionDto(
    val x: Double?,                        // X坐标
    val y: Double?,                        // Y坐标
    val layoutType: String                 // 布局类型（hierarchical, force, circular等）
)

/**
 * 列图元数据DTO (Column Graph Metadata DTO)
 * 
 * 包含列级血缘图的整体信息和统计数据
 */
data class ColumnGraphMetadataDto(
    val totalNodes: Int,                   // 节点总数
    val totalEdges: Int,                   // 边总数
    val tablesInvolved: List<TableSummaryDto>, // 涉及的表
    val transformationTypes: Map<String, Int>, // 转换类型统计
    val sourceTypeDistribution: Map<String, Int>, // 来源类型分布
    val avgConfidenceScore: BigDecimal?,   // 平均置信度
    val queryMetadata: ColumnQueryMetadataDto, // 查询元数据
    val visualMetadata: VisualMetadataDto  // 可视化元数据
)

/**
 * 表摘要DTO (Table Summary DTO)
 */
data class TableSummaryDto(
    val tableId: Long?,
    val tableName: String,
    val schema: String?,
    val datasource: String?,
    val columnCount: Int,                  // 涉及的列数量
    val role: String                       // 在血缘中的角色（source, target, intermediate）
)

/**
 * 列查询元数据DTO (Column Query Metadata DTO)
 */
data class ColumnQueryMetadataDto(
    val rootTableId: Long?,                // 根表ID
    val rootTableName: String?,            // 根表名称
    val queryType: String,                 // 查询类型（by_table, by_column等）
    val maxLevels: Int,                    // 最大层级
    val includeUpstream: Boolean,          // 是否包含上游
    val includeDownstream: Boolean,        // 是否包含下游
    val queryTimestamp: LocalDateTime      // 查询时间戳
)

/**
 * 可视化元数据DTO (Visual Metadata DTO)
 */
data class VisualMetadataDto(
    val recommendedLayout: String,         // 推荐布局类型
    val nodeSpacing: Int,                  // 节点间距
    val levelSpacing: Int,                 // 层级间距
    val canvasSize: CanvasSizeDto,         // 画布尺寸建议
    val colorScheme: String                // 颜色方案
)

/**
 * 画布尺寸DTO (Canvas Size DTO)
 */
data class CanvasSizeDto(
    val width: Int,
    val height: Int,
    val minZoom: Double,
    val maxZoom: Double
)

/**
 * 列血缘查询参数DTO (Column Lineage Query Parameters DTO)
 */
data class ColumnLineageQueryDto(
    val tableId: Long?,                    // 表ID（可选）
    val tableName: String?,                // 表名（可选）
    val columnName: String?,               // 列名（可选）
    val maxLevels: Int = 3,                // 最大层级
    val includeUpstream: Boolean = true,   // 包含上游
    val includeDownstream: Boolean = true, // 包含下游
    val filterByTransformationType: List<String> = emptyList(), // 转换类型过滤
    val filterBySourceType: List<String> = emptyList(), // 来源类型过滤
    val minConfidenceScore: BigDecimal? = null // 最小置信度
)

/**
 * 从 ColumnLineageView 转换为 ColumnNodeDto
 */
fun ColumnLineageView.toColumnNodeDto(nodeType: ColumnNodeType, tableIds: Map<String, Long?> = emptyMap()): Pair<ColumnNodeDto, ColumnNodeDto> {
    val sourceTableId = tableIds["${this.sourceTable}_${this.sourceSchema}"]
    val targetTableId = tableIds["${this.targetTable}_${this.targetSchema}"]

    val sourceNode = ColumnNodeDto(
        id = "column_${sourceTableId ?: 0}_${this.sourceColumn}",
        columnName = this.sourceColumn,
        dataType = this.sourceDataType,
        tableId = sourceTableId,
        tableName = this.sourceTable,
        schema = this.sourceSchema,
        datasource = null, // 需要从额外查询获取
        nodeType = if (nodeType == ColumnNodeType.ROOT) ColumnNodeType.ROOT else ColumnNodeType.SOURCE,
        confidenceScore = this.confidenceScore,
        comment = null, // 需要从额外查询获取
        isNullable = null, // 需要从额外查询获取
        position = ColumnPositionDto(
            tableGroup = "${this.sourceTable}_${this.sourceSchema}",
            columnIndex = 0, // 需要计算
            level = 1,
            suggested = null
        ),
        systemGroupId = generateColumnSystemGroupId(this.sourceTable, null)
    )

    val targetNode = ColumnNodeDto(
        id = "column_${targetTableId ?: 0}_${this.targetColumn}",
        columnName = this.targetColumn,
        dataType = this.targetDataType,
        tableId = targetTableId,
        tableName = this.targetTable,
        schema = this.targetSchema,
        datasource = null, // 需要从额外查询获取
        nodeType = if (nodeType == ColumnNodeType.ROOT) ColumnNodeType.ROOT else ColumnNodeType.TARGET,
        confidenceScore = this.confidenceScore,
        comment = null, // 需要从额外查询获取
        isNullable = null, // 需要从额外查询获取
        position = ColumnPositionDto(
            tableGroup = "${this.targetTable}_${this.targetSchema}",
            columnIndex = 0, // 需要计算
            level = 1,
            suggested = null
        ),
        systemGroupId = generateColumnSystemGroupId(this.targetTable, null)
    )

    return Pair(sourceNode, targetNode)
}

/**
 * 从 ColumnLineageView 转换为 ColumnEdgeDto
 */
fun ColumnLineageView.toColumnEdgeDto(): ColumnEdgeDto {
    val lineageTypeDto = createLineageTypeDto(this.transformationType)
    val transformationDto = createTransformationDto(this.transformationType, this.transformationDescription, this.transformationExpression)
    val sourceTypeDto = createSourceTypeDto(this.lineageSource)
    val visualStyleDto = createVisualStyleDto(transformationDto, sourceTypeDto)

    return ColumnEdgeDto(
        id = "edge_${this.relationshipId}",
        relationshipId = this.relationshipId,
        sourceNodeId = "column_0_${this.sourceColumn}", // 需要实际的tableId
        targetNodeId = "column_0_${this.targetColumn}", // 需要实际的tableId
        sourceColumn = this.sourceColumn,
        targetColumn = this.targetColumn,
        lineageType = lineageTypeDto,
        transformation = transformationDto,
        confidenceScore = this.confidenceScore,
        sourceType = sourceTypeDto,
        visualStyle = visualStyleDto,
        createdAt = this.createdAt
    )
}

/**
 * 创建血缘类型DTO (Create Lineage Type DTO)
 */
private fun createLineageTypeDto(transformationType: String?): LineageTypeDto {
    return when (transformationType?.uppercase()) {
        "NONE" -> LineageTypeDto("DIRECT_COPY", "直接复制", "直接列映射", "#2E7D32", "solid")
        "FUNCTION" -> LineageTypeDto("SQL_QUERY", "函数转换", "通过函数进行转换", "#1976D2", "solid")
        "EXPRESSION" -> LineageTypeDto("SQL_QUERY", "表达式转换", "通过表达式进行转换", "#1976D2", "dashed")
        "AGGREGATION" -> LineageTypeDto("AGGREGATION", "聚合", "数据聚合操作", "#F57C00", "solid")
        "TYPE_CAST" -> LineageTypeDto("DIRECT_COPY", "类型转换", "数据类型转换", "#2E7D32", "dashed")
        else -> LineageTypeDto("COMPLEX_TRANSFORMATION", "复杂转换", "复杂数据转换", "#7B1FA2", "dotted")
    }
}

/**
 * 创建转换DTO (Create Transformation DTO)
 */
private fun createTransformationDto(transformationType: String?, description: String?, expression: String?): TransformationDto? {
    if (transformationType == null) return null

    val complexity = when (transformationType.uppercase()) {
        "NONE" -> TransformationComplexity.SIMPLE
        "TYPE_CAST" -> TransformationComplexity.SIMPLE
        "FUNCTION" -> TransformationComplexity.MODERATE
        "EXPRESSION" -> TransformationComplexity.COMPLEX
        "AGGREGATION" -> TransformationComplexity.COMPLEX
        else -> TransformationComplexity.MODERATE
    }

    val displayName = when (transformationType.uppercase()) {
        "NONE" -> "直接映射"
        "TYPE_CAST" -> "类型转换"
        "FUNCTION" -> "函数计算"
        "EXPRESSION" -> "表达式计算"
        "AGGREGATION" -> "聚合计算"
        "CONSTANT" -> "常量赋值"
        "CONDITIONAL" -> "条件判断"
        else -> transformationType
    }

    val icon = when (transformationType.uppercase()) {
        "NONE" -> "arrow-right"
        "FUNCTION" -> "function"
        "EXPRESSION" -> "code"
        "AGGREGATION" -> "sigma"
        "TYPE_CAST" -> "convert"
        else -> "transform"
    }

    return TransformationDto(
        type = transformationType,
        displayName = displayName,
        description = description,
        expression = expression,
        icon = icon,
        complexity = complexity
    )
}

/**
 * 创建来源类型DTO (Create Source Type DTO)
 */
private fun createSourceTypeDto(lineageSource: String): SourceTypeDto {
    return when (lineageSource.uppercase()) {
        "SCRIPT_ANALYSIS" -> SourceTypeDto(
            type = "SCRIPT_ANALYSIS",
            displayName = "脚本分析",
            reliability = SourceReliability.HIGH,
            color = "#1565C0",
            pattern = "solid"
        )
        "MANUAL_INPUT" -> SourceTypeDto(
            type = "MANUAL_INPUT",
            displayName = "手动输入",
            reliability = SourceReliability.MANUAL,
            color = "#E65100",
            pattern = "dashed"
        )
        else -> SourceTypeDto(
            type = lineageSource,
            displayName = lineageSource,
            reliability = SourceReliability.MEDIUM,
            color = "#424242",
            pattern = "solid"
        )
    }
}

/**
 * 创建可视化样式DTO (Create Visual Style DTO)
 */
private fun createVisualStyleDto(
    transformation: TransformationDto?,
    sourceType: SourceTypeDto
): EdgeVisualStyleDto {
    val strokeWidth = when (transformation?.complexity) {
        TransformationComplexity.SIMPLE -> 2
        TransformationComplexity.MODERATE -> 3
        TransformationComplexity.COMPLEX -> 4
        null -> 2
    }

    val opacity = when (sourceType.reliability) {
        SourceReliability.HIGH -> 1.0
        SourceReliability.MEDIUM -> 0.8
        SourceReliability.LOW -> 0.6
        SourceReliability.MANUAL -> 0.7
    }

    val label = transformation?.let {
        when {
            it.expression != null && it.expression.length <= 20 -> it.expression
            it.description != null && it.description.length <= 15 -> it.description
            else -> it.displayName
        }
    }

    return EdgeVisualStyleDto(
        strokeColor = sourceType.color,
        strokeWidth = strokeWidth,
        strokeStyle = sourceType.pattern ?: "solid",
        arrowType = "triangle",
        label = label,
        labelPosition = "middle",
        opacity = opacity,
        zIndex = if (sourceType.type == "MANUAL_INPUT") 100 else 50
    )
}

/**
 * 从 ColumnLineageView 转换为 TableLineageView 的辅助函数
 * 用于复用现有的表级血缘处理逻辑
 */
fun ColumnLineageView.toTableLineageView(): TableLineageView {
    return TableLineageView(
        relationshipId = this.relationshipId,
        sourceTableId = this.sourceTableId ?: 0L,
        targetTableId = this.targetTableId ?: 0L,
        sourceTable = this.sourceTable,
        sourceSchema = this.sourceSchema,
        sourceChineseName = this.sourceChineseName,
        sourceDatasource = this.sourceDatasource ?: "unknown",
        sourceSystem = this.sourceSystem,
        targetTable = this.targetTable,
        targetSchema = this.targetSchema,
        targetDatasource = this.targetDatasource ?: "unknown",
        lineageType = this.transformationType ?: "COLUMN_LEVEL",
        lineageSource = this.lineageSource,
        confidenceScore = this.confidenceScore,
        createdAt = this.createdAt,
        level = 1 // 默认层级
    )
}

/**
 * 生成列级系统分组ID的辅助函数 (Helper function to generate column system group ID)
 */
private fun generateColumnSystemGroupId(tableName: String, datasource: String?): String {
    return "group_${(tableName + "_" + (datasource ?: "unknown")).hashCode().toString().replace("-", "")}"
}
