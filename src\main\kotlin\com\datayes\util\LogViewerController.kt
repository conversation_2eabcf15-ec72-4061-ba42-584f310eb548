package com.datayes.util

import org.springframework.beans.factory.annotation.Value
import org.springframework.http.MediaType
import org.springframework.stereotype.Controller
import org.springframework.ui.Model
import org.springframework.web.bind.annotation.*

/**
 * Controller for the log viewer UI and API
 */
@Controller
@RequestMapping("/logs")
class LogViewerController {

    @Value("\${app.k8s.path-prefix:}")
    private lateinit var pathPrefix: String

    /**
     * Serve the main log viewer page
     */
    @GetMapping
    fun viewLogs(model: Model): String {
        model.addAttribute("pathPrefix", pathPrefix)
        return "logs"
    }

    /**
     * API endpoint to get all logs
     */
    @GetMapping("/api/all", produces = [MediaType.APPLICATION_JSON_VALUE])
    @ResponseBody
    fun getAllLogs(): List<LogEntry> {
        return InMemoryLogAppender.getLogs()
    }

    /**
     * API endpoint to search logs
     */
    @GetMapping("/api/search", produces = [MediaType.APPLICATION_JSON_VALUE])
    @ResponseBody
    fun searchLogs(@RequestParam(required = false, defaultValue = "") query: String): List<LogEntry> {
        return InMemoryLogAppender.searchLogs(query)
    }

    /**
     * API endpoint to clear logs
     */
    @PostMapping("/api/clear")
    @ResponseBody
    fun clearLogs(): Map<String, String> {
        InMemoryLogAppender.clearLogs()
        return mapOf("status" to "success", "message" to "Logs cleared")
    }

    /**
     * API endpoint to keep only logs from the last N minutes
     * 保留最近 N 分钟的日志端点
     */
    @PostMapping("/api/keep-last-minutes")
    @ResponseBody
    fun keepLastMinutesLogs(@RequestParam minutes: Int): Map<String, Any> {
        return try {
            if (minutes <= 0) {
                mapOf(
                    "status" to "error", 
                    "message" to "Minutes must be greater than 0"
                )
            } else {
                val removedCount = InMemoryLogAppender.keepLastMinutesLogs(minutes)
                mapOf(
                    "status" to "success", 
                    "message" to "Kept logs from last $minutes minutes",
                    "removedCount" to removedCount,
                    "minutes" to minutes
                )
            }
        } catch (e: Exception) {
            mapOf(
                "status" to "error", 
                "message" to "Failed to filter logs: ${e.message}"
            )
        }
    }

    /**
     * HTMX endpoint to get logs table content
     */
    @GetMapping("/htmx/logs-table", produces = [MediaType.TEXT_HTML_VALUE])
    fun getLogsTable(
        @RequestParam(required = false, defaultValue = "") query: String,
        model: Model
    ): String {
        val logs = if (query.isBlank()) {
            InMemoryLogAppender.getLogs()
        } else {
            InMemoryLogAppender.searchLogs(query)
        }
        model.addAttribute("logs", logs)
        return "fragments/logs-table :: logs-table"
    }
}