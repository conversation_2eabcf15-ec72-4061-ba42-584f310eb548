# Complete Data Flow Analysis: `lineage_tasks` Table

## 📊 Overview

The `lineage_tasks` table is the **central orchestration hub** for managing lineage data collection tasks across different systems in the DGP Lineage Collector application.

## 🗄️ Table Structure & Purpose

### Core Schema
- **id** (PK): Auto-increment task identifier
- **job_key**: Unique job identifier (`readerJobId_writeJobId` format)
- **task_type**: `DATA_EXCHANGE_PLATFORM`, `BASH_SCRIPT`, `MANUAL_IMPORT`, `EXCEL_IMPORT`
- **task_status**: `PENDING` → `RUNNING` → `SUCCESS`/`FAILED`/`CANCELLED`
- **execution_count**: Number of task executions
- **has_changes**: Boolean flag indicating data changes
- **batch_id**: Groups related task executions

### Enhanced Fields
- **processing_time_ms**: Execution duration in milliseconds
- **last_execution_id**: UUID of the last execution
- **schedule_type**: <PERSON><PERSON><PERSON>, SCHEDULED, REAL_TIME
- **is_enabled**: Boolean flag for active/inactive tasks
- **source_identifier**: Job ID, script path, or other source identifiers
- **source_content**: Original SQL, script content, etc.

## 🔄 Complete Data Flow Map

```
📥 DATA SOURCES
├── Data Exchange Platform Jobs
├── HDFS Shell Scripts  
├── Manual API Requests
└── Excel Import Operations

        ⬇️ [Service Layer Processing]

📋 TASK MANAGEMENT (lineage_tasks)
├── Task Creation & Status Tracking
├── Execution History & Metrics
├── Batch Processing Coordination
└── Change Detection Logic

        ⬇️ [Processing Results]

📤 DATA CONSUMPTION
├── REST API Endpoints
├── Batch Processing Reports
├── System Monitoring Dashboards
└── Lineage Relationship Storage
```

## 🏗️ Key Classes & Components

### Data Access Layer
- **`LineageTaskRepository`** - CRUD operations and custom queries
- **`LineageTaskCustomRepository`** - Complex filtering and pagination
- **`LineageTask.kt`** - Entity data class with Spring Data JDBC mapping

### Business Logic Layer
- **`LineageTaskService`** - Core task management logic
- **`LineageTaskProcessingService`** - Batch processing orchestration
- **`DataExchangeJobService`** - Integration with data exchange platform

### API Layer
- **`LineageTaskController`** - REST endpoints at `/api/v1/lineage/tasks`
- **`LineageSystemController`** - System-specific operations
- **`HdfsShellScriptController`** - HDFS script processing

## 🔄 CRUD Operations Breakdown

### CREATE (INSERT)
```kotlin
// Automatic task creation during job processing
LineageTaskService.findOrCreateLineageTask()
└── LineageTaskRepository.save() 
    └── INSERT INTO lineage_tasks

// HDFS script processing
HdfsShellScriptService.createNewLineageTask()
```

### READ (SELECT)
```kotlin
// Key query patterns:
├── findByJobKey() - Job-specific lookup
├── findByBatchId() - Batch operation tracking  
├── findByTaskStatusIn() - Status-based filtering
├── findTasksWithCriteria() - Complex paginated queries
└── findActiveTask() - Active task retrieval
```

### UPDATE
```kotlin
// Execution updates
updateTaskExecution() - Status, timing, change detection
updateTaskStatus() - Enable/disable tasks
// Note: No DELETE operations - uses soft deletion via is_enabled flag
```

## 🔄 Processing Workflows

### 1. Batch Processing Flow
```
processAllActiveJobs()
├── Fetch active Data Exchange jobs
├── Create/update LineageTask entries  
├── Execute lineage analysis in parallel
├── Update task status and metrics
└── Generate ExecutionSummary report
```

### 2. Individual Task Flow
```
Single Job Processing
├── Set status to RUNNING
├── Analyze SQL and extract lineage
├── Detect changes in lineage data
├── Update lineage_relationships table
├── Record execution metrics
└── Set final status (SUCCESS/FAILED)
```

### 3. Data Creation/Input Flow
```
1. Data Exchange Platform Jobs → DataExchangeJobService
2. HDFS Shell Scripts → HdfsShellScriptService  
3. Manual/API Requests → LineageTaskController
                          ↓
4. LineageTaskService.findOrCreateLineageTask()
                          ↓
5. LineageTaskRepository.save() → lineage_tasks table
```

### 4. Data Retrieval Flow
```
1. REST API calls → LineageTaskController
                          ↓
2. LineageTaskService.findTasks()
                          ↓
3. LineageTaskCustomRepository.findTasksWithCriteria()
                          ↓
4. Convert to DTOs → Return to client
```

## 🌐 API Endpoints

### Primary Endpoints
- **`POST /api/v1/lineage/tasks/process-all`** - Batch process all active jobs
- **`GET /api/v1/lineage/tasks`** - Query tasks with filtering & pagination  
- **`POST /api/v1/lineage/tasks/{taskId}/rerun`** - Rerun specific task
- **`POST /api/v1/lineage/systems/{systemType}/collect`** - System-specific collection

### LineageTaskController (/api/v1/lineage/tasks)
1. **POST /process-all** - Batch process all active jobs
2. **GET /** - Paginated task query with filtering
3. **POST /{taskId}/rerun** - Rerun specific task
4. **GET /{taskId}** - Get task details (not implemented yet)

### LineageSystemController (/api/v1/lineage/systems)
1. **POST /{systemType}/collect** - System-specific lineage collection
2. **GET /types** - Get supported system types
3. **GET /info** - Query all system information

### HdfsShellScriptController (/api/hdfs)
1. **POST /process** - Process HDFS shell scripts
2. **POST /process-and-save** - Complete HDFS processing with task creation
3. **GET /zip-files** - Query ZIP files in HDFS
4. **GET /health** - HDFS connectivity health check

## 🔍 Key Integration Points

### External Systems
- **Data Exchange Platform** (`dc_data_exc` database)
- **HDFS Cluster** (shell script processing)
- **Git Repositories** (script source management)

### Internal Tables
- **`lineage_relationships`** - Created/updated by tasks via `task_id` FK
- **`lineage_systems`** - System configuration reference
- **Data Exchange tables** - Source job definitions

## 💡 Data Transformation Points

### 1. Input Transformation
- `DataExchangeJob` → `LineageTask` (automatic mapping)
- Shell scripts → `LineageTask` (HDFS processing)
- API requests → `LineageTask` (manual creation)

### 2. Processing Transformation
- Raw SQL → Parsed lineage relationships
- Job execution → Execution metrics & history
- Status changes → Audit trail

### 3. Output Transformation
- `LineageTask` → `LineageTaskDto` (API responses)
- Batch results → `ExecutionSummary` (reporting)
- Task data → Monitoring dashboards

## 🔄 Background Processes

### Automated Task Creation
- Tasks are automatically created when processing data exchange jobs
- HDFS script processing creates corresponding lineage tasks
- Batch processing handles multiple jobs concurrently

### Change Detection
- **LineageChangeDetectionService** detects if lineage data has changed
- Only processes updates when changes are detected
- Maintains processing history for auditing

### Task Lifecycle Management
- Tasks for inactive jobs are automatically disabled
- Execution counts and timing are tracked
- Error states are preserved for debugging

## 📋 SQL Operations Summary

### INSERT Operations
1. **LineageTaskRepository.save()** - Creates new tasks
   - Location: `src/main/kotlin/com/datayes/task/LineageTaskRepository.kt`
   - Called from: LineageTaskService.findOrCreateLineageTask()

2. **HdfsShellScriptService.createNewLineageTask()** - Creates HDFS script tasks
   - Location: `src/main/kotlin/com/datayes/hdfs/HdfsShellScriptService.kt`

### SELECT Operations
1. **LineageTaskRepository.findByJobKey()** - Find task by job key
2. **LineageTaskRepository.findByTaskStatusIn()** - Find tasks by status list
3. **LineageTaskRepository.findByBatchId()** - Find tasks by batch ID
4. **LineageTaskRepository.findActiveTask()** - Find enabled tasks
5. **LineageTaskRepository.findAllByTaskTypeAndIsEnabled()** - Find tasks by type and status
6. **LineageTaskCustomRepository.findTasksWithCriteria()** - Complex paginated queries

### UPDATE Operations
1. **LineageTaskCustomRepository.updateTaskExecution()** - Updates task execution info
   ```sql
   UPDATE lineage_tasks 
   SET task_status = ?, executed_at = ?, completed_at = ?, 
       processing_time_ms = ?, has_changes = ?, error_message = ?, 
       last_execution_id = ?, execution_count = execution_count + 1,
       updated_at = CURRENT_TIMESTAMP
   WHERE id = ?
   ```

2. **LineageTaskCustomRepository.updateTaskStatus()** - Updates task enabled status
   ```sql
   UPDATE lineage_tasks 
   SET is_enabled = ?, task_status = ?, error_message = ?, updated_at = CURRENT_TIMESTAMP
   WHERE id = ?
   ```

### DELETE Operations
No direct DELETE operations found - the system uses soft deletion via `is_enabled` flag.

## 🎯 Key Operations Performed

### Batch Processing
- **processAllActiveJobs()**: Processes all active data exchange jobs
- Automatically creates LineageTask entries for new jobs
- Deactivates tasks for jobs that are no longer active
- Updates execution statistics and processing times

### Task Management
- **Rerun Tasks**: Individual task re-execution via API
- **Status Tracking**: PENDING → RUNNING → SUCCESS/FAILED workflow
- **Change Detection**: Only updates database when lineage data changes
- **Batch Organization**: Groups related executions using batch_id

### System Integration
- **Data Exchange Platform**: Primary source of lineage tasks
- **HDFS Shell Scripts**: Secondary source for script-based lineage
- **Manual Operations**: API-driven task creation and management

## 🏁 Summary

The `lineage_tasks` table serves as the **mission-critical coordination center** for all lineage data collection activities in the DGP system. It provides:

- **Comprehensive Task Orchestration** - Manages task lifecycle from creation to completion
- **Multi-Source Integration** - Handles data from exchange platform, HDFS, and manual sources
- **Robust Execution Tracking** - Maintains detailed execution history and metrics
- **Change Detection & Optimization** - Only processes when actual changes occur
- **Scalable Batch Processing** - Supports concurrent processing of multiple tasks
- **Full API Access** - Complete REST API for monitoring and control
- **Audit Trail Maintenance** - Comprehensive logging and status tracking

The system follows functional core/imperative shell patterns with proper error handling, transaction management, and maintains data consistency across all operations.