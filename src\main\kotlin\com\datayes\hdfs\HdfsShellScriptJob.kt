package com.datayes.hdfs

import java.time.LocalDateTime

/**
 * HDFS Shell Script Job 数据模型
 * 
 * 表示从HDFS ZIP文件中提取的shell脚本作业，包含脚本内容和相关元数据信息
 * 类似于DataExchangeJob，但针对shell脚本处理场景
 * 
 * @property jobId 作业唯一标识符 (基于ZIP文件路径和脚本名称生成)
 * @property jobName 作业名称 (通常为脚本文件名)
 * @property zipFilePath HDFS中ZIP文件的完整路径
 * @property scriptName shell脚本文件名
 * @property scriptContent shell脚本完整内容
 * @property scriptSizeBytes 脚本文件大小(字节)
 * @property extractedAt 脚本提取时间
 * @property lastModified 最后修改时间
 * @property status 作业状态
 */
data class HdfsShellScriptJob(
    val jobId: String,
    val jobName: String,
    val zipFilePath: String,
    val scriptName: String,
    val scriptContent: String,
    val scriptSizeBytes: Int,
    val extractedAt: LocalDateTime = LocalDateTime.now(),
    val lastModified: LocalDateTime = LocalDateTime.now(),
    val status: HdfsJobStatus = HdfsJobStatus.ACTIVE
) {
    companion object {
        /**
         * 从ShellScript和ZIP文件路径创建HdfsShellScriptJob
         */
        fun fromShellScript(zipFilePath: String, shellScript: ShellScript): HdfsShellScriptJob {
            val jobId = generateJobId(zipFilePath, shellScript.name)
            val jobName = shellScript.name.substringBeforeLast(".sh")
            
            return HdfsShellScriptJob(
                jobId = jobId,
                jobName = jobName,
                zipFilePath = zipFilePath,
                scriptName = shellScript.name,
                scriptContent = shellScript.content,
                scriptSizeBytes = shellScript.sizeBytes
            )
        }
        
        /**
         * 生成作业ID
         * 格式: hdfs_sha256(zipFilePath + scriptName)的前16位
         */
        private fun generateJobId(zipFilePath: String, scriptName: String): String {
            val input = "$zipFilePath/$scriptName"
            val hash = java.security.MessageDigest.getInstance("SHA-256")
                .digest(input.toByteArray())
                .joinToString("") { "%02x".format(it) }
            return "hdfs_${hash.substring(0, 16)}"
        }
    }
}

/**
 * HDFS作业状态枚举
 */
enum class HdfsJobStatus {
    ACTIVE,      // 活动状态
    INACTIVE,    // 非活动状态
    ERROR,       // 错误状态
    ARCHIVED     // 已归档
}

/**
 * HDFS Shell Script Processing Result
 * 
 * 表示HDFS shell脚本处理的结果，包含多个脚本作业的处理信息
 * 
 * @property jobs 从HDFS提取的shell脚本作业列表
 * @property totalJobsFound 发现的作业总数
 * @property processingTimestamp 处理时间戳
 * @property hdfsPath 处理的HDFS路径
 * @property errors 处理过程中的错误信息
 * @property warnings 处理过程中的警告信息
 */
data class HdfsShellScriptProcessingResult(
    val jobs: List<HdfsShellScriptJob>,
    val totalJobsFound: Int = jobs.size,
    val processingTimestamp: LocalDateTime = LocalDateTime.now(),
    val hdfsPath: String,
    val errors: List<String> = emptyList(),
    val warnings: List<String> = emptyList()
)

/**
 * HDFS Shell Script Lineage Processing Configuration
 * 
 * HDFS shell脚本血缘处理的配置信息
 * 
 * @property hdfsBasePath HDFS基础路径
 * @property includePatterns 包含的文件模式列表
 * @property excludePatterns 排除的文件模式列表
 * @property enableBackupFiltering 是否启用备份文件过滤
 * @property maxScriptSizeBytes 脚本文件最大大小限制
 */
data class HdfsProcessingConfig(
    val hdfsBasePath: String = "/share_ftp",
    val includePatterns: List<String> = listOf("*.zip"),
    val excludePatterns: List<String> = listOf("*backup*", "*temp*"),
    val enableBackupFiltering: Boolean = true,
    val maxScriptSizeBytes: Int = 10 * 1024 * 1024 // 10MB
)