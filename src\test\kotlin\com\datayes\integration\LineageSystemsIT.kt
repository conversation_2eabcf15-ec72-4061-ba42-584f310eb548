package com.datayes.integration

import io.restassured.RestAssured.given
import io.restassured.path.json.JsonPath
import org.assertj.core.api.Assertions.assertThat
import org.hamcrest.Matchers.*
import org.hamcrest.collection.IsCollectionWithSize.hasSize
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test

/**
 * 系统信息 REST API 集成测试 (System Info REST API Integration Test)
 *
 * 测试 /api/lineage/systems 端点的功能。
 *
 * 前提条件:
 * - 应用程序必须已经启动并运行在配置的端口上
 * - 数据库中应该存在 lineage_systems 表和测试数据
 * - 测试遵循只读操作原则，不修改数据库数据
 */
@DisplayName("系统信息 REST API 集成测试")
class LineageSystemsIT : RestApiIntegrationTestBase() {

    @Test
    @DisplayName("应该成功查询所有系统信息")
    fun `should successfully query all systems`() {
        val response = given()
            .`when`()
            .get("/lineage/systems")
            .then()
            .statusCode(200)
            .contentType("application/json")
            .body("success", equalTo(true))
            .body("message", equalTo("查询成功"))
            .body("data", notNullValue())
            .body("data", hasSize<Any>(greaterThan(0)))
            .extract()
            .response()

        println("c7b8e9f2 | 系统查询响应: ${response.asString()}")
    }

    @Test
    @DisplayName("应该返回正确的系统数据结构")
    fun `should return correct system data structure`() {
        val response = given()
            .`when`()
            .get("/lineage/systems")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data[0].id", notNullValue())
            .body("data[0].systemName", notNullValue())
            .body("data[0].systemCode", notNullValue())
            .body("data[0].status", notNullValue())
            .body("data[0].createdAt", notNullValue())
            .body("data[0].updatedAt", notNullValue())
            // scheduleTime 可以为 null，所以不验证 notNullValue()
            .extract()
            .response()

        // 使用 JsonPath 进行更详细的验证
        val jsonPath = JsonPath.from(response.asString())
        val systems = jsonPath.getList<Map<String, Any>>("data")

        assertThat(systems).isNotEmpty()

        val firstSystem = systems[0]
        assertThat(firstSystem).containsKeys(
            "id", "systemName", "systemCode", "status",
            "createdAt", "updatedAt"
        )

        // 验证数据类型
        assertThat(firstSystem["id"]).isInstanceOf(Number::class.java)
        assertThat(firstSystem["systemName"]).isInstanceOf(String::class.java)
        assertThat(firstSystem["systemCode"]).isInstanceOf(String::class.java)
        assertThat(firstSystem["status"]).isInstanceOf(String::class.java)

        // 验证状态值是否为有效的枚举值
        val status = firstSystem["status"] as String
        assertThat(status).isIn("ACTIVE", "INACTIVE")

        println("5d4a7b1c | 验证系统数据结构完成: 共${systems.size}个系统")
    }

    @Test
    @DisplayName("应该按系统名称排序返回结果")
    fun `should return results ordered by system name`() {
        val response = given()
            .`when`()
            .get("/lineage/systems")
            .then()
            .statusCode(200)
            .extract()
            .response()

        val jsonPath = JsonPath.from(response.asString())
        val systemNames = jsonPath.getList<String>("data.systemName")

        // 验证结果是否按系统名称排序
        val sortedNames = systemNames.sorted()
        assertThat(systemNames).isEqualTo(sortedNames)

        println("e8f2a6d9 | 系统名称排序验证完成: ${systemNames.joinToString(", ")}")
    }

    @Test
    @DisplayName("应该包含预期的默认系统")
    fun `should contain expected default systems`() {
        val response = given()
            .`when`()
            .get("/lineage/systems")
            .then()
            .statusCode(200)
            .extract()
            .response()

        val jsonPath = JsonPath.from(response.asString())
        val systemCodes = jsonPath.getList<String>("data.systemCode")

        // 验证是否包含预期的默认系统代码
        val expectedSystemCodes = listOf(
            "DATA_EXCHANGE_PLATFORM",
            "BIG_DATA_PLATFORM",
            "EXTERNAL_SYSTEM",
            "UNCATEGORIZED"
        )

        expectedSystemCodes.forEach { expectedCode ->
            assertThat(systemCodes).contains(expectedCode)
        }

        println("b3c9d7e4 | 默认系统验证完成: 找到预期系统代码 ${expectedSystemCodes.size}/${systemCodes.size}")
    }

    @Test
    @DisplayName("应该正确处理 scheduleTime 字段(Cron表达式)")
    fun `should handle scheduleTime field correctly as cron expression`() {
        val response = given()
            .`when`()
            .get("/lineage/systems")
            .then()
            .statusCode(200)
            .extract()
            .response()

        val jsonPath = JsonPath.from(response.asString())
        val systems = jsonPath.getList<Map<String, Any>>("data")

        // 验证每个系统都有 scheduleTime 字段（即使值为 null）
        systems.forEach { system ->
            assertThat(system).containsKey("scheduleTime")
            // scheduleTime 可以是 null 或有效的 cron 表达式字符串
            val scheduleTime = system["scheduleTime"]
            if (scheduleTime != null) {
                assertThat(scheduleTime).isInstanceOf(String::class.java)
                // 如果不为 null，应该是有效的 cron 表达式格式
                val cronExpression = scheduleTime as String
                // 基本的 cron 表达式格式验证 (5或6个字段，空格分隔)
                assertThat(cronExpression).matches("^[\\s\\S]*${'$'}") // 允许任何字符串格式，具体验证可以后续增强
            }
        }

        println("f1e5a8c2 | scheduleTime 字段验证完成")
    }
}