# DGP Lineage Collector - Core Domain Concepts

## 1. Core Lineage Domain

### Primary Models
- **DataLineage** - Complete lineage from source to target with table/column mappings
- **TableLineage** - Table-level data flow relationships
- **ColumnLineage** - Column-level mappings with transformations
- **TableInfo** - Table metadata (schema, name, database)
- **ColumnInfo** - Column metadata (name, type, comments)
- **DatabaseInfo** - Database connection info from JDBC URLs
- **DataTransformation** - Transformation logic between columns

### Processing Results
- **LineageResult** - Lineage construction result with warnings/errors
- **JobProcessingHistory** - Historical job processing with change detection
- **TableLineageView** - Read-only table lineage with confidence scores
- **ColumnLineageView** - Read-only column lineage with transformations
- **SystemStatistics** - System-level statistics (counts, metrics)

### Enumerations
- **LineageType** - DIRECT_COPY, SQL_QUERY, AGGREGATION, JOIN, FILTER
- **TransformationType** - NONE, TYPE_CAST, FUNCTION, EXPRESSION
- **JobType** - DATA_EXCHANGE, HDFS_SHELL_SCRIPT
- **ProcessingResult** - NO_CHANGE, UPDATED, FAILED

## 2. Data Exchange Integration

### Models
- **DataExchangeJob** - Complete job config with reader/writer settings
- **DataExchangeColumnMapping** - Column mapping with type conversion

## 3. Task Management

### Core Task Models
- **LineageTask** - Main task entity with scheduling and execution tracking
- **CreateLineageTaskRequest** - New task creation request
- **UpdateLineageTaskRequest** - Task update request
- **SystemLineageCollectionRequest** - System-wide collection request
- **SystemLineageCollectionResponse** - Collection results and statistics

### Processing Results
- **TaskProcessResult** - Individual task outcome with timing
- **ExecutionSummary** - Batch processing statistics
- **FailedTaskInfo** - Failed task details with errors
- **BatchProcessResult** - Batch operation results
- **TaskExecutionResult** - Individual execution outcome

### Configuration
- **CollectionOptions** - Collection settings (concurrency, skip unchanged)

### DTOs
- **TableLineageDto** - Complete table lineage with relationships
- **TableRelationshipDto** - Individual table relationship
- **ColumnMappingDto** - Column mapping with confidence scores

### Enumerations
- **TaskType** - DATA_EXCHANGE_PLATFORM, BASH_SCRIPT, MANUAL_IMPORT
- **SourceType** - DATA_EXCHANGE_JOB, SCRIPT_ANALYSIS, MANUAL_INPUT
- **TaskStatus** - PENDING, RUNNING, SUCCESS, FAILED, CANCELLED, DISABLED
- **ScheduleType** - MANUAL, SCHEDULED, REAL_TIME

## 4. SQL Parsing

### Parse Results
- **ParseResult** - SQL parsing results with extracted tables/columns
- **TableReference** - Table reference with schema, name, alias
- **ColumnReference** - Column reference with prefix, alias, wildcards
- **DataModificationResult** - INSERT/UPDATE/DELETE results with mappings
- **ColumnMapping** - Source-target column mappings

### Exceptions
- **SqlParsingException** - SQL parsing errors

## 5. HDFS & Shell Script Processing

### Models
- **HdfsShellScriptJob** - Shell script job from HDFS ZIP files
- **HdfsShellScriptProcessingResult** - HDFS script processing results
- **HdfsProcessingConfig** - HDFS processing configuration
- **ShellScript** - Individual script with content and metadata
- **ZipFileProcessingResult** - Individual ZIP file results
- **ZipProcessingResult** - Overall ZIP processing results

### Enumerations
- **HdfsJobStatus** - ACTIVE, INACTIVE, ERROR, ARCHIVED

## 6. System Management

### Models
- **LineageSystem** - System registry for lineage sources
- **SystemStatus** - ACTIVE, INACTIVE

## 7. Metadata Integration

### Models
- **MetadataDataSourceDto** - External metadata system data sources
- **MetadataSystemInfoDto** - System information from metadata systems
- **MatchedMetadataResponse** - Matched metadata sources response

## 8. Utilities & Support

### Exceptions
- **LineageTaskProcessingException** - Task processing errors
- **LineageTaskNotFoundException** - Task not found errors

### Utilities
- **CronExpressionUtil** - Cron expression parsing/validation
- **GitUtils** - Git repository operations
- **HdfsUtils** - HDFS file system operations
- **LineageHashCalculator** - Hash-based change detection

## Key Domain Characteristics

- **Data-Oriented Design** - Immutable Kotlin data classes throughout
- **Dual Processing Modes** - Data exchange platform + shell script analysis
- **Change Detection** - Hash-based incremental updates
- **Multi-Level Lineage** - Table and column level tracking
- **Batch Processing** - Comprehensive batch operations with statistics
- **Metadata Integration** - External metadata system enrichment
- **Multi-Database Support** - MySQL, Oracle, Hive2, etc.
- **HDFS Integration** - Shell script processing from HDFS
- **Task Scheduling** - Manual, scheduled, and real-time execution
- **Confidence Scoring** - Reliability indicators for lineage relationships