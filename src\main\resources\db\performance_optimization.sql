-- ====================================================================
-- 数据库性能优化脚本 (Database Performance Optimization)
-- ====================================================================

-- 1. 额外的索引优化 (Additional Index Optimization)
-- 根据实际查询模式添加复合索引

-- 血缘查询优化索引
CREATE INDEX idx_lineage_upstream_query ON lineage_relationships 
    (target_table_id, relationship_type, is_active, source_table_id);

CREATE INDEX idx_lineage_downstream_query ON lineage_relationships 
    (source_table_id, relationship_type, is_active, target_table_id);

-- 多条件搜索优化索引
CREATE INDEX idx_table_search ON lineage_tables 
    (table_name, chinese_name, status, datasource_id);

CREATE INDEX idx_column_search ON lineage_columns 
    (column_name, table_id, status);

-- 系统统计查询优化索引
CREATE INDEX idx_system_statistics ON lineage_relationships 
    (created_at, relationship_type, is_active);

-- 任务执行历史查询优化索引  
CREATE INDEX idx_task_execution_history ON lineage_tasks 
    (executed_at, task_status, task_type);

-- 2. 分区策略建议 (Partitioning Strategy Recommendations)
-- 注意：以下为分区策略建议，需要根据数据量和访问模式决定是否实施

-- 按月分区血缘关系表（适用于大数据量场景）
/*
ALTER TABLE lineage_relationships PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    PARTITION p202403 VALUES LESS THAN (202404),
    -- 继续添加更多分区...
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
*/

-- 按日期分区执行日志表
/*
ALTER TABLE lineage_execution_logs PARTITION BY RANGE (TO_DAYS(created_at)) (
    PARTITION p_old VALUES LESS THAN (TO_DAYS('2024-01-01')),
    PARTITION p_current VALUES LESS THAN (TO_DAYS('2024-12-31')),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
*/

-- 3. 视图创建 (Create Views)
-- 为常用查询创建视图以简化业务层代码

-- 表血缘关系视图
CREATE VIEW v_table_lineage AS
SELECT 
    lr.id as relationship_id,
    st.id as source_table_id,
    st.table_name as source_table,
    st.schema_name as source_schema,
    st.chinese_name as source_chinese_name,
    sds.datasource_name as source_datasource,
    ssys.system_name as source_system,
    tt.id as target_table_id,
    tt.table_name as target_table,
    tt.schema_name as target_schema,
    tt.chinese_name as target_chinese_name,
    tds.datasource_name as target_datasource,
    tsys.system_name as target_system,
    lr.lineage_type,
    lr.source_system as lineage_source,
    lr.confidence_score,
    lr.created_at,
    lr.updated_at
FROM lineage_relationships lr
JOIN lineage_tables st ON lr.source_table_id = st.id
JOIN lineage_tables tt ON lr.target_table_id = tt.id
JOIN lineage_datasources sds ON st.datasource_id = sds.id
JOIN lineage_datasources tds ON tt.datasource_id = tds.id
LEFT JOIN lineage_systems ssys ON sds.system_id = ssys.id
LEFT JOIN lineage_systems tsys ON tds.system_id = tsys.id
WHERE lr.relationship_type = 'TABLE_LEVEL' 
  AND lr.is_active = true
  AND st.status = 'ACTIVE' 
  AND tt.status = 'ACTIVE';

-- 列血缘关系视图
CREATE VIEW v_column_lineage AS
SELECT 
    lr.id as relationship_id,
    sc.id as source_column_id,
    sc.column_name as source_column,
    sc.data_type as source_data_type,
    st.table_name as source_table,
    st.schema_name as source_schema,
    tc.id as target_column_id,
    tc.column_name as target_column,
    tc.data_type as target_data_type,
    tt.table_name as target_table,
    tt.schema_name as target_schema,
    lr.transformation_type,
    lr.transformation_description,
    lr.transformation_expression,
    lr.confidence_score,
    lr.source_system as lineage_source,
    lr.created_at
FROM lineage_relationships lr
JOIN lineage_columns sc ON lr.source_column_id = sc.id
JOIN lineage_columns tc ON lr.target_column_id = tc.id
JOIN lineage_tables st ON sc.table_id = st.id
JOIN lineage_tables tt ON tc.table_id = tt.id
WHERE lr.relationship_type = 'COLUMN_LEVEL' 
  AND lr.is_active = true
  AND sc.status = 'ACTIVE' 
  AND tc.status = 'ACTIVE';

-- 系统概览视图
CREATE VIEW v_system_overview AS
SELECT 
    sys.id as system_id,
    sys.system_name,
    sys.system_code,
    sys.description,
    COUNT(DISTINCT ds.id) as datasource_count,
    COUNT(DISTINCT t.id) as table_count,
    COUNT(DISTINCT c.id) as column_count,
    COUNT(DISTINCT CASE WHEN lr.relationship_type = 'TABLE_LEVEL' THEN lr.id END) as table_lineage_count,
    COUNT(DISTINCT CASE WHEN lr.relationship_type = 'COLUMN_LEVEL' THEN lr.id END) as column_lineage_count,
    MAX(lr.created_at) as last_lineage_update,
    sys.status
FROM lineage_systems sys
LEFT JOIN lineage_datasources ds ON sys.id = ds.system_id AND ds.status = 'ACTIVE'
LEFT JOIN lineage_tables t ON ds.id = t.datasource_id AND t.status = 'ACTIVE'
LEFT JOIN lineage_columns c ON t.id = c.table_id AND c.status = 'ACTIVE'
LEFT JOIN lineage_relationships lr ON (t.id = lr.source_table_id OR t.id = lr.target_table_id) AND lr.is_active = true
WHERE sys.status = 'ACTIVE'
GROUP BY sys.id, sys.system_name, sys.system_code, sys.description, sys.status;

-- 4. 存储过程 (Stored Procedures)
-- 用于复杂的血缘查询逻辑

DELIMITER //

-- 获取表的完整血缘链（上游和下游）
CREATE PROCEDURE GetTableCompleteLineage(
    IN p_table_name VARCHAR(100),
    IN p_schema_name VARCHAR(100),
    IN p_datasource_name VARCHAR(100),
    IN p_max_levels INT DEFAULT 3
)
BEGIN
    DECLARE v_table_id BIGINT;
    
    -- 获取表ID
    SELECT t.id INTO v_table_id
    FROM lineage_tables t
    JOIN lineage_datasources ds ON t.datasource_id = ds.id
    WHERE t.table_name = p_table_name 
      AND (p_schema_name IS NULL OR t.schema_name = p_schema_name)
      AND ds.datasource_name = p_datasource_name
      AND t.status = 'ACTIVE'
    LIMIT 1;
    
    IF v_table_id IS NULL THEN
        SELECT 'Table not found' as error_message;
    ELSE
        -- 返回上游血缘
        WITH RECURSIVE upstream_lineage AS (
            SELECT 
                lr.source_table_id,
                lr.target_table_id,
                st.table_name as source_table,
                tt.table_name as target_table,
                1 as level,
                'UPSTREAM' as direction
            FROM lineage_relationships lr
            JOIN lineage_tables st ON lr.source_table_id = st.id
            JOIN lineage_tables tt ON lr.target_table_id = tt.id
            WHERE lr.target_table_id = v_table_id
              AND lr.relationship_type = 'TABLE_LEVEL'
              AND lr.is_active = true
            
            UNION ALL
            
            SELECT 
                lr.source_table_id,
                lr.target_table_id,
                st.table_name,
                tt.table_name,
                ul.level + 1,
                'UPSTREAM'
            FROM lineage_relationships lr
            JOIN lineage_tables st ON lr.source_table_id = st.id
            JOIN lineage_tables tt ON lr.target_table_id = tt.id
            JOIN upstream_lineage ul ON ul.source_table_id = lr.target_table_id
            WHERE ul.level < p_max_levels
              AND lr.relationship_type = 'TABLE_LEVEL'
              AND lr.is_active = true
        ),
        downstream_lineage AS (
            SELECT 
                lr.source_table_id,
                lr.target_table_id,
                st.table_name as source_table,
                tt.table_name as target_table,
                1 as level,
                'DOWNSTREAM' as direction
            FROM lineage_relationships lr
            JOIN lineage_tables st ON lr.source_table_id = st.id
            JOIN lineage_tables tt ON lr.target_table_id = tt.id
            WHERE lr.source_table_id = v_table_id
              AND lr.relationship_type = 'TABLE_LEVEL'
              AND lr.is_active = true
            
            UNION ALL
            
            SELECT 
                lr.source_table_id,
                lr.target_table_id,
                st.table_name,
                tt.table_name,
                dl.level + 1,
                'DOWNSTREAM'
            FROM lineage_relationships lr
            JOIN lineage_tables st ON lr.source_table_id = st.id
            JOIN lineage_tables tt ON lr.target_table_id = tt.id
            JOIN downstream_lineage dl ON dl.target_table_id = lr.source_table_id
            WHERE dl.level < p_max_levels
              AND lr.relationship_type = 'TABLE_LEVEL'
              AND lr.is_active = true
        )
        SELECT * FROM upstream_lineage
        UNION ALL
        SELECT * FROM downstream_lineage
        ORDER BY direction, level, source_table;
    END IF;
END //

DELIMITER ;

-- 5. 数据清理和维护脚本 (Data Cleanup and Maintenance)

-- 清理过期的执行日志（保留最近30天）
CREATE EVENT IF NOT EXISTS cleanup_old_logs
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
DELETE FROM lineage_execution_logs 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 更新统计信息表
CREATE EVENT IF NOT EXISTS update_statistics
ON SCHEDULE EVERY 1 HOUR
STARTS CURRENT_TIMESTAMP
DO
INSERT INTO lineage_statistics (
    system_id, 
    datasource_id, 
    table_count, 
    column_count, 
    relationship_count, 
    last_collection_time,
    health_status,
    statistics_date
)
SELECT 
    ds.system_id,
    ds.id as datasource_id,
    COUNT(DISTINCT t.id) as table_count,
    COUNT(DISTINCT c.id) as column_count,
    COUNT(DISTINCT lr.id) as relationship_count,
    MAX(lr.created_at) as last_collection_time,
    CASE 
        WHEN COUNT(DISTINCT lr.id) > 0 THEN 'HEALTHY'
        WHEN COUNT(DISTINCT t.id) > 0 THEN 'WARNING'
        ELSE 'ERROR'
    END as health_status,
    CURDATE() as statistics_date
FROM lineage_datasources ds
LEFT JOIN lineage_tables t ON ds.id = t.datasource_id AND t.status = 'ACTIVE'
LEFT JOIN lineage_columns c ON t.id = c.table_id AND c.status = 'ACTIVE'
LEFT JOIN lineage_relationships lr ON (t.id = lr.source_table_id OR t.id = lr.target_table_id) 
    AND lr.is_active = true
WHERE ds.status = 'ACTIVE'
GROUP BY ds.system_id, ds.id
ON DUPLICATE KEY UPDATE
    table_count = VALUES(table_count),
    column_count = VALUES(column_count),
    relationship_count = VALUES(relationship_count),
    last_collection_time = VALUES(last_collection_time),
    health_status = VALUES(health_status),
    updated_at = CURRENT_TIMESTAMP;