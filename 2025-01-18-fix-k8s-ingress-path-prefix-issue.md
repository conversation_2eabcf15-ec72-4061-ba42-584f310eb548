# Fix Kubernetes Ingress Path Prefix Issue

## 问题描述 (Problem Description)

在 Kubernetes 环境中部署应用时，使用 Ingress 配置了路径前缀 `/dgp-lineage-collector`，但日志查看器页面 (`/logs`) 中的 HTMX 请求路径没有包含这个前缀，导致请求失败。

具体表现：
- 访问页面：`https://dgpsit.minshenglife.com/dgp-lineage-collector/logs`
- HTMX 请求：`https://dgpsit.minshenglife.com/logs/htmx/logs-table` ❌
- 期望请求：`https://dgpsit.minshenglife.com/dgp-lineage-collector/logs/htmx/logs-table` ✅

## 解决方案 (Solution)

**重要说明**: 不能使用 `server.servlet.context-path` 配置，因为这会影响所有 API 端点，破坏其他 API 调用。

### 1. 配置文件修改 (Configuration Changes)

在 `application-qa.properties` 中添加自定义路径前缀配置：

```properties
# Custom path prefix for HTML template URL generation in K8s deployment
app.k8s.path-prefix=/dgp-lineage-collector
```

### 2. Controller 修改 (Controller Changes)

修改 `LogViewerController` 注入路径前缀配置并传递给模板：

```kotlin
@Value("\${app.k8s.path-prefix:}")
private lateinit var pathPrefix: String

@GetMapping
fun viewLogs(model: Model): String {
    model.addAttribute("pathPrefix", pathPrefix)
    return "logs"
}
```

### 3. HTML 模板修改 (Template Changes)

将 `logs.html` 模板中的硬编码路径替换为使用路径前缀变量：

**修改前 (Before):**
```html
hx-get="/logs/htmx/logs-table"
hx-post="/logs/api/clear"
```

**修改后 (After):**
```html
th:hx-get="${pathPrefix + '/logs/htmx/logs-table'}"
th:hx-post="${pathPrefix + '/logs/api/clear'}"
```

## 技术实现 (Technical Implementation)

### 路径前缀动态处理 (Dynamic Path Prefix Handling)

使用 Spring 配置属性和 Thymeleaf 模板变量：
- 本地环境 (default profile)：`pathPrefix` 为空字符串，生成 `/logs/htmx/logs-table`
- QA 环境 (qa profile)：`pathPrefix` 为 `/dgp-lineage-collector`，生成 `/dgp-lineage-collector/logs/htmx/logs-table`

### 影响的文件 (Modified Files)

1. `src/main/resources/application-qa.properties` - 添加自定义路径前缀配置
2. `src/main/kotlin/com/datayes/util/LogViewerController.kt` - 注入并传递路径前缀变量
3. `src/main/resources/templates/logs.html` - 更新 HTMX 属性使用路径前缀变量

## 环境兼容性 (Environment Compatibility)

- ✅ **本地开发环境** (Local Development): 使用 default profile，`pathPrefix` 为空，API 路径正常
- ✅ **QA/K8s 环境** (QA/K8s Environment): 使用 qa profile，`pathPrefix` 为 `/dgp-lineage-collector`，HTML 请求路径包含前缀
- ✅ **API 端点兼容性** (API Endpoint Compatibility): 所有 REST API 端点不受影响，保持原有路径

## 测试验证 (Testing)

### 本地测试 (Local Testing)
```bash
mvn spring-boot:run
```
访问：`http://localhost:9503/logs`
HTMX 请求：`/logs/htmx/logs-table`

### QA 环境测试 (QA Environment Testing)
```bash
mvn spring-boot:run -Dspring.profiles.active=qa
```
访问：`http://localhost:9503/logs`
HTMX 请求：`/dgp-lineage-collector/logs/htmx/logs-table`

## 受益 (Benefits)

1. **路径一致性** (Path Consistency): 确保所有 AJAX/HTMX 请求使用正确的路径前缀
2. **环境适配** (Environment Adaptation): 自动适应不同部署环境的路径配置
3. **维护性** (Maintainability): 使用声明式配置，避免硬编码路径
4. **向后兼容** (Backward Compatibility): 不影响本地开发工作流

## 相关文档 (Related Documentation)

- [Spring Boot Context Path Documentation](https://docs.spring.io/spring-boot/docs/current/reference/html/web.html#web.servlet.context-path)
- [Thymeleaf URL Expressions](https://www.thymeleaf.org/doc/tutorials/3.0/usingthymeleaf.html#link-urls)
- [HTMX Documentation](https://htmx.org/docs/) 