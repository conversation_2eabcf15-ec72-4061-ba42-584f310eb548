package com.datayes.lineage

import com.datayes.task.ColumnMappingDto
import com.datayes.task.TableLineageDto
import com.datayes.task.TableRelationshipDto
import com.datayes.task.toColumnMappingDto
import com.datayes.task.toTableRelationshipDtos
import com.datayes.metadata.MetadataDataSourceDto
import com.datayes.scheduler.SystemScheduleInfo
import org.springframework.stereotype.Service

/**
 * 血缘关系服务层 (Lineage Service Layer)
 *
 * 处理血缘关系的业务逻辑
 */
@Service
class LineageService(private val lineageRepository: LineageRepository) {

    /**
     * 根据表ID查询上游血缘关系
     *
     * @param tableId 表ID
     * @param maxLevels 最大查询层级
     * @return 表血缘视图列表
     */
    fun findUpstreamLineageByTableId(tableId: Long, maxLevels: Int = 3): List<TableLineageView> {
        return lineageRepository.findUpstreamLineageByTableId(tableId, maxLevels)
    }
    
    /**
     * 根据表ID查询下游血缘关系
     *
     * @param tableId 表ID
     * @param maxLevels 最大查询层级
     * @return 表血缘视图列表
     */
    fun findDownstreamLineageByTableId(tableId: Long, maxLevels: Int = 3): List<TableLineageView> {
        return lineageRepository.findDownstreamLineageByTableId(tableId, maxLevels)
    }
    
    /**
     * 根据表ID查询上下游血缘关系及列映射
     *
     * @param tableId 表ID
     * @param maxLevels 最大查询层级
     * @return 表血缘DTO，包含上下游关系和列映射
     */
    fun findTableLineageWithColumnMappings(tableId: Long, maxLevels: Int = 3): TableLineageDto {
        // 查询上游和下游表级血缘关系
        val upstreamLineage = lineageRepository.findUpstreamLineageByTableId(tableId, maxLevels)
        val downstreamLineage = lineageRepository.findDownstreamLineageByTableId(tableId, maxLevels)
        
        // 查询列级血缘关系
        val columnLineages = lineageRepository.findColumnLineageByTableId(tableId)
        
        // 转换为DTO
        val upstreamRelationships = upstreamLineage.toTableRelationshipDtos()
        val downstreamRelationships = downstreamLineage.toTableRelationshipDtos()
        
        // 为每个表关系添加列映射
        val upstreamWithColumnMappings = addColumnMappingsToRelationships(upstreamRelationships, columnLineages, true)
        val downstreamWithColumnMappings = addColumnMappingsToRelationships(downstreamRelationships, columnLineages, false)
        
        return TableLineageDto(
            tableId = tableId,
            upstream = upstreamWithColumnMappings,
            downstream = downstreamWithColumnMappings
        )
    }
    
    /**
     * 为表关系添加列映射
     *
     * @param relationships 表关系列表
     * @param columnLineages 列级血缘关系
     * @param isUpstream 是否为上游关系
     * @return 添加了列映射的表关系列表
     */
    private fun addColumnMappingsToRelationships(
        relationships: List<TableRelationshipDto>, 
        columnLineages: List<ColumnLineageView>,
        isUpstream: Boolean
    ): List<TableRelationshipDto> {
        return relationships.map { relationship ->
            val columnMappings = columnLineages.filter { columnLineage ->
                if (isUpstream) {
                    // 上游关系：源表是上游，目标表是当前表
                    columnLineage.sourceTable == relationship.sourceTable && 
                    columnLineage.sourceSchema == relationship.sourceSchema &&
                    columnLineage.targetTable == relationship.targetTable &&
                    columnLineage.targetSchema == relationship.targetSchema
                } else {
                    // 下游关系：源表是当前表，目标表是下游
                    columnLineage.sourceTable == relationship.sourceTable &&
                    columnLineage.sourceSchema == relationship.sourceSchema &&
                    columnLineage.targetTable == relationship.targetTable &&
                    columnLineage.targetSchema == relationship.targetSchema
                }
            }.map { it.toColumnMappingDto() }
            
            relationship.copy(columnMappings = columnMappings)
        }
    }
    
    /**
     * 查询所有系统信息 (Query all system information with filtering)
     *
     * @param systemName 系统名称模糊搜索 (optional)
     * @param systemStatus 系统状态过滤 (optional)
     * @return 系统信息列表
     */
    fun findAllSystems(systemName: String? = null, systemStatus: String? = null): List<SystemInfo> {
        return lineageRepository.findAllSystems(systemName, systemStatus)
    }
    
    /**
     * 根据数据源名称查找血缘数据源ID (Find lineage datasource ID by name)
     *
     * @param datasourceName 数据源名称
     * @return 血缘数据源ID，如果未找到则返回null
     */
    fun findLineageDatasourceIdByName(datasourceName: String): Long? {
        return lineageRepository.findLineageDatasourceIdByName(datasourceName)
    }

    /**
     * 根据数据源名称查找血缘数据源完整信息 (Find lineage datasource info by name)
     *
     * @param datasourceName 数据源名称
     * @return 血缘数据源DTO，如果未找到则返回null
     */
    fun findLineageDatasourceByName(datasourceName: String): LineageDatasourceDto? {
        return lineageRepository.findLineageDatasourceByName(datasourceName)
    }

    /**
     * 根据数据库信息和表名查询表ID (Find table ID by database info and table name)
     *
     * 支持大小写不敏感匹配，支持 'hive' 与 'hive2' 的映射
     * 查询逻辑：先根据数据库信息找到匹配的数据源，再根据表名和schema查找表
     *
     * @param host 主机地址
     * @param port 端口号
     * @param databaseName 数据库名称
     * @param dbType 数据库类型（支持大小写不敏感，hive/hive2映射）
     * @param tableName 表名称
     * @param schema 模式名称，可选
     * @return 表ID，如果未找到则返回null
     */
    fun findTableIdByDatabaseInfo(
        host: String,
        port: Int,
        databaseName: String,
        dbType: String,
        tableName: String,
        schema: String? = null
    ): Long? {
        return lineageRepository.findTableIdByDatabaseInfo(
            host = host,
            port = port,
            databaseName = databaseName,
            dbType = dbType,
            tableName = tableName,
            schema = schema
        )
    }
    
    /**
     * 根据系统ID查询系统相关内容 (Query system contents by system ID)
     * 
     * UC-02 & UC-03: 查看系统内容（数据源和血缘）
     * 
     * @param systemId 系统ID
     * @return 系统内容，包含元数据数据源和表级血缘关系
     */
    fun findSystemContents(systemId: Long): SystemContentsDto {
        // 查询该系统关联的元数据数据源
        val metadataDataSources = lineageRepository.findMetadataDataSourcesBySystemId(systemId)
        
        // 查询该系统关联的表级血缘关系
        val tableLineageViews = lineageRepository.findTableLineageBySystemId(systemId)
        
        return SystemContentsDto(
            systemId = systemId,
            metadataDataSources = metadataDataSources,
            tableLineageViews = tableLineageViews
        )
    }
    
    /**
     * 更新系统信息 (Update system information)
     * 
     * @param systemId 系统ID
     * @param status 系统状态 (可选)
     * @param cronExpression Cron表达式 (可选)
     * @return 更新后的系统信息，如果系统不存在则返回null
     */
    fun updateSystem(systemId: Long, status: String? = null, cronExpression: String? = null): SystemInfo? {
        val updateSuccess = lineageRepository.updateSystem(systemId, status, cronExpression)
        return if (updateSuccess) {
            lineageRepository.findSystemById(systemId)
        } else {
            null
        }
    }
    
    /**
     * 根据元数据数据源过滤表级血缘关系 (Filter table lineage by metadata data source)
     * 
     * UC-04: 从数据源快速导航到表
     * 
     * @param metadataDataSourceId 元数据数据源ID
     * @return 过滤后的表级血缘视图列表
     */
    fun findTableLineageByMetadataDataSource(metadataDataSourceId: Long): List<TableLineageView> {
        return lineageRepository.findTableLineageByMetadataDataSource(metadataDataSourceId)
    }
    
    /**
     * 查询作业处理历史 (Query job processing history)
     * 
     * UC-06: 支持查看手动血缘收集的执行结果
     * 
     * @param jobKey 作业键，可选，不提供则返回所有记录
     * @param limit 返回记录数限制
     * @return 作业处理历史列表
     */
    fun findJobProcessingHistory(jobKey: String? = null, limit: Int = 50): List<JobProcessingHistory> {
        return lineageRepository.findJobProcessingHistory(jobKey, limit)
    }

    /**
     * 根据表ID查询上游列级血缘关系
     *
     * @param tableId 表ID
     * @param maxLevels 最大查询层级
     * @return 列级血缘视图列表
     */
    fun findUpstreamColumnLineageByTableId(tableId: Long, maxLevels: Int = 3): List<ColumnLineageView> {
        return lineageRepository.findUpstreamColumnLineageByTableId(tableId, maxLevels)
    }

    /**
     * 根据表ID查询下游列级血缘关系
     *
     * @param tableId 表ID
     * @param maxLevels 最大查询层级
     * @return 列级血缘视图列表
     */
    fun findDownstreamColumnLineageByTableId(tableId: Long, maxLevels: Int = 3): List<ColumnLineageView> {
        return lineageRepository.findDownstreamColumnLineageByTableId(tableId, maxLevels)
    }

    /**
     * 查询所有具有cron表达式的活跃系统 (Find all active systems with cron expression)
     *
     * @return 系统调度信息列表
     */
    fun findSystemsWithCronExpression(): List<SystemScheduleInfo> {
        return lineageRepository.findSystemsWithCronExpression()
    }

    /**
     * 根据系统ID查询系统调度信息 (Find system schedule info by system ID)
     *
     * @param systemId 系统ID
     * @return 系统调度信息，如果不存在则返回null
     */
    fun findSystemScheduleInfo(systemId: Long): SystemScheduleInfo? {
        return lineageRepository.findSystemScheduleInfo(systemId)
    }
}

/**
 * 系统内容DTO (System Contents DTO)
 * 
 * UC-02 & UC-03: 包含系统关联的元数据数据源和表级血缘关系
 */
data class SystemContentsDto(
    val systemId: Long,
    val metadataDataSources: List<MetadataDataSourceDto>,
    val tableLineageViews: List<TableLineageView>
)
