package com.datayes.hdfs

import com.datayes.lineage.*
import com.datayes.shell.ShellScriptParser
import com.datayes.sql.SqlParser
import com.datayes.sql.SqlParsingException
import com.datayes.sql.TableReference as SqlTableReference
import com.datayes.sql.ColumnReference as SqlColumnReference
import org.slf4j.LoggerFactory

/**
 * HDFS Shell Script 血缘转换器
 * 
 * 将HDFS中的shell脚本转换为血缘信息，类似于DataExchangeJobLineageConverter
 * 主要功能：
 * 1. 从shell脚本中提取SQL语句
 * 2. 解析SQL获取表和列依赖关系
 * 3. 构建DataLineage对象
 */
object HdfsShellScriptLineageConverter {

    private val logger = LoggerFactory.getLogger(HdfsShellScriptLineageConverter::class.java)

    /**
     * 将HDFS shell脚本作业转换为数据血缘信息
     * 
     * @param job HDFS shell脚本作业
     * @return 血缘构建结果
     */
    fun convertToLineage(job: HdfsShellScriptJob): LineageResult {
        val warnings = mutableListOf<String>()
        val errors = mutableListOf<String>()

        return try {
            logger.info("a5e3f8c2 | 开始转换shell脚本血缘: {}", job.jobId)

            // 1. 从shell脚本中提取SQL语句
            val extractedSqls = extractSqlFromShellScript(job.scriptContent, warnings)

            if (extractedSqls.isEmpty()) {
                warnings.add("未从shell脚本中提取到任何SQL语句")
                return createEmptyLineageResult(warnings, errors)
            }

            logger.info("b7f4e9c1 | 从脚本{}中提取到{}条SQL语句", job.scriptName, extractedSqls.size)

            // 2. 解析每个SQL语句获取血缘信息
            val allSourceTables = mutableSetOf<SqlTableReference>()
            val allTargetTables = mutableSetOf<SqlTableReference>()
            val allColumnReferences = mutableSetOf<SqlColumnReference>()
            val consolidatedSql = extractedSqls.joinToString("\n-- Next SQL --\n")

            for ((index, sql) in extractedSqls.withIndex()) {
                try {
                    val parseResult = parseSqlStatement(sql)
                    if (parseResult.isDataModification) {
                        // 数据修改语句：分别处理源表和目标表
                        allSourceTables.addAll(parseResult.sourceTables)
                        parseResult.targetTable?.let { allTargetTables.add(it) }
                        allColumnReferences.addAll(parseResult.sourceColumns)
                        logger.info("c2d6f9e4 | SQL {}(INSERT)解析成功，发现{}个源表引用，1个目标表", 
                                   index + 1, parseResult.sourceTables.size)
                    } else {
                        // 查询语句：所有表都是源表
                        allSourceTables.addAll(parseResult.tables)
                        allColumnReferences.addAll(parseResult.columns)
                        logger.info("c2d6f9e4 | SQL {}(SELECT)解析成功，发现{}个表引用", 
                                   index + 1, parseResult.tables.size)
                    }
                } catch (e: SqlParsingException) {
                    warnings.add("SQL解析失败 (SQL ${index + 1}): ${e.message}")
                    logger.warn("d8e3f5c7 | SQL解析失败: {}", e.message)
                }
            }

            if (allSourceTables.isEmpty() && allTargetTables.isEmpty()) {
                warnings.add("所有SQL解析均失败，无法提取表血缘信息")
                return createEmptyLineageResult(warnings, errors)
            }

            // 3. 构建血缘信息
            val lineageResult = buildLineageFromSeparatedTables(
                job = job,
                sourceTables = allSourceTables.toList(),
                targetTables = allTargetTables.toList(),
                columnReferences = allColumnReferences.toList(),
                consolidatedSql = consolidatedSql,
                warnings = warnings,
                errors = errors
            )

            logger.info("e9f2c8a5 | 成功构建shell脚本血缘: {} ({}个源表)", 
                       job.jobId, lineageResult.lineage?.tableLineage?.sourceTables?.size ?: 0)

            lineageResult

        } catch (e: Exception) {
            logger.error("f3a8e6d2 | 转换shell脚本血缘时发生异常: {}", job.jobId, e)
            errors.add("转换过程中发生异常: ${e.message}")
            LineageResult(null, warnings, errors, false)
        }
    }

    /**
     * 从shell脚本内容中提取SQL语句
     */
    private fun extractSqlFromShellScript(
        scriptContent: String, 
        warnings: MutableList<String>
    ): List<String> {
        return try {
            // 使用现有的ShellScriptParser提取SQL
            val rawSqls = ShellScriptParser.parseShellScriptForSql(scriptContent)
            logger.info("4c9e7f3a | 原始提取到{}条SQL语句", rawSqls.size)

            // 过滤出真正的SQL语句，排除Hive命令
            val filteredSqls = rawSqls.filter { sql ->
                isValidSqlStatement(sql, warnings)
            }

            logger.debug("6e8f2a9c | 过滤后剩余{}条有效SQL语句", filteredSqls.size)
            filteredSqls

        } catch (e: Exception) {
            warnings.add("shell脚本解析失败: ${e.message}")
            logger.warn("5d2a8f6e | shell脚本解析失败", e)
            emptyList()
        }
    }

    /**
     * 判断是否为有效的SQL语句
     * 排除Hive命令和其他非SQL语句
     */
    private fun isValidSqlStatement(sql: String, warnings: MutableList<String>): Boolean {
        val trimmedSql = sql.trim().lowercase()

        // 空语句或过短语句
        if (trimmedSql.length < 10) {
            logger.debug("a3f7e2b8 | 跳过过短语句: {}", sql.take(50))
            return false
        }

        // Hive命令模式（非SQL）
        val hiveCommandPatterns = listOf(
            // load data inpath 命令
            Regex("^load\\s+data\\s+inpath\\s+", RegexOption.IGNORE_CASE),
            // add jar/file 命令
            Regex("^add\\s+(jar|file)\\s+", RegexOption.IGNORE_CASE),
            // set 命令
            Regex("^set\\s+\\w+\\s*=", RegexOption.IGNORE_CASE),
            // describe 命令
            Regex("^(desc|describe)\\s+", RegexOption.IGNORE_CASE),
            // show 命令
            Regex("^show\\s+", RegexOption.IGNORE_CASE),
            // explain 命令
            Regex("^explain\\s+", RegexOption.IGNORE_CASE),
            // use database 命令
            Regex("^use\\s+\\w+\\s*;?\\s*$", RegexOption.IGNORE_CASE),
            // msck repair 命令
            Regex("^msck\\s+repair\\s+", RegexOption.IGNORE_CASE),
            // analyze table 命令
            Regex("^analyze\\s+table\\s+", RegexOption.IGNORE_CASE)
        )

        // 检查是否匹配Hive命令模式
        for (pattern in hiveCommandPatterns) {
            if (pattern.containsMatchIn(trimmedSql)) {
                logger.debug("c5d8f9e2 | 跳过Hive命令: {}", sql.take(100))
                warnings.add("跳过Hive命令（非SQL）: ${sql.take(50)}...")
                return false
            }
        }

        // 有效的SQL语句模式
        val validSqlPatterns = listOf(
            Regex("^select\\s+", RegexOption.IGNORE_CASE),
            Regex("^insert\\s+", RegexOption.IGNORE_CASE),
            Regex("^update\\s+", RegexOption.IGNORE_CASE),
            Regex("^delete\\s+", RegexOption.IGNORE_CASE),
            Regex("^create\\s+(table|view|database|schema)\\s+", RegexOption.IGNORE_CASE),
            Regex("^drop\\s+(table|view|database|schema)\\s+", RegexOption.IGNORE_CASE),
            Regex("^alter\\s+(table|view)\\s+", RegexOption.IGNORE_CASE),
            Regex("^with\\s+", RegexOption.IGNORE_CASE), // CTE (Common Table Expressions)
            Regex("^truncate\\s+", RegexOption.IGNORE_CASE)
        )

        // 检查是否匹配有效SQL模式
        val isValidSql = validSqlPatterns.any { pattern ->
            pattern.containsMatchIn(trimmedSql)
        }

        if (!isValidSql) {
            logger.debug("f2a9e6c4 | 跳过无法识别的语句类型: {}", sql.take(100))
            warnings.add("跳过无法识别的语句类型: ${sql.take(50)}...")
        }

        return isValidSql
    }

    /**
     * 从已分离的源表和目标表构建血缘信息
     */
    private fun buildLineageFromSeparatedTables(
        job: HdfsShellScriptJob,
        sourceTables: List<SqlTableReference>,
        targetTables: List<SqlTableReference>,
        columnReferences: List<SqlColumnReference>,
        consolidatedSql: String,
        warnings: MutableList<String>,
        errors: MutableList<String>
    ): LineageResult {

        if (sourceTables.isEmpty()) {
            warnings.add("未识别到源表信息")
        }

        // 构建数据库信息（模拟，实际可能需要从脚本中解析）
        val defaultDatabase = createDefaultDatabase()

        // 构建表信息
        val sourceTableInfos = sourceTables.map { tableRef ->
            TableInfo(
                schema = tableRef.schema,
                tableName = tableRef.name,
                database = defaultDatabase
            )
        }

        // 如果没有明确的目标表，创建一个基于作业名称的虚拟目标表
        val targetTable = if (targetTables.isNotEmpty()) {
            val targetRef = targetTables.first()
            TableInfo(
                schema = targetRef.schema,
                tableName = targetRef.name,
                database = defaultDatabase
            )
        } else {
            // 创建虚拟目标表
            warnings.add("未识别到目标表，创建基于脚本名称的虚拟目标表")
            TableInfo(
                schema = "hdfs_scripts",
                tableName = job.jobName,
                database = defaultDatabase
            )
        }

        // 构建表级血缘
        val tableLineage = TableLineage(
            sourceTables = sourceTableInfos,
            targetTable = targetTable,
            lineageType = determineLineageTypeFromSql(consolidatedSql)
        )

        // 构建列级血缘（简化处理）
        val columnLineages = buildColumnLineages(
            columnReferences = columnReferences,
            sourceTableInfos = sourceTableInfos,
            targetTable = targetTable,
            warnings = warnings
        )

        // 构建完整血缘信息
        val dataLineage = DataLineage(
            jobId = job.jobId,
            jobName = "HDFS Shell Script: ${job.jobName}",
            tableLineage = tableLineage,
            columnLineages = columnLineages,
            sourceDatabase = defaultDatabase,
            targetDatabase = defaultDatabase,
            originalSql = consolidatedSql
        )

        return LineageResult(dataLineage, warnings, errors, true)
    }

    /**
     * 分离源表和目标表引用
     * 简化处理：INSERT/CREATE语句中的表视为目标表，其他视为源表
     */
    private fun separateSourceAndTargetTables(
        tableReferences: List<SqlTableReference>,
        warnings: MutableList<String>
    ): Pair<List<SqlTableReference>, List<SqlTableReference>> {

        // TODO: 实现更复杂的源表/目标表识别逻辑
        // 目前简化处理：所有表都视为源表
        warnings.add("使用简化的表分类逻辑：所有表视为源表")

        return Pair(tableReferences, emptyList())
    }

    /**
     * 构建列级血缘关系
     */
    private fun buildColumnLineages(
        columnReferences: List<SqlColumnReference>,
        sourceTableInfos: List<TableInfo>,
        targetTable: TableInfo,
        warnings: MutableList<String>
    ): List<ColumnLineage> {

        // 简化处理：为每个列引用创建基本的血缘关系
        return columnReferences.mapIndexedNotNull { index, columnRef ->
            try {
                // 检查是否为复杂表达式 (Complex Expression)
                val complexExpressionInfo = analyzeColumnExpression(columnRef, index)

                if (complexExpressionInfo.isComplex) {
                    warnings.add("检测到复杂表达式 (${complexExpressionInfo.expressionType})，使用生成的列名: ${complexExpressionInfo.suggestedColumnName}")
                    
                    // 使用第一个源表作为虚拟源表
                    val sourceTable = sourceTableInfos.firstOrNull()
                    if (sourceTable == null) {
                        warnings.add("无法为复杂表达式 ${complexExpressionInfo.suggestedColumnName} 找到源表")
                        return@mapIndexedNotNull null
                    }

                    val sourceColumn = ColumnInfo(
                        columnName = complexExpressionInfo.suggestedColumnName,
                        dataType = "unknown",
                        comment = "Computed from ${complexExpressionInfo.expressionType}: ${columnRef.name.take(200)}",
                        table = sourceTable
                    )

                    val targetColumn = ColumnInfo(
                        columnName = columnRef.alias ?: complexExpressionInfo.suggestedColumnName,
                        dataType = "unknown",
                        comment = null,
                        table = targetTable
                    )

                    ColumnLineage(
                        sourceColumn = sourceColumn,
                        targetColumn = targetColumn,
                        transformation = DataTransformation(
                            transformationType = complexExpressionInfo.transformationType,
                            description = complexExpressionInfo.description,
                            expression = columnRef.name.take(500) // 限制表达式长度
                        ),
                        columnIndex = index
                    )
                } else {
                    // 处理简单列引用
                    val sourceTable = sourceTableInfos.find { table ->
                        columnRef.tablePrefix == null || 
                        columnRef.tablePrefix == table.tableName ||
                        columnRef.tablePrefix == "${table.schema}.${table.tableName}"
                    } ?: sourceTableInfos.firstOrNull()

                    if (sourceTable == null) {
                        warnings.add("无法为列 ${columnRef.name} 找到对应的源表")
                        return@mapIndexedNotNull null
                    }

                    val sourceColumn = ColumnInfo(
                        columnName = columnRef.name,
                        dataType = "unknown", // SQL解析通常不包含类型信息
                        comment = null,
                        table = sourceTable
                    )

                    val targetColumn = ColumnInfo(
                        columnName = columnRef.alias ?: columnRef.name, // 优先使用别名，如total_amount
                        dataType = "unknown",
                        comment = null,
                        table = targetTable
                    )

                    ColumnLineage(
                        sourceColumn = sourceColumn,
                        targetColumn = targetColumn,
                        transformation = null, // 无转换的简单映射
                        columnIndex = index
                    )
                }

            } catch (e: Exception) {
                warnings.add("构建列血缘失败: ${columnRef.name.take(100)}, 错误: ${e.message}")
                null
            }
        }
    }

    /**
     * 从SQL确定血缘类型
     */
    private fun determineLineageTypeFromSql(sql: String): LineageType {
        val normalizedSql = sql.lowercase()

        return when {
            normalizedSql.contains("join") -> LineageType.JOIN
            normalizedSql.contains("group by") || 
            normalizedSql.contains("count(") ||
            normalizedSql.contains("sum(") ||
            normalizedSql.contains("avg(") -> LineageType.AGGREGATION
            normalizedSql.contains("where") -> LineageType.FILTER
            normalizedSql.contains("case when") ||
            normalizedSql.contains("substring") ||
            normalizedSql.contains("concat") -> LineageType.COMPLEX_TRANSFORMATION
            normalizedSql.contains("select") && 
            normalizedSql.contains("from") -> LineageType.SQL_QUERY
            normalizedSql.contains("insert") ||
            normalizedSql.contains("create") -> LineageType.DATA_MOVEMENT
            else -> LineageType.SCRIPT_PROCESSING
        }
    }

    /**
     * 创建默认数据库信息
     */
    private fun createDefaultDatabase(): DatabaseInfo {
        return DatabaseInfo(
            dbType = "hive",
            host = "hdfs-cluster",
            port = 10000,
            databaseName = "default",
            originalConnectionString = "***************************************"
        )
    }

    /**
     * 解析SQL语句，自动检测是SELECT查询还是数据修改语句
     * 
     * @param sql SQL语句
     * @return 统一的解析结果，包含表和列信息
     */
    private fun parseSqlStatement(sql: String): ParsedSqlResult {
        val trimmedSql = sql.trim()

        return when {
            // 检测数据修改语句 (INSERT, UPDATE, DELETE)
            trimmedSql.startsWith("INSERT", ignoreCase = true) ||
            trimmedSql.startsWith("UPDATE", ignoreCase = true) ||
            trimmedSql.startsWith("DELETE", ignoreCase = true) -> {
                logger.debug("5b8c2f9a | 检测到数据修改语句，使用parseDataModification解析")
                val result = SqlParser.parseDataModification(sql)
                // 转换为统一结果格式，分离源表和目标表
                ParsedSqlResult(
                    tables = result.sourceTables + result.targetTable,
                    columns = result.sourceColumns,
                    isDataModification = true,
                    sourceTables = result.sourceTables,
                    targetTable = result.targetTable,
                    sourceColumns = result.sourceColumns
                )
            }

            // 默认作为SELECT查询处理
            else -> {
                logger.debug("7e9d3f1c | 检测到查询语句，使用parse解析")
                val result = SqlParser.parse(sql)
                ParsedSqlResult(
                    tables = result.tables,
                    columns = result.columns,
                    isDataModification = false
                )
            }
        }
    }

    /**
     * 统一的SQL解析结果
     */
    private data class ParsedSqlResult(
        val tables: List<SqlTableReference>,
        val columns: List<SqlColumnReference>,
        val isDataModification: Boolean = false,
        val sourceTables: List<SqlTableReference> = emptyList(),
        val targetTable: SqlTableReference? = null,
        val sourceColumns: List<SqlColumnReference> = emptyList()
    )

    /**
     * 分析列表达式，判断是否为复杂表达式并提供相应的处理信息
     */
    private fun analyzeColumnExpression(columnRef: SqlColumnReference, index: Int): ColumnExpressionInfo {
        val expression = columnRef.name
        val expressionLower = expression.lowercase()
        
        // 如果有别名且表达式很复杂，优先使用别名
        val suggestedColumnName = columnRef.alias ?: run {
            when {
                // CASE WHEN 表达式
                expressionLower.contains("case") && expressionLower.contains("when") -> "case_when_expr_$index"
                
                // 日期函数
                expressionLower.contains("datediff") -> "date_diff_$index"
                expressionLower.contains("date_add") || expressionLower.contains("date_sub") -> "date_calc_$index"
                expressionLower.contains("to_date") || expressionLower.contains("from_unixtime") -> "date_convert_$index"
                
                // 聚合函数
                expressionLower.contains("count(") -> "count_result_$index"
                expressionLower.contains("sum(") -> "sum_result_$index"
                expressionLower.contains("avg(") || expressionLower.contains("average(") -> "avg_result_$index"
                expressionLower.contains("max(") -> "max_result_$index"
                expressionLower.contains("min(") -> "min_result_$index"
                
                // 字符串函数
                expressionLower.contains("concat") -> "concat_result_$index"
                expressionLower.contains("substring") || expressionLower.contains("substr") -> "substr_result_$index"
                expressionLower.contains("trim") || expressionLower.contains("ltrim") || expressionLower.contains("rtrim") -> "trim_result_$index"
                
                // 数学函数
                expressionLower.contains("round") -> "round_result_$index"
                expressionLower.contains("ceil") || expressionLower.contains("floor") -> "math_result_$index"
                
                // 类型转换
                expressionLower.contains("cast(") -> "cast_result_$index"
                
                // 窗口函数
                expressionLower.contains("row_number") -> "row_num_$index"
                expressionLower.contains("rank") -> "rank_result_$index"
                expressionLower.contains("over(") -> "window_func_$index"
                
                // 默认情况
                else -> "computed_column_$index"
            }
        }
        
        // 判断表达式类型和是否复杂
        val isComplex = expression.contains("(") ||  // 函数调用
                       expression.contains("CASE", ignoreCase = true) ||  // CASE 表达式
                       expression.contains("WHEN", ignoreCase = true) ||
                       expression.contains("THEN", ignoreCase = true) ||
                       expression.contains("+") || expression.contains("-") ||  // 算术运算
                       expression.contains("*") || expression.contains("/") ||
                       expression.length > 50  // 长表达式
        
        if (!isComplex) {
            return ColumnExpressionInfo(
                isComplex = false,
                suggestedColumnName = expression,
                expressionType = "Simple Column",
                transformationType = TransformationType.NONE,
                description = "直接列映射"
            )
        }
        
        // 识别表达式类型
        val (expressionType, transformationType, description) = when {
            expressionLower.contains("case") && expressionLower.contains("when") -> 
                Triple("CASE Expression", TransformationType.CONDITIONAL, "条件判断表达式")
            
            expressionLower.contains("count") || expressionLower.contains("sum") || 
            expressionLower.contains("avg") || expressionLower.contains("max") || 
            expressionLower.contains("min") ->
                Triple("Aggregate Function", TransformationType.AGGREGATION, "聚合函数计算")
            
            expressionLower.contains("datediff") || expressionLower.contains("date_add") ||
            expressionLower.contains("date_sub") || expressionLower.contains("to_date") ||
            expressionLower.contains("from_unixtime") ->
                Triple("Date Function", TransformationType.FUNCTION, "日期函数计算")
            
            expressionLower.contains("cast(") ->
                Triple("Type Conversion", TransformationType.TYPE_CAST, "数据类型转换")
            
            expressionLower.contains("concat") || expressionLower.contains("substring") ||
            expressionLower.contains("trim") ->
                Triple("String Function", TransformationType.FUNCTION, "字符串函数处理")
            
            expression.contains("+") || expression.contains("-") ||
            expression.contains("*") || expression.contains("/") ->
                Triple("Arithmetic Expression", TransformationType.EXPRESSION, "算术表达式计算")
            
            expressionLower.contains("(") ->
                Triple("Function Call", TransformationType.FUNCTION, "函数调用")
            
            else ->
                Triple("Complex Expression", TransformationType.EXPRESSION, "复杂表达式计算")
        }
        
        return ColumnExpressionInfo(
            isComplex = true,
            suggestedColumnName = suggestedColumnName,
            expressionType = expressionType,
            transformationType = transformationType,
            description = description
        )
    }

    /**
     * 列表达式分析结果信息
     */
    private data class ColumnExpressionInfo(
        val isComplex: Boolean,
        val suggestedColumnName: String,
        val expressionType: String,
        val transformationType: TransformationType,
        val description: String
    )

    /**
     * 创建空血缘结果
     */
    private fun createEmptyLineageResult(
        warnings: MutableList<String>,
        errors: MutableList<String>
    ): LineageResult {
        warnings.add("创建空血缘结果")
        return LineageResult(null, warnings, errors, false)
    }
}
