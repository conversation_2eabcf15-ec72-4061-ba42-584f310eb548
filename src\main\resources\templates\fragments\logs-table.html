<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<body>
    <!-- Logs table fragment -->
    <div th:fragment="logs-table">
        <div class="table-responsive">
            <table class="table table-striped table-hover log-table">
                <thead class="table-dark">
                    <tr>
                        <th scope="col">Time</th>
                        <th scope="col">Level</th>
                        <th scope="col">Logger</th>
                        <th scope="col">Message</th>
                        <th scope="col">Thread</th>
                        <th scope="col">Request ID</th>
                        <th scope="col">File</th>
                        <th scope="col">Line</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:if="${logs.empty}">
                        <td colspan="8" class="text-center">No logs available</td>
                    </tr>
                    <tr th:each="log : ${logs}">
                                                <td th:text="${log.formattedTime}"></td>
                        <td>
                            <span th:class="'log-level-' + ${log.level}" th:text="${log.level}"></span>
                        </td>
                        <td class="log-logger" th:text="${log.loggerName}"></td>
                        <td class="log-message" th:text="${log.message}"></td>
                        <td th:text="${log.threadName}"></td>
                        <td th:text="${log.requestId}"></td>
                        <td th:text="${log.fileName}"></td>
                        <td th:text="${log.lineNumber}"></td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <span th:text="${logs.size()} + ' log entries'"></span>
            </div>
            <div>
                <small class="text-muted">Last updated: <span th:text="${#dates.format(#dates.createNow(), 'yyyy-MM-dd HH:mm:ss')}"></span></small>
            </div>
        </div>
    </div>
</body>
</html>