package com.datayes.lineage

import java.security.MessageDigest

/**
 * 血缘哈希计算器 (Lineage Hash Calculator)
 * 用于生成血缘信息的唯一标识，支持变更检测
 */
object LineageHashCalculator {
    
    /**
     * 计算数据血缘的哈希值
     * @param dataLineage 数据血缘对象
     * @return SHA-256 哈希值
     */
    fun calculateHash(dataLineage: DataLineage): String {
        val content = buildString {
            // 表级血缘信息
            append("tables:")
            dataLineage.tableLineage.sourceTables.sortedBy { "${it.database.host}:${it.schema}:${it.tableName}" }
                .forEach { table ->
                    append("${table.database.host}:${table.database.port}:${table.database.databaseName}:")
                    append("${table.schema}:${table.tableName};")
                }
            append("target:${dataLineage.tableLineage.targetTable.database.host}:")
            append("${dataLineage.tableLineage.targetTable.database.port}:")
            append("${dataLineage.tableLineage.targetTable.database.databaseName}:")
            append("${dataLineage.tableLineage.targetTable.schema}:")
            append("${dataLineage.tableLineage.targetTable.tableName};")
            
            // 列级血缘信息
            append("columns:")
            dataLineage.columnLineages.sortedBy { "${it.sourceColumn.columnName}:${it.targetColumn.columnName}" }
                .forEach { columnLineage ->
                    append("${columnLineage.sourceColumn.columnName}->")
                    append("${columnLineage.targetColumn.columnName}:")
                    append("${columnLineage.transformation?.transformationType?.name ?: "DIRECT"};")
                }
        }
        
        return MessageDigest.getInstance("SHA-256")
            .digest(content.toByteArray())
            .joinToString("") { "%02x".format(it) }
    }
}