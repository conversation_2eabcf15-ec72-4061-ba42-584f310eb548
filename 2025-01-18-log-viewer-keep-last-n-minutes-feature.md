# 日志查看器新增"保留最近 N 分钟日志"功能
## Log Viewer Keep Last N Minutes Feature

**变更日期 (Change Date)**: 2025-01-18  
**变更类型 (Change Type)**: 功能增强 (Feature Enhancement)  
**影响范围 (Impact Scope)**: 日志管理模块 (Log Management Module)

## 变更概述 (Change Summary)

为 DGP Lineage Collector 的日志查看器 (Log Viewer) 添加了"保留最近 N 分钟日志"功能，允许用户快速清理旧日志，只保留指定时间范围内的最新日志记录。

## 变更动机 (Why the Change was Necessary)

1. **内存管理优化 (Memory Management Optimization)**: 长时间运行的应用程序会积累大量日志，消耗内存资源
2. **日志管理便利性 (Log Management Convenience)**: 用户需要快速清理旧日志以专注于最近的活动
3. **性能提升 (Performance Improvement)**: 减少日志数量可以提高日志搜索和展示的性能
4. **用户体验改善 (User Experience Enhancement)**: 提供更灵活的日志管理选项

## 技术实现 (How it was Implemented)

### 1. 后端实现 (Backend Implementation)

#### 1.1 InMemoryLogAppender 增强
- **文件**: `src/main/kotlin/com/datayes/util/InMemoryLogAppender.kt`
- **新增方法**: `keepLastMinutesLogs(minutes: Int): Int`
- **功能**: 根据时间戳过滤日志，保留最近 N 分钟的日志条目
- **算法**: 计算截止时间，遍历日志队列并移除过期条目

```kotlin
fun keepLastMinutesLogs(minutes: Int): Int {
    val cutoffTime = System.currentTimeMillis() - (minutes * 60 * 1000L)
    val originalSize = logEvents.size
    
    // Remove logs older than the cutoff time
    val iterator = logEvents.iterator()
    while (iterator.hasNext()) {
        val log = iterator.next()
        if (log.timestamp < cutoffTime) {
            iterator.remove()
        } else {
            // Since logs are added in chronological order, we can break early
            break
        }
    }
    
    return originalSize - logEvents.size
}
```

#### 1.2 LogViewerController 新端点
- **文件**: `src/main/kotlin/com/datayes/util/LogViewerController.kt`
- **新增端点**: `POST /logs/api/keep-last-minutes`
- **参数**: `minutes: Int` (要保留的分钟数)
- **返回**: JSON 格式的操作结果，包含状态、消息和移除的日志条数

```kotlin
@PostMapping("/api/keep-last-minutes")
@ResponseBody
fun keepLastMinutesLogs(@RequestParam minutes: Int): Map<String, Any> {
    return try {
        if (minutes <= 0) {
            mapOf("status" to "error", "message" to "Minutes must be greater than 0")
        } else {
            val removedCount = InMemoryLogAppender.keepLastMinutesLogs(minutes)
            mapOf(
                "status" to "success", 
                "message" to "Kept logs from last $minutes minutes",
                "removedCount" to removedCount,
                "minutes" to minutes
            )
        }
    } catch (e: Exception) {
        mapOf("status" to "error", "message" to "Failed to filter logs: ${e.message}")
    }
}
```

### 2. 前端实现 (Frontend Implementation)

#### 2.1 用户界面增强
- **文件**: `src/main/resources/templates/logs.html`
- **新增组件**: 卡片式 UI 组件，包含输入框和操作按钮
- **交互设计**: 数字输入框（1-1440 分钟）+ 确认对话框 + 操作结果显示

#### 2.2 HTMX 集成
- **技术栈**: HTMX + Bootstrap 5
- **异步操作**: 通过 HTMX 发送 POST 请求，无需页面刷新
- **用户反馈**: 实时显示操作结果，5 秒后自动清除

#### 2.3 输入验证和用户体验
- **客户端验证**: JavaScript 实时验证输入范围（1-1440 分钟）
- **用户确认**: 操作前显示确认对话框，防止误操作
- **反馈机制**: 成功/失败状态的视觉反馈
- **自动刷新**: 操作完成后自动刷新日志表格

### 3. 测试实现 (Testing Implementation)
- **文件**: `src/test/kotlin/com/datayes/util/InMemoryLogAppenderTest.kt`
- **测试覆盖**: 边界条件测试、错误处理测试、返回值验证
- **测试策略**: 单元测试 + 集成测试

## 功能特性 (Key Features)

1. **时间范围控制 (Time Range Control)**: 支持 1-1440 分钟（24 小时）的时间范围
2. **操作确认 (Operation Confirmation)**: 防止误操作的确认对话框
3. **实时反馈 (Real-time Feedback)**: 显示操作结果和移除的日志条数
4. **输入验证 (Input Validation)**: 客户端和服务器端双重验证
5. **无刷新操作 (No Page Refresh)**: 使用 HTMX 实现流畅的用户体验
6. **双语支持 (Bilingual Support)**: 中英文对照的界面文本

## 安全考虑 (Security Considerations)

1. **参数验证 (Parameter Validation)**: 严格验证输入参数范围
2. **错误处理 (Error Handling)**: 妥善处理异常情况，避免系统崩溃
3. **用户确认 (User Confirmation)**: 要求用户确认不可逆操作

## 性能影响 (Performance Impact)

1. **正面影响 (Positive Impact)**:
   - 减少内存占用
   - 提高日志查询性能
   - 改善页面渲染速度

2. **操作开销 (Operation Overhead)**:
   - O(n) 时间复杂度的日志过滤操作
   - 在大量日志场景下可能有短暂的处理延迟

## 兼容性 (Compatibility)

- **向后兼容 (Backward Compatible)**: 完全兼容现有日志功能
- **浏览器支持 (Browser Support)**: 支持所有现代浏览器
- **依赖项 (Dependencies)**: 无新增外部依赖

## 未来改进方向 (Future Enhancements)

1. **预设时间选项 (Preset Time Options)**: 添加常用时间选项（如 15 分钟、1 小时、4 小时）
2. **定时清理 (Scheduled Cleanup)**: 支持自动定时清理旧日志
3. **批量操作 (Batch Operations)**: 支持更复杂的日志管理操作
4. **日志导出 (Log Export)**: 清理前支持导出重要日志
5. **用户偏好设置 (User Preferences)**: 记住用户的时间范围偏好

## 测试验证 (Testing Verification)

- ✅ 单元测试通过
- ✅ 集成测试通过  
- ✅ 用户界面测试通过
- ✅ 边界条件验证
- ✅ 错误处理验证

## 文档和帮助 (Documentation and Help)

- 界面文本提供中英文说明
- 输入框有明确的提示信息
- 操作结果有详细的反馈信息

---

**变更责任人 (Change Owner)**: AI Assistant  
**复查人 (Reviewer)**: 待定  
**部署状态 (Deployment Status)**: 开发完成，待部署 