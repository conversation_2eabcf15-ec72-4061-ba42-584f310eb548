# HDFS Shell Script Lineage Integration

This document describes the complete data flow implementation for processing shell scripts from HDFS ZIP files and converting them to data lineage information.

## Architecture Overview

The HDFS lineage integration follows the same architectural patterns as the existing DataExchange integration, providing a parallel processing pipeline specifically for shell scripts stored in HDFS ZIP files.

## Data Flow

```
HDFS ZIP Files → Extract Shell Scripts → Parse SQL → Generate Lineage → Store in Database
```

### 1. HDFS Processing Layer (`com.datayes.hdfs`)

#### Core Components

- **`HdfsUtils.kt`**: HDFS connectivity and file operations
  - ZIP file discovery and filtering
  - Shell script extraction from ZIP archives
  - Backup file filtering (timestamp-based)

- **`HdfsShellScriptJob.kt`**: Data model for shell script jobs
  - Similar to `DataExchangeJob` but for shell scripts
  - Includes metadata like ZIP path, script content, size, etc.

#### Key Functions

```kotlin
// Find and process ZIP files, extract shell scripts
fun processZipFilesForShellScripts(fileSystem: FileSystem, directoryPath: Path): ZipProcessingResult

// Filter out backup files with timestamps
fun filterBackupZipFiles(zipFilePaths: List<String>): List<String>

// Extract shell scripts from ZIP
fun extractShellScriptsFromZip(fileSystem: FileSystem, zipFilePath: Path): List<ShellScript>
```

### 2. Service Layer

#### `HdfsShellScriptService.kt`
- Orchestrates HDFS processing and lineage analysis
- Implements change detection similar to `DataExchangeJobService`
- Batch processing of multiple shell scripts

Key Methods:
```kotlin
// Main processing function
fun processHdfsShellScripts(hdfsPath: String, config: HdfsProcessingConfig): HdfsShellScriptProcessingResult

// Lineage processing with change detection
fun processJobLineageWithChangeDetection(job: HdfsShellScriptJob, taskId: Long?): HdfsLineageProcessResult
```

### 3. Lineage Conversion Layer

#### `HdfsShellScriptLineageConverter.kt`
- Converts shell scripts to `DataLineage` objects
- Leverages existing `ShellScriptParser` for SQL extraction
- Uses existing `SqlParser` for table/column dependency analysis

Conversion Process:
1. Extract SQL statements from shell script using `ShellScriptParser`
2. **Filter out Hive commands** (non-SQL statements) to focus on actual SQL
3. Parse each valid SQL statement using `SqlParser` 
4. Build table-level and column-level lineage relationships
5. Create `DataLineage` object compatible with existing storage

### 4. REST API Layer

#### `HdfsShellScriptController.kt`
- REST endpoints for HDFS processing
- Health checks for HDFS connectivity
- Configuration management

API Endpoints:
- `POST /api/hdfs/process` - Process HDFS shell scripts
- `GET /api/hdfs/config` - Get default configuration
- `GET /api/hdfs/health` - HDFS connectivity health check

## Data Models

### Core Data Structures

```kotlin
data class HdfsShellScriptJob(
    val jobId: String,                    // Generated from ZIP path + script name
    val jobName: String,                  // Script name without .sh extension
    val zipFilePath: String,              // Full HDFS path to ZIP file
    val scriptName: String,               // Shell script filename
    val scriptContent: String,            // Complete script content
    val scriptSizeBytes: Int,             // Script size in bytes
    val extractedAt: LocalDateTime,       // Extraction timestamp
    val status: HdfsJobStatus             // ACTIVE, INACTIVE, ERROR, ARCHIVED
)

data class ZipProcessingResult(
    val results: List<ZipFileProcessingResult>,
    val totalZipFilesProcessed: Int,
    val totalShellScriptsFound: Int,
    val totalContentSize: Int
)
```

### Configuration

```kotlin
data class HdfsProcessingConfig(
    val hdfsBasePath: String = "/share_ftp",
    val includePatterns: List<String> = listOf("*.zip"),
    val excludePatterns: List<String> = listOf("*backup*", "*temp*"),
    val enableBackupFiltering: Boolean = true,
    val maxScriptSizeBytes: Int = 10 * 1024 * 1024  // 10MB
)
```

## Integration with Existing System

### Reused Components

1. **`ShellScriptParser`** - SQL extraction from shell scripts
2. **`SqlParser`** - SQL parsing for table/column dependencies  
3. **`LineageRepository`** - Database persistence layer
4. **`LineageChangeDetectionService`** - Change detection logic
5. **`DataLineage`** model - Same lineage data structure

### New LineageType

Added `SCRIPT_PROCESSING` to handle shell script specific processing patterns.

## Backup File Filtering

The system automatically filters out backup ZIP files based on timestamp patterns:

- Pattern 1: `filename.YYYYMMDDHHMMSS.zip` (dot separator)
- Pattern 2: `filenameYYYYMMDDHHMMSS.zip` (direct concatenation)

Examples of filtered files:
- `test.20240313213459.zip` ✅ Filtered out
- `test20230914161419.zip` ✅ Filtered out  
- `test.zip` ❌ Processed
- `test111.zip` ❌ Processed

## Hive Command Filtering

The system intelligently separates actual SQL statements from Hive-specific commands to focus lineage analysis on data flow operations.

### Filtered Out (Hive Commands)

```bash
# Data loading commands
hive -e "load data inpath '/data/file.csv' overwrite into table test_table"

# Configuration commands  
hive -e "set hive.exec.dynamic.partition=true"

# Metadata commands
hive -e "show tables"
hive -e "describe table_name"
hive -e "use database_name"

# Administrative commands
hive -e "add jar /path/to/file.jar"
hive -e "msck repair table test_table"
hive -e "analyze table test_table compute statistics"
```

### Processed (Valid SQL)

```sql
-- Data query operations
hive -e "SELECT * FROM source_table WHERE date='2024-01-01'"

-- Data manipulation
hive -e "INSERT INTO target_table SELECT col1, col2 FROM source_table"

-- DDL operations
hive -e "CREATE TABLE new_table AS SELECT * FROM existing_table"
hive -e "DROP TABLE temp_table"
hive -e "ALTER TABLE test_table ADD COLUMN new_col STRING"

-- Data modification
hive -e "UPDATE test_table SET col1='value' WHERE id=1"
hive -e "DELETE FROM test_table WHERE condition=true"
```

This filtering ensures that lineage analysis focuses on actual data transformations and movements rather than administrative or metadata operations.

## Usage Examples

### Process HDFS Shell Scripts

```kotlin
val service = HdfsShellScriptService(lineageRepository, changeDetectionService)

// Process all shell scripts in a directory
val result = service.processHdfsShellScripts("/share_ftp/public_project")

// Process lineage for discovered jobs
val lineageResults = service.processMultipleJobsLineage(result.jobs)
```

### REST API Usage

```bash
# Process HDFS shell scripts
curl -X POST http://localhost:8080/api/hdfs/process \
  -H "Content-Type: application/json" \
  -d '{
    "hdfsPath": "/share_ftp/public_project",
    "processLineage": true
  }'

# Check HDFS connectivity
curl http://localhost:8080/api/hdfs/health
```

## Testing

### Test Coverage

- **`HdfsShellScriptServiceTest.kt`**: Service layer functionality
- **`HdfsReaderTest.kt`**: HDFS connectivity and ZIP processing
- Unit tests for backup file filtering
- Integration tests for lineage conversion

### Test Scenarios

1. Simple shell script with single SQL
2. Complex shell script with multiple SQL statements
3. Scripts with variable substitution
4. Backup file filtering validation
5. **Hive command filtering validation**
6. **Change detection for duplicate processing**
7. Error handling and edge cases

## Logging and Monitoring

### Structured Logging

All log messages include unique UUID prefixes for traceability:

```kotlin
logger.info("5a3e8f2d | 开始处理HDFS路径: {}", hdfsPath)
logger.warn("32a4f7b1 | Failed to read file content: {}", filePath, e)
```

### Key Metrics

- Number of ZIP files processed
- Number of shell scripts extracted
- Processing time per script
- Lineage conversion success rate
- Database update frequency

## Change Detection Implementation

### Problem Solved

The initial implementation had a change detection issue where processing the same ZIP file path twice would incorrectly trigger database updates on the second run, even though the content hadn't changed.

### Root Cause

- HDFS shell script jobs were using `LineageChangeDetectionService` which queries `JobProcessingHistoryRepository`
- This repository only contains history for `DataExchangeJob` entities, not `HdfsShellScriptJob`
- Without previous processing history, every run was treated as a "first run" with changes

### Solution

1. **Reuse Existing Infrastructure**: HDFS jobs now save processing history to the same `JobProcessingHistory` table
2. **Compatible Storage**: Processing history is stored with HDFS-specific prefixes (`hdfs_` prefix for job IDs)
3. **Proper Change Detection**: Second run of the same content now correctly detects no changes and skips database updates

### Implementation Details

```kotlin
// Processing history is now recorded after each HDFS job processing
recordHdfsProcessingHistory(
    jobKey = jobKey,
    job = job, 
    result = processingResult,
    lineageHash = changeDetection.currentHash,
    processingTime = processingTime
)
```

### Expected Behavior

- **First Run**: `hasChanges = true` → Database updated + History recorded
- **Second Run** (same content): `hasChanges = false` → No database update + History updated

## Future Enhancements

1. **Database Persistence**: Implement repository layer for `HdfsShellScriptJob`
2. **Scheduling**: Add cron-based processing for regular HDFS scanning
3. **Advanced SQL Parsing**: Enhance column-level lineage accuracy
4. **Performance Optimization**: Parallel processing of large ZIP files
5. **Monitoring Dashboard**: Web UI for HDFS processing status

## Error Handling

The system includes comprehensive error handling:

- HDFS connectivity failures
- ZIP file corruption
- Shell script parsing errors
- SQL parsing failures
- Database update errors

All errors are logged with context and returned in API responses for proper debugging.