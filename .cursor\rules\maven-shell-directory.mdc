---
description: 
globs: 
alwaysApply: true
---
# Maven Commands and Working Directory

When using `run_terminal_cmd` to execute Maven-related commands (`mvn`, `./mvnw`, `mvnw.cmd`):

1. **Assume Current Directory**: You are already in the project root (`D:/projects/dgp/dgp-lineage-collector`). Do **NOT** prepend `cd` commands such as `cd /path/to/project && mvn ...`.
2. **Simplify Commands**: Invoke <PERSON><PERSON> directly, e.g.
   ```
   mvn clean test -q
   ./mvnw package
   ```
3. **Background Jobs**: Follow existing guidelines for long-running tasks (`is_background = true`).
4. **Consistency**: This keeps terminal output concise and avoids path errors in CI or local shells.

> **Example – Bad**
> ```
> cd /d/projects/dgp/dgp-lineage-collector && mvn compile
> ```
>
> **Example – Good**
> ```
> mvn compile
> ```

