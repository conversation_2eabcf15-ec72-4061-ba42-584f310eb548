#!/bin/bash
hdfs dfs -cat /project/regulatory_platform/common/tbds_config.properties > tbds_config.properties
source tbds_config.properties
hdfs dfs -cat /project/regulatory_platform/common/tbds_config.sh > tbds_config.sh
source tbds_config.sh

db_name='urp_dws'
table_name='ljapolpay'
base_field="b.<PERSON>       ,
            b.GrpPolicyNo    ,
            b.GrpProductNo   ,
            b.<PERSON>pp<PERSON>o          ,
            b.Policy<PERSON>o       ,
            b.ProductNo      ,
            b.Pay<PERSON>o          ,
            b.Getnotice<PERSON>o    ,
            b.<PERSON>no       ,
            b.Report<PERSON>o       ,
            b.<PERSON><PERSON>  ,
            b.<PERSON>        ,
            b.<PERSON>         ,
            b.<PERSON>   ,
            b.<PERSON>         ,
            b.<PERSON>r<PERSON>       ,
            b.<PERSON>ack<PERSON>  ,
            b.LiabilityCode  ,
            b.PayPlanCode    ,
            b.PayType        ,
            b.GPFlag         ,
            b.CompanyCode    ,
            b.Manage<PERSON>om      ,
            b.ASType         ,
            b.Pay<PERSON>nt<PERSON>        ,
            b.<PERSON>m<PERSON><PERSON>       ,
            b.PremiumType    ,
            b.Product<PERSON><PERSON>    ,
            b.<PERSON>        ,
            b.<PERSON><PERSON>,
            b.<PERSON>,
            b<PERSON>       ,
            b.<PERSON>   ,
            b.CurPaidToDate  ,
            b.ConfDate       ,
            b.SendDate       ,
            b.AvyDate        ,
            b.PayFromDate    ,
            b.PayEndDate     ,
            b.Paymode        ,
            b.BankCode       ,
            b.BankName       ,
            b.BankAccNo      ,
            b.AccName        ,
            b.CertType       ,
            b.CertNo         ,
            b.InvoCode       ,
            b.PsnsCount      ,
            b.PaymentType    ,
            b.PayabColVouCod ,
            b.PreColVouCod   ,
            b.ActColVouCod   ,
            b.Checkcode      ,
            b.AgentCode      ,
            b.AgtCode        ,
            b.ProcRate       ,
            b.PayabProcVouCod,
            b.ActProcVouCode ,
            b.Rate           ,
            b.PayabVouCod    ,
            b.ActVouCode     ,
            b.premflag       ,
            b.MakeDate       ,
            b.MakeTime       ,
            b.ModifyDate     ,
            b.ModifyTime"
export yesterday=$1
export nominalformateDate=`date -d "${yesterday} +1 days" "+%Y-%m-%d"`
#======================================================================================================================================================
#'0101','0105','0201','0202','0304','04','0501','0503','0702','0703','0801','1601','2201','2501','2701','9801'
#1.刷新${db_name}.full_${table_name}表
echo "$(date +'%Y-%m-%d %T')：开始刷新${db_name}.full_${table_name}表"
${hive} -e "
insert overwrite table ${db_name}.increment_trans_${table_name} partition(pushdate='${nominalformateDate}')
select distinct t.* from (select a.btype,
       a.bdtype,
       a.transdate, ${base_field}
from
  (select '01' as btype,
          '01' as bdtype,
          transdate,
          contno
   from urp_dws.transinfo_0101 where contno is not null
     and grpcontno is null)a
left join ${db_name}.full_${table_name} b on a.contno = b.policyno
where b.PremiumType in ('1','12')
union all
select a.btype,
       a.bdtype,
       a.transdate, ${base_field}
from
  (select '01' as btype,
          '01' as bdtype,
          transdate,
          grpcontno
   from urp_dws.transinfo_0101
   where contno is null
     and grpcontno is not null
   group by transdate,
            grpcontno
   union all select '01' as btype,
                    '05' as bdtype,
                    transdate,
                    contno
   from urp_dws.transinfo_0105) a
left join ${db_name}.full_${table_name} b on a.grpcontno = b.grppolicyno
where b.PremiumType in ('1','12')
union all
select a.btype,
       a.bdtype,
       a.transdate, ${base_field}
from
  (select '02' as btype,
          '01' as bdtype,
          transdate,
          payno,
          contno
   from urp_dws.transinfo_0201) a
left join ${db_name}.full_${table_name} b on a.contno = b.policyno and a.payno=b.payno
left semi join 
(select a.polno from urp_ods.full_lcpol a
left semi join urp_ods.mslmriskapp app
 on a.riskcode = app.riskcode
and app.riskperiod = 'L' and nvl(app.POLICYPERIODSUBTYPE,'') <> '12') pol
 on b.productno=pol.polno
 where b.PremiumType in ('2','12')
union all
select a.btype,
       a.bdtype,
       a.transdate, ${base_field}
from
  (select '02' as btype,
                    '02' as bdtype,
                    transdate,
                    contno,payno
   from urp_dws.transinfo_0202) a
left join ${db_name}.full_${table_name} b on a.contno = b.policyno and a.payno=b.payno
left semi join 
(select a.polno from urp_ods.full_lcpol a
left semi join urp_ods.mslmriskapp app
 on a.riskcode = app.riskcode
and (app.riskperiod in ('M','S') or (app.riskperiod ='L' and app.POLICYPERIODSUBTYPE = '12'))) pol
 on b.productno=pol.polno
where b.PremiumType in ('2','12')
union all
select a.btype,
       a.bdtype,
       a.transdate, ${base_field}
from
  (select '03' as btype,
          '04' as bdtype,
          transdate,
          contno
   from urp_dws.transinfo_0304) a
left join ${db_name}.full_${table_name} b on a.contno = b.policyno
where b.PremiumType in ('11')
union all
select a.btype,
       a.bdtype,
       a.transdate, ${base_field}
from
  (select '05' as btype,
          '01' as bdtype,
          ClaimNo,
          transdate,
          contno
   from urp_dws.transinfo_0501
   group by ClaimNo,
            transdate,
            contno) a
left join ${db_name}.full_${table_name} b on a.contno = b.policyno
and a.claimno=b.claimno
where b.PremiumType in ('8','13')
union all
select a.btype,
       a.bdtype,
       a.transdate, ${base_field}
from
  (select '05' as btype,
          '03' as bdtype,
          ClaimNo,
          transdate,
          contno
   from urp_dws.transinfo_0503
   group by ClaimNo,
            transdate,
            contno ) a
left join ${db_name}.full_${table_name} b on a.contno = b.policyno
and a.claimno=b.claimno
where b.PremiumType in ('13')
union all
select a.btype,
       a.bdtype,
       a.transdate, ${base_field}
from
  (select '98' as btype,
          '01' as bdtype,
          ClaimNo,
          transdate
   from urp_dws.transinfo_9801
   group by ClaimNo,
            transdate) a
left join ${db_name}.full_${table_name} b on a.claimno = b.ClaimNo
where b.PremiumType in ('13')
union all
select a.btype,
       a.bdtype,
       a.transdate, ${base_field}
from
  (select '07' as btype,
          '02' as bdtype,
          transdate,
          payno
   from urp_dws.transinfo_0702) a
left join ${db_name}.full_${table_name} b on a.payno = b.payno
union all
select a.btype,
       a.bdtype,
       a.transdate, ${base_field}
from
  (select '16' as btype,
          '01' as bdtype,
          transdate,
          payno,
          contno
   from urp_dws.transinfo_1601
   where nvl(payno,'') != ''
   group by payno,
            transdate,
            contno) a
left join ${db_name}.full_${table_name} b on a.payno = b.payno
and a.contno=b.policyno
where b.PremiumType in ('5')
union all
select a.btype,
       a.bdtype,
       a.transdate, ${base_field}
from
  (select '08' as btype,
          '01' as bdtype,
          transdate,
          payno,
          contno
   from urp_dws.transinfo_0801
   where nvl(payno,'') != ''
   group by payno,
            transdate,
            contno
   union all select '25' as btype,
                    '01' as bdtype,
                    transdate,
                    payno,
                    contno
   from urp_dws.transinfo_2501
   union all select '27' as btype,
                    '01' as bdtype,
                    transdate,
                    payno,
                    contno
   from urp_dws.transinfo_2701
   group by transdate,
            contno,
            payno) a
left join ${db_name}.full_${table_name} b on a.contno = b.policyno
and a.payno=b.payno
union all
select a.btype,
       a.bdtype,
       a.transdate, ${base_field}
from
  (select '08' as btype,
          '01' as bdtype,
          transdate,
          getnoticeno,
          contno
   from urp_dws.transinfo_0801
   where nvl(payno,'') = ''
   group by transdate,
            contno,
            getnoticeno) a
left join ${db_name}.full_${table_name} b on a.contno = b.policyno
and a.getnoticeno=b.getnoticeno
where b.PremiumType in ('3','9')
union all
select a.btype,
       a.bdtype,
       a.transdate, ${base_field}
from
  (select '08' as btype,
          '01' as bdtype,
          transdate,
          getnoticeno,
          contno
   from urp_dws.transinfo_0801
   where nvl(payno,'') = ''
   group by transdate,
            contno,
            getnoticeno) a
join dorado_endorse.full_ljagetdraw draw on to_date(draw.getdate) = a.transdate and a.contno = draw.contno
join ${db_name}.full_${table_name} b on draw.getnoticeno = b.getnoticeno
where b.PremiumType in ('3') and  nvl(a.getnoticeno,'')=''
union all
select a.btype,
       a.bdtype,
       a.transdate, ${base_field}
from
  (select '16' as btype,
          '01' as bdtype,
          transdate,
          getnoticeno,
          contno
   from urp_dws.transinfo_1601
   where nvl(payno,'') = ''
   group by transdate,
            contno,
            getnoticeno) a
left join ${db_name}.full_${table_name} b on a.contno = b.policyno
and a.getnoticeno=b.getnoticeno
where b.PremiumType in ('5')
union all
select a.btype,
       a.bdtype,
       a.transdate, ${base_field}
from
  (select '22' as btype,
          '01' as bdtype,
          transdate,
          payno,
          grpcontno
   from urp_dws.transinfo_2201
   group by grpcontno,
            transdate,
            payno) a
left join ${db_name}.full_${table_name} b on a.payno = b.payno
and a.grpcontno=b.grppolicyno
where b.PremiumType in ('9')
union all
select a.btype,
       a.bdtype,
       a.transdate, ${base_field}
from
  (select '04' as btype,
          edortype as bdtype,
          transdate,
          contno,
          edoracceptno
   from urp_dws.transinfo_04
   where contno is not null
   group by transdate,
            contno,
            edortype,
            edoracceptno) a
left join ${db_name}.full_${table_name} b on a.edoracceptno = b.edoracceptno
and a.contno=b.policyno
and a.bdtype=b.edortype
where b.PremiumType in ('2','10','7','')
union all
select a.btype,
       a.bdtype,
       a.transdate, ${base_field}
from
  (select '04' as btype,
          edortype as bdtype,
          transdate,
          grpcontno,
          edoracceptno
   from urp_dws.transinfo_04
   where contno is null
     and grpcontno is not null
   group by edortype,
            transdate,
            grpcontno,
            edoracceptno) a
left join ${db_name}.full_${table_name} b on a.edoracceptno = b.edoracceptno
and a.grpcontno=b.grppolicyno
and a.bdtype=b.edortype
where b.PremiumType in ('2','10','7','')) t"
exitCodeCheck $? '刷新${db_name}.increment_${table_name} 失败' '刷新${db_name}.increment_${table_name} 成功'
