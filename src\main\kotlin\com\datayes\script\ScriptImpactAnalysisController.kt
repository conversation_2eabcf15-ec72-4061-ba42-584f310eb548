package com.datayes.script

import com.datayes.ApiResponse
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import java.time.LocalDateTime

/**
 * 脚本影响分析控制器 (Script Impact Analysis Controller)
 * 
 * 实现脚本影响分析相关的 REST API 接口，包括脚本上传、查询、删除和下载功能
 */
@Tag(name = "Script Impact Analysis API", description = "脚本影响分析 (Script Impact Analysis) 相关接口")
@RestController
@CrossOrigin(origins = ["*"])
@RequestMapping("/api/v1/script-impact")
class ScriptImpactAnalysisController(
    private val scriptService: ScriptService,
    private val scriptAnalysisService: ScriptAnalysisService
) {

    private val logger = LoggerFactory.getLogger(ScriptImpactAnalysisController::class.java)

    /**
     * UC-1: 上传脚本文件 (Upload Script File)
     * 
     * 通过一个API端点上传一个SQL (.sql) 或 Shell (.sh) 脚本文件，
     * 以便系统可以存储该文件并对其进行后续的影响分析。
     * 
     * @param file 上传的脚本文件
     * @param uploadUser 上传用户名
     * @return 上传结果
     */
    @Operation(summary = "上传脚本文件 (UC-1)", description = "上传SQL (.sql) 或 Shell (.sh) 脚本文件进行影响分析")
    @PostMapping("/upload", consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    fun uploadScript(
        @Parameter(description = "上传的脚本文件 (SQL或SH)", required = true)
        @RequestParam("file") file: MultipartFile,
        @Parameter(description = "上传用户名", example = "john.doe", required = true)
        @RequestParam("uploadUser") uploadUser: String
    ): ResponseEntity<ApiResponse<ScriptUploadResponse>> {
        logger.info("a1b2c3d4 | 接收到脚本上传请求: fileName=${file.originalFilename}, uploadUser=$uploadUser")

        return try {
            // 参数验证 (Parameter validation)
            if (uploadUser.isBlank()) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("上传用户名不能为空 (Upload user cannot be empty)"))
            }

            // 调用服务处理上传 (Call service to handle upload)
            val result = scriptService.uploadScript(file, uploadUser)

            if (result.success) {
                val response = ScriptUploadResponse(
                    scriptId = result.scriptId!!,
                    scriptName = result.uploadedScript!!.scriptName,
                    scriptType = result.uploadedScript.scriptType,
                    fileSize = result.uploadedScript.fileSize,
                    analysisStatus = result.uploadedScript.analysisStatus,
                    uploadUser = result.uploadedScript.uploadUser,
                    createdAt = result.uploadedScript.createdAt
                )

                logger.info("e5f6g7h8 | 脚本上传成功: scriptId=${response.scriptId}")

                ResponseEntity.ok(
                    ApiResponse.success(
                        data = response,
                        message = result.message
                    )
                )
            } else {
                // 处理重复文件情况 (Handle duplicate file case)
                if (result.duplicateScript != null) {
                    val response = ScriptUploadResponse(
                        scriptId = result.duplicateScript.id!!,
                        scriptName = result.duplicateScript.scriptName,
                        scriptType = result.duplicateScript.scriptType,
                        fileSize = result.duplicateScript.fileSize,
                        analysisStatus = result.duplicateScript.analysisStatus,
                        uploadUser = result.duplicateScript.uploadUser,
                        createdAt = result.duplicateScript.createdAt
                    )

                    logger.warn("i9j0k1l2 | 文件重复: duplicateScriptId=${response.scriptId}")

                    ResponseEntity.ok()
                        .body(ApiResponse.error<ScriptUploadResponse>(result.message).copy(data = response))
                } else {
                    ResponseEntity.badRequest()
                        .body(ApiResponse.error(result.message))
                }
            }

        } catch (e: IllegalArgumentException) {
            logger.warn("m3n4o5p6 | 脚本上传参数错误", e)
            ResponseEntity.badRequest()
                .body(ApiResponse.error("请求参数错误: ${e.message} (Invalid request parameters)"))

        } catch (e: Exception) {
            logger.error("q7r8s9t0 | 脚本上传时发生错误", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("上传失败: ${e.message} (Upload failed)"))
        }
    }

    /**
     * UC-2: 查询脚本列表 (Query Script List)
     * 
     * 通过API请求一个已上传脚本的分页列表，并能根据脚本名称、上传人和上传时间范围进行筛选
     * 
     * @param scriptName 脚本名称模糊搜索 (可选)
     * @param uploadUser 上传用户名过滤 (可选)
     * @param startDate 开始日期时间过滤 (可选)
     * @param endDate 结束日期时间过滤 (可选)
     * @param scriptType 脚本类型过滤 (可选)
     * @param analysisStatus 分析状态过滤 (可选)
     * @param page 页码 (从1开始)
     * @param size 每页大小
     * @return 分页的脚本列表
     */
    @Operation(summary = "查询脚本列表 (UC-2)", description = "分页查询已上传的脚本，支持按名称、上传人、精确时间、类型和状态筛选。时间参数支持完整日期时间格式 (yyyy-MM-ddTHH:mm:ss) 以提供更精确的时间范围查询")
    @GetMapping("/scripts")
    fun queryScripts(
        @Parameter(description = "脚本名称模糊搜索 (可选)", example = "daily_job")
        @RequestParam(required = false) scriptName: String?,
        @Parameter(description = "上传人 (可选)", example = "jane.doe")
        @RequestParam(required = false) uploadUser: String?,
        @Parameter(description = "开始日期时间 (格式: yyyy-MM-ddTHH:mm:ss) (可选)", example = "2024-01-01T00:00:00")
        @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) startDate: LocalDateTime?,
        @Parameter(description = "结束日期时间 (格式: yyyy-MM-ddTHH:mm:ss) (可选)", example = "2024-12-31T23:59:59")
        @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) endDate: LocalDateTime?,
        @RequestParam(required = false) scriptType: ScriptType?,
        @Parameter(description = "分析状态 (PENDING, ANALYZING, COMPLETED, FAILED) (可选)", schema = Schema(implementation = AnalysisStatus::class))
        @RequestParam(required = false) analysisStatus: AnalysisStatus?,
        @Parameter(description = "页码 (从1开始)", example = "1")
        @RequestParam(defaultValue = "1") page: Int,
        @Parameter(description = "每页大小", example = "20")
        @RequestParam(defaultValue = "20") size: Int
    ): ResponseEntity<ApiResponse<ScriptListResponse>> {
        logger.info("u1v2w3x4 | 查询脚本列表: page=$page, size=$size, scriptName=$scriptName")

        return try {
            val result = scriptService.queryScripts(
                scriptName = scriptName,
                uploadUser = uploadUser,
                startDate = startDate,
                endDate = endDate,
                scriptType = scriptType,
                analysisStatus = analysisStatus,
                page = if (page > 0) page - 1 else 0, // API层面page从1开始，service层面从0开始
                size = size
            )

            val response = ScriptListResponse(
                content = result.content.map { script ->
                    ScriptSummary(
                        id = script.id!!,
                        scriptName = script.scriptName,
                        scriptType = script.scriptType,
                        fileSize = script.fileSize,
                        uploadUser = script.uploadUser,
                        analysisStatus = script.analysisStatus,
                        createdAt = script.createdAt,
                        updatedAt = script.updatedAt
                    )
                },
                page = result.page,
                size = result.size,
                totalElements = result.totalElements,
                totalPages = result.totalPages
            )

            logger.info("y5z6a7b8 | 脚本列表查询成功: 返回${response.content.size}条记录")

            ResponseEntity.ok(
                ApiResponse.success(
                    data = response,
                    message = "查询成功 (Query successful)"
                )
            )

        } catch (e: IllegalArgumentException) {
            logger.warn("c9d0e1f2 | 脚本列表查询参数错误", e)
            ResponseEntity.badRequest()
                .body(ApiResponse.error("查询参数错误: ${e.message} (Invalid query parameters)"))

        } catch (e: Exception) {
            logger.error("g3h4i5j6 | 脚本列表查询时发生错误", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("查询失败: ${e.message} (Query failed)"))
        }
    }

    /**
     * UC-4: 删除已上传的脚本 (Delete Uploaded Script)
     * 
     * 通过API使用脚本ID来删除一个已上传的脚本及其元数据记录
     * 
     * @param scriptId 脚本ID
     * @return 删除结果
     */
    @Operation(summary = "删除已上传的脚本 (UC-4)", description = "根据脚本ID删除一个已上传的脚本及其元数据")
    @DeleteMapping("/scripts/{scriptId}")
    fun deleteScript(
        @Parameter(description = "要删除的脚本ID", example = "101", required = true)
        @PathVariable scriptId: Long
    ): ResponseEntity<ApiResponse<Unit>> {
        logger.info("k7l8m9n0 | 接收到删除脚本请求: scriptId=$scriptId")

        return try {
            val result = scriptService.deleteScript(scriptId)

            if (result.success) {
                logger.info("o1p2q3r4 | 脚本删除成功: scriptId=$scriptId")
                ResponseEntity.ok(
                    ApiResponse.success(
                        data = Unit,
                        message = result.message
                    )
                )
            } else {
                if (result.message.contains("脚本不存在") || result.message.contains("not found")) {
                    ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(ApiResponse.error(result.message))
                } else {
                    ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(ApiResponse.error(result.message))
                }
            }

        } catch (e: Exception) {
            logger.error("s5t6u7v8 | 删除脚本时发生错误: scriptId=$scriptId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("删除失败: ${e.message} (Delete failed)"))
        }
    }

    /**
     * UC-5: 下载原始脚本文件 (Download Original Script File)
     * 
     * 通过API使用脚本ID下载原始的脚本文件
     * 
     * @param scriptId 脚本ID
     * @return 脚本文件内容
     */
    @Operation(summary = "下载原始脚本文件 (UC-5)", description = "根据脚本ID下载原始的脚本文件")
    @GetMapping("/scripts/{scriptId}/download")
    fun downloadScript(
        @Parameter(description = "要下载的脚本ID", example = "102", required = true)
        @PathVariable scriptId: Long
    ): ResponseEntity<ByteArray> {
        logger.info("w9x0y1z2 | 接收到下载脚本请求: scriptId=$scriptId")

        return try {
            val script = scriptService.getScriptById(scriptId)

            if (script == null) {
                logger.warn("a3b4c5d6 | 脚本不存在: scriptId=$scriptId")
                return ResponseEntity.notFound().build()
            }

            // 确定内容类型 (Determine content type)
            val contentType = when (script.scriptType) {
                ScriptType.SQL -> "application/sql"
                ScriptType.SHELL -> "application/x-sh"
            }

            // 设置响应头 (Set response headers)
            val headers = HttpHeaders().apply {
                add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"${script.scriptName}\"")
                add(HttpHeaders.CONTENT_TYPE, contentType)
            }

            logger.info("e7f8g9h0 | 脚本下载成功: scriptId=$scriptId, fileName=${script.scriptName}")

            ResponseEntity.ok()
                .headers(headers)
                .body(script.scriptContent.toByteArray())

        } catch (e: Exception) {
            logger.error("i1j2k3l4 | 下载脚本时发生错误: scriptId=$scriptId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()
        }
    }

    /**
     * 获取脚本详情 (Get script details)
     * 
     * @param scriptId 脚本ID
     * @return 脚本详细信息
     */
    @GetMapping("/scripts/{scriptId}")
    fun getScriptDetails(
        @Parameter(description = "脚本ID", example = "103", required = true)
        @PathVariable scriptId: Long
    ): ResponseEntity<ApiResponse<ScriptDetailResponse>> {
        logger.info("m5n6o7p8 | 查询脚本详情: scriptId=$scriptId")

        return try {
            val script = scriptService.getScriptById(scriptId)

            if (script == null) {
                logger.warn("q9r0s1t2 | 脚本不存在: scriptId=$scriptId")
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("脚本不存在 (Script not found)"))
            }

            val response = ScriptDetailResponse(
                id = script.id!!,
                scriptName = script.scriptName,
                scriptType = script.scriptType,
                filePath = script.filePath,
                fileSize = script.fileSize,
                fileHash = script.fileHash,
                scriptContent = script.scriptContent,
                uploadUser = script.uploadUser,
                analysisStatus = script.analysisStatus,
                analysisResult = script.analysisResult,
                temporaryLineageId = script.temporaryLineageId,
                createdAt = script.createdAt,
                updatedAt = script.updatedAt
            )

            logger.info("u3v4w5x6 | 脚本详情查询成功: scriptId=$scriptId")

            ResponseEntity.ok(
                ApiResponse.success(
                    data = response,
                    message = "查询成功 (Query successful)"
                )
            )

        } catch (e: Exception) {
            logger.error("y7z8a9b0 | 查询脚本详情时发生错误: scriptId=$scriptId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("查询失败: ${e.message} (Query failed)"))
        }
    }

    /**
     * UC-3: 获取脚本影响分析结果 (Get Script Impact Analysis Result)
     * 
     * 通过API为一个指定的脚本ID请求影响分析，
     * 获得一个结构化的数据，该数据描述了完整的血缘关系图谱。
     * 
     * @param scriptId 脚本ID
     * @return 脚本影响分析结果
     */
    @Operation(summary = "获取脚本影响分析结果 (UC-3)", description = "根据脚本ID获取其影响分析结果，包括血缘关系图谱")
    @GetMapping("/scripts/{scriptId}/analysis")
    fun getScriptImpactAnalysis(
        @Parameter(description = "脚本ID", example = "104", required = true)
        @PathVariable scriptId: Long
    ): ResponseEntity<ApiResponse<ScriptImpactAnalysisResponse>> {
        logger.info("c1d2e3f4 | 获取脚本影响分析结果: scriptId=$scriptId")

        return try {
            // 1. 获取脚本详情
            val script = scriptService.getScriptById(scriptId)
            if (script == null) {
                logger.warn("g5h6i7j8 | 脚本不存在: scriptId=$scriptId")
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("脚本不存在 (Script not found)"))
            }

            // 2. 检查分析状态
            when (script.analysisStatus) {
                AnalysisStatus.PENDING -> {
                    logger.info("k9l0m1n2 | 脚本分析尚未开始: scriptId=$scriptId")
                    return ResponseEntity.ok()
                        .body(ApiResponse.error("脚本分析尚未开始，请稍后再试 (Script analysis not yet started, please try again later)"))
                }
                AnalysisStatus.ANALYZING -> {
                    logger.info("o3p4q5r6 | 脚本正在分析中: scriptId=$scriptId")
                    return ResponseEntity.ok()
                        .body(ApiResponse.error("脚本正在分析中，请稍后再试 (Script analysis in progress, please try again later)"))
                }
                AnalysisStatus.FAILED -> {
                    logger.warn("s7t8u9v0 | 脚本分析失败: scriptId=$scriptId")
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(ApiResponse.error("脚本分析失败: ${script.analysisResult ?: "未知错误"} (Script analysis failed)"))
                }
                AnalysisStatus.COMPLETED -> {
                    // 继续处理已完成的分析结果
                }
            }

            // 3. 解析分析结果
            if (script.analysisResult.isNullOrBlank()) {
                logger.error("f8bf0796 | 分析结果为空: scriptId=$scriptId")
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("分析结果为空 (Analysis result is empty)"))
            }

            // 4. 构建响应
            val response = ScriptImpactAnalysisResponse(
                scriptId = script.id!!,
                scriptName = script.scriptName,
                scriptType = script.scriptType,
                analysisStatus = script.analysisStatus,
                analysisResult = script.analysisResult,
                temporaryLineageId = script.temporaryLineageId,
                analyzedAt = script.updatedAt
            )

            logger.info("a5b6c7d8 | 脚本影响分析结果获取成功: scriptId=$scriptId")

            ResponseEntity.ok(
                ApiResponse.success(
                    data = response,
                    message = "分析结果获取成功 (Analysis result retrieved successfully)"
                )
            )

        } catch (e: Exception) {
            logger.error("e9f0g1h2 | 获取脚本影响分析结果时发生错误: scriptId=$scriptId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取分析结果失败: ${e.message} (Failed to get analysis result)"))
        }
    }

    /**
     * 手动触发脚本分析 (Manually trigger script analysis)
     * 
     * 提供一个显式的API端点来触发脚本分析，作为UC-3的补充选项
     * 
     * @param scriptId 脚本ID
     * @return 触发结果
     */
    @Operation(summary = "手动触发脚本分析", description = "提供一个显式的API端点来触发指定脚本ID的分析过程")
    @PostMapping("/scripts/{scriptId}/analyze")
    fun triggerScriptAnalysis(
        @Parameter(description = "要触发分析的脚本ID", example = "105", required = true)
        @PathVariable scriptId: Long
    ): ResponseEntity<ApiResponse<ScriptAnalysisTriggerResponse>> {
        logger.info("i3j4k5l6 | 手动触发脚本分析: scriptId=$scriptId")

        return try {
            // 1. 检查脚本是否存在
            val script = scriptService.getScriptById(scriptId)
            if (script == null) {
                logger.warn("m7n8o9p0 | 脚本不存在: scriptId=$scriptId")
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("脚本不存在 (Script not found)"))
            }

            // 2. 检查当前状态是否允许重新分析
            if (script.analysisStatus == AnalysisStatus.ANALYZING) {
                logger.warn("q1r2s3t4 | 脚本正在分析中，无法重复触发: scriptId=$scriptId")
                return ResponseEntity.ok()
                    .body(ApiResponse.error("脚本正在分析中，请稍后再试 (Script analysis already in progress)"))
            }

            // 3. 触发分析
            scriptAnalysisService.triggerScriptAnalysis(scriptId)

            val response = ScriptAnalysisTriggerResponse(
                scriptId = scriptId,
                message = "分析已触发，请稍后查询结果 (Analysis triggered, please check result later)",
                triggeredAt = java.time.LocalDateTime.now()
            )

            logger.info("u5v6w7x8 | 脚本分析触发成功: scriptId=$scriptId")

            ResponseEntity.ok()
                .body(ApiResponse.success(
                    data = response,
                    message = "分析触发成功 (Analysis triggered successfully)"
                ))

        } catch (e: Exception) {
            logger.error("y9z0a1b2 | 触发脚本分析时发生错误: scriptId=$scriptId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("触发分析失败: ${e.message} (Failed to trigger analysis)"))
        }
    }
} 