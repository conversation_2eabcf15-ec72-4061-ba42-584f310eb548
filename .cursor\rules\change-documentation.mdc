---
description: 
globs: 
alwaysApply: true
---
# Change Documentation Rule

## Requirement
After every **non-trivial change** to the codebase, create a Markdown file that documents:

1. **What** was changed (summary of the modification)
2. **Why** the change was necessary (reasoning / context)
3. **How** it was implemented (key steps or design decisions)
4. Any **follow-up tasks** or TODOs

## File Naming Convention
Name the file using the pattern:

```
<YYYY-MM-DD>-<short_change_description>.md
```

*Example*: `2025-06-19-manual-lineage-upsert-api.md`

## Location
Place the file in the project root or inside a dedicated `docs/changes` directory.

## Benefits
- Provides an easily navigable change history
- Improves onboarding by explaining the rationale behind modifications
- Aids in debugging and future refactoring efforts

## Enforcement
Reviewers should verify that a corresponding change-log Markdown file exists for every pull request containing non-trivial changes.

