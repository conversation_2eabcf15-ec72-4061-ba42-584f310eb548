package com.datayes.metadata

/**
 * 系统关系 DTO (System Relationship DTO)
 * 
 * 表示两个元数据系统之间的血缘关系
 */
data class SystemRelationshipDto(
    val sourceSystemId: Long,
    val targetSystemId: Long,
    val isCyclicReference: Boolean,
    val relationshipCount: Int = 1,
    val sourceSystemInfo: MetadataSystemInfoDto? = null,
    val targetSystemInfo: MetadataSystemInfoDto? = null
)

/**
 * 系统关系响应 DTO (System Relationships Response DTO)
 * 
 * 包含系统关系列表和统计信息
 */
data class SystemRelationshipsResponseDto(
    val systemRelationships: List<SystemRelationshipDto>,
    val totalRelationships: Int,
    val totalSystems: Int,
    val cyclicReferencesCount: Int,
    val metadata: SystemRelationshipMetadataDto
)

/**
 * 系统关系元数据 DTO (System Relationship Metadata DTO)
 * 
 * 包含查询的元数据信息
 */
data class SystemRelationshipMetadataDto(
    val queryTimestamp: String,
    val dataSource: String = "lineage_relationships",
    val includeCyclicDetection: Boolean = true,
    val systemMappingMethod: String = "metadata_datasource_matching"
)