# 血缘任务管理系统 PRD
## Product Requirements Document for Lineage Task Management System

### 📋 文档信息 (Document Information)
- **产品名称**: 血缘任务管理系统 (Lineage Task Management System)
- **版本**: v1.0
- **创建日期**: 2024-12-19
- **文档类型**: 产品需求文档 (Product Requirements Document)

---

## 🎯 产品概述 (Product Overview)

### 背景 (Background)
当前系统通过 `EnhancedDataExchangeJobService` 处理数据交换作业（DataExchangeJob）的血缘分析，但缺乏统一的任务管理机制。需要建立血缘任务管理系统，将血缘处理过程标准化、可追踪、可重复执行。

### 目标 (Objectives)
1. **标准化血缘处理流程** - 将每个 DataExchangeJob 的血缘处理标准化为可管理的任务
2. **提供任务管理能力** - 支持任务的查询、重跑、状态跟踪
3. **增强可观测性** - 提供完整的任务执行历史和状态监控
4. **支持批量处理** - 一键处理所有活跃的数据交换作业

---

## 🚀 核心功能 (Core Features)

### 1. 批量血缘处理 API (Batch Lineage Processing)

#### 功能描述
提供 REST API 支持批量处理所有活跃的 DataExchangeJob，自动创建/更新对应的血缘任务（lineage_tasks）。

#### 接口规格
```http
POST /api/v1/lineage/tasks/process-all
Content-Type: application/json

{
  "executedBy": "system|username",
  "batchId": "optional-batch-identifier"
}
```

#### 响应格式
```json
{
  "success": true,
  "data": {
    "batchId": "batch_20241219_001",
    "totalJobs": 25,
    "processedTasks": [
      {
        "taskId": 1001,
        "jobKey": "reader_123_writer_456",
        "taskName": "数据同步任务-表A到表B",
        "status": "SUCCESS",
        "processingTimeMs": 1250,
        "hasChanges": true
      }
    ],
    "summary": {
      "successful": 23,
      "failed": 2,
      "unchanged": 18,
      "updated": 5
    }
  },
  "message": "批量处理完成"
}
```

#### 业务规则
- **任务创建策略**: 每个 DataExchangeJob 对应一个 lineage_task 记录
- **任务标识**: 使用 `{readerJobId}_{writeJobId}` 作为唯一标识符
- **重复处理**: 如果任务已存在，更新执行状态和时间
- **错误处理**: 单个任务失败不影响整体批处理流程

### 2. 血缘任务查询 API (Lineage Tasks Query)

#### 功能描述
提供分页查询血缘任务列表，支持多维度过滤和排序。

#### 接口规格
```http
GET /api/v1/lineage/tasks?page=1&size=20&status=SUCCESS&taskType=DATA_EXCHANGE_PLATFORM&sort=createdAt,desc
```

#### 查询参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| page | Integer | 否 | 页码，默认1 |
| size | Integer | 否 | 页大小，默认20，最大100 |
| status | String | 否 | 任务状态过滤 |
| taskType | String | 否 | 任务类型过滤 |
| taskName | String | 否 | 任务名称模糊搜索 |
| createdBy | String | 否 | 创建人过滤 |
| dateFrom | String | 否 | 创建时间起始(yyyy-MM-dd) |
| dateTo | String | 否 | 创建时间结束(yyyy-MM-dd) |
| sort | String | 否 | 排序字段和方向 |

#### 响应格式
```json
{
  "success": true,
  "data": {
    "content": [
      {
        "id": 1001,
        "taskName": "数据同步任务-用户表到分析表",
        "taskType": "DATA_EXCHANGE_PLATFORM",
        "sourceType": "DATA_EXCHANGE_JOB",
        "sourceIdentifier": "reader_123_writer_456",
        "status": "SUCCESS",
        "scheduleType": "MANUAL",
        "isEnabled": true,
        "createdBy": "system",
        "executedAt": "2024-12-19T10:30:00",
        "completedAt": "2024-12-19T10:30:02",
        "processingTimeMs": 2150,
        "createdAt": "2024-12-19T10:30:00",
        "updatedAt": "2024-12-19T10:30:02"
      }
    ],
    "pageable": {
      "page": 1,
      "size": 20,
      "totalElements": 125,
      "totalPages": 7
    }
  }
}
```

### 3. 血缘任务重跑 API (Lineage Task Rerun)

#### 功能描述
支持重新执行指定的血缘任务，重新分析并更新血缘关系。

#### 接口规格
```http
POST /api/v1/lineage/tasks/{taskId}/rerun
Content-Type: application/json

{
  "executedBy": "username",
  "reason": "数据源结构变更，需重新分析血缘"
}
```

#### 响应格式
```json
{
  "success": true,
  "data": {
    "taskId": 1001,
    "newExecutionId": "exec_20241219_001",
    "status": "RUNNING",
    "message": "任务重跑已启动"
  }
}
```

#### 业务规则
- **状态检查**: 只有非 RUNNING 状态的任务才能重跑
- **数据更新**: 重跑会更新任务的执行时间、状态和结果
- **血缘更新**: 重新执行血缘分析，可能产生新的血缘关系
- **历史保留**: 保留历史执行记录，支持血缘变更追踪

---

## 📊 数据模型设计 (Data Model Design)

### lineage_tasks 表结构增强
基于现有的 `lineage_tasks` 表，需要增加以下字段来支持新功能：

```sql
-- 增加字段以支持任务管理功能
ALTER TABLE lineage_tasks ADD COLUMN IF NOT EXISTS job_key VARCHAR(500) COMMENT '作业唯一标识符';
ALTER TABLE lineage_tasks ADD COLUMN IF NOT EXISTS processing_time_ms BIGINT COMMENT '处理耗时(毫秒)';
ALTER TABLE lineage_tasks ADD COLUMN IF NOT EXISTS has_changes BOOLEAN DEFAULT FALSE COMMENT '是否有血缘变更';
ALTER TABLE lineage_tasks ADD COLUMN IF NOT EXISTS batch_id VARCHAR(100) COMMENT '批处理标识';
ALTER TABLE lineage_tasks ADD COLUMN IF NOT EXISTS execution_count INT DEFAULT 1 COMMENT '执行次数';
ALTER TABLE lineage_tasks ADD COLUMN IF NOT EXISTS last_execution_id VARCHAR(100) COMMENT '最后执行ID';

-- 添加索引
CREATE INDEX IF NOT EXISTS idx_lineage_tasks_job_key ON lineage_tasks(job_key);
CREATE INDEX IF NOT EXISTS idx_lineage_tasks_batch_id ON lineage_tasks(batch_id);
```

### 任务状态枚举
- **PENDING**: 待执行
- **RUNNING**: 执行中  
- **SUCCESS**: 执行成功
- **FAILED**: 执行失败
- **CANCELLED**: 已取消

---

## 🎨 实现架构 (Implementation Architecture)

### 核心组件设计

#### 1. LineageTaskEntity (血缘任务实体)
```kotlin
@Entity
@Table(name = "lineage_tasks")
data class LineageTaskEntity(
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    val id: Long = 0,
    
    @Column(name = "task_name")
    val taskName: String,
    
    @Column(name = "job_key")
    val jobKey: String,
    
    @Enumerated(EnumType.STRING)
    @Column(name = "task_status")
    val status: TaskStatus,
    
    @Column(name = "processing_time_ms")
    val processingTimeMs: Long? = null,
    
    @Column(name = "has_changes")
    val hasChanges: Boolean = false,
    
    // ... 其他字段
)
```

#### 2. LineageTaskRepository (任务仓库)
```kotlin
@Repository
interface LineageTaskRepository : JpaRepository<LineageTaskEntity, Long> {
    fun findByJobKey(jobKey: String): LineageTaskEntity?
    fun findByStatusIn(statuses: List<TaskStatus>): List<LineageTaskEntity>
    // ... 自定义查询方法
}
```

#### 3. LineageTaskService (任务服务)
```kotlin
@Service
class LineageTaskService(
    private val taskRepository: LineageTaskRepository,
    private val enhancedDataExchangeJobService: EnhancedDataExchangeJobService
) {
    fun processAllActiveJobs(): BatchProcessResult
    fun rerunTask(taskId: Long, request: RerunRequest): TaskExecutionResult
    fun findTasks(criteria: TaskQueryCriteria): Page<LineageTaskEntity>
}
```

#### 4. LineageTaskController (任务控制器)
```kotlin
@RestController
@RequestMapping("/api/v1/lineage/tasks")
class LineageTaskController(
    private val lineageTaskService: LineageTaskService
) {
    @PostMapping("/process-all")
    fun processAllActiveTasks(@RequestBody request: BatchProcessRequest): ResponseEntity<ApiResponse<BatchProcessResult>>
    
    @GetMapping
    fun getTasks(@ModelAttribute criteria: TaskQueryCriteria): ResponseEntity<ApiResponse<Page<LineageTaskDto>>>
    
    @PostMapping("/{taskId}/rerun")
    fun rerunTask(@PathVariable taskId: Long, @RequestBody request: RerunRequest): ResponseEntity<ApiResponse<TaskExecutionResult>>
}
```

---

## ✅ 验收标准 (Acceptance Criteria)

### 功能验收
1. **批量处理API**
   - [ ] 能够处理所有活跃的 DataExchangeJob
   - [ ] 为每个作业创建或更新对应的 lineage_task 记录
   - [ ] 返回详细的处理结果和统计信息
   - [ ] 支持并发处理，性能满足要求

2. **任务查询API**
   - [ ] 支持分页查询，默认页大小20，最大100
   - [ ] 支持多字段过滤：状态、类型、名称、创建人、时间范围
   - [ ] 支持多字段排序
   - [ ] 响应时间 < 500ms（100条记录内）

3. **任务重跑API**
   - [ ] 只允许非RUNNING状态的任务重跑
   - [ ] 重跑后更新任务状态和执行时间
   - [ ] 重新执行血缘分析并更新关系
   - [ ] 保留执行历史记录

### 性能要求
- **批量处理**: 100个作业处理时间 < 30秒
- **查询响应**: 普通查询 < 200ms，复杂查询 < 500ms  
- **重跑响应**: 任务提交 < 100ms，实际执行异步进行

### 安全要求
- **接口鉴权**: 所有API需要有效的认证令牌
- **操作审计**: 记录所有操作的用户、时间和操作内容
- **数据隔离**: 确保用户只能访问授权范围内的数据

---

## 🚦 实施计划 (Implementation Plan)

### Phase 1: 核心实体和仓库 (第1周)
- [ ] 创建 LineageTaskEntity 实体类
- [ ] 实现 LineageTaskRepository 仓库接口
- [ ] 数据库表结构调整和索引优化

### Phase 2: 服务层实现 (第2周)  
- [ ] 实现 LineageTaskService 核心业务逻辑
- [ ] 集成 EnhancedDataExchangeJobService
- [ ] 实现批量处理和重跑逻辑

### Phase 3: API接口开发 (第3周)
- [ ] 实现 LineageTaskController REST接口
- [ ] 添加请求验证和异常处理
- [ ] 编写API文档

### Phase 4: 测试和优化 (第4周)
- [ ] 单元测试和集成测试
- [ ] 性能测试和优化
- [ ] 代码审查和重构

---

## 📈 成功指标 (Success Metrics)

### 业务指标
- **任务处理成功率**: ≥ 95%
- **用户满意度**: ≥ 4.5/5.0
- **功能使用率**: ≥ 80% 的用户使用新功能

### 技术指标  
- **API可用性**: ≥ 99.5%
- **平均响应时间**: < 300ms
- **系统错误率**: < 0.5%

### 运维指标
- **问题解决时间**: < 2小时
- **部署成功率**: ≥ 98%
- **监控覆盖率**: 100%

---

## 🔮 未来规划 (Future Roadmap)

### 短期增强 (3个月内)
- **定时调度**: 支持 Cron 表达式的定时血缘分析
- **任务优先级**: 支持任务优先级设置和队列管理
- **通知机制**: 任务完成/失败的邮件和webhook通知

### 中期发展 (6个月内)
- **可视化界面**: Web界面的任务管理和监控
- **血缘比对**: 血缘变更的可视化对比和审批
- **性能优化**: 基于历史数据的智能调度优化

### 长期愿景 (1年内)
- **AI辅助**: 基于机器学习的血缘质量评估
- **多租户支持**: 企业级的多租户数据隔离
- **开放平台**: API网关和第三方系统集成

---

*本文档遵循数据为中心的编程原则，优先关注数据结构设计和业务逻辑的清晰表达，确保系统的可维护性和可扩展性。* 