package com.datayes.integration

import io.restassured.RestAssured.given
import org.assertj.core.api.Assertions.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.Test
import io.restassured.path.json.JsonPath

/**
 * 元数据系统血缘关系集成测试 (Metadata Systems in Relationships Integration Test)
 * 
 * 测试获取参与血缘关系的系统信息API
 */
class MetadataSystemsInRelationshipsIT : RestApiIntegrationTestBase() {

    @Test
    fun `should return systems that participate in lineage relationships`() {
        println("8e5f2c7a | 开始测试获取参与血缘关系的系统信息API")
        
        val response = given()
            .`when`()
            .get("/v1/metadata/systems/in-relationships")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("message", equalTo("查询成功"))
            .body("data", notNullValue())
            .extract()
            .response()
        
        val jsonPath = JsonPath.from(response.asString())
        val systems = jsonPath.getList<Map<String, Any>>("data")
        
        println("3b7d9f1e | 返回的系统数量: ${systems.size}")
        
        // 验证返回的数据结构
        if (systems.isNotEmpty()) {
            val firstSystem = systems[0]
            
            // 验证必需字段存在
            assertThat(firstSystem).containsKeys("id", "systemName")
            
            // 验证字段类型
            val systemId = firstSystem["id"]
            val systemName = firstSystem["systemName"]
            val systemAbbreviation = firstSystem["systemAbbreviation"]
            
            assertThat(systemId).isInstanceOf(Number::class.java)
            assertThat(systemName).isInstanceOf(String::class.java)
            
            println("4c8e0g2i | 第一个系统信息: id=$systemId, systemName=$systemName, systemAbbreviation=$systemAbbreviation")
            
            // 验证系统名称不为空
            assertThat(systemName.toString()).isNotBlank()
            
            // 验证所有系统都有ID和系统名称
            systems.forEach { system ->
                assertThat(system["id"]).isNotNull()
                assertThat(system["systemName"]).isNotNull()
                assertThat(system["systemName"].toString()).isNotBlank()
            }
        } else {
            println("6j4k8l2m | 当前没有参与血缘关系的系统")
        }
        
        println("9n7o5p3q | 测试完成，API响应正常")
    }
    
    @Test
    fun `should return valid response structure`() {
        println("1r5s9t3u | 开始测试响应结构")
        
        given()
            .`when`()
            .get("/v1/metadata/systems/in-relationships")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("message", notNullValue())
            .body("timestamp", notNullValue())
            
        println("7v1w5x9y | 响应结构验证通过")
    }
}