package com.datayes.dataexchange

import com.datayes.lineage.JobProcessingHistory
import com.datayes.lineage.JobProcessingHistoryRepository
import com.datayes.lineage.JobType
import com.datayes.lineage.LineageChangeDetectionService
import com.datayes.lineage.LineageRepository
import com.datayes.lineage.LineageResult
import com.datayes.lineage.ProcessingResult
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

/**
 * 增强的数据交互作业服务 (Enhanced Data Exchange Job Service)
 */
@Service
class DataExchangeJobService(
    private val lineageRepository: LineageRepository,
    private val changeDetectionService: LineageChangeDetectionService,
    private val processingHistoryRepository: JobProcessingHistoryRepository,
    private val dataExchangeJobRepository: DataExchangeJobRepository,
) {

    private val logger = LoggerFactory.getLogger(DataExchangeJobService::class.java)

    /**
     * 处理所有活动作业的血缘 (Process lineage for all active jobs)
     */
    fun processAllActiveJobsLineage(): List<LineageProcessResult> {
        val listActiveDataExchangeJobs = dataExchangeJobRepository.listActiveDataExchangeJobs()
        val lineageProcessResults = mutableListOf<LineageProcessResult>()
        for (job in listActiveDataExchangeJobs) {
            val result = processJobLineageWithChangeDetection(job)
            lineageProcessResults.add(result)
        }
        return lineageProcessResults
    }

    /**
     * 根据数据源ID过滤数据交互作业 (Filter data exchange jobs by datasource ID)
     * 
     * 通过匹配数据交互作业的连接字符串与血缘数据源的连接字符串来过滤作业
     * 
     * @param datasourceId 血缘数据源ID
     * @return 匹配的数据交互作业列表
     */
    fun findDataExchangeJobsByDatasourceId(datasourceId: Long): List<DataExchangeJob> {
        logger.info("2a8f4e9c | 开始根据数据源ID过滤数据交互作业: datasourceId=$datasourceId")
        
        // 1. 获取指定的血缘数据源信息
        val datasource = lineageRepository.findDatasourceById(datasourceId)
        if (datasource == null) {
            logger.warn("5b7d3f1a | 未找到指定的数据源: datasourceId=$datasourceId")
            return emptyList()
        }
        
        logger.info("8c4e6b2d | 找到数据源: name=${datasource.datasourceName}, connectionString=${datasource.connectionString}")
        
        // 2. 获取所有活跃的数据交互作业
        val allJobs = dataExchangeJobRepository.listActiveDataExchangeJobs()
        logger.info("3f9a7e5b | 获取到${allJobs.size}个活跃的数据交互作业")
        
        // 3. 过滤匹配的作业
        val matchedJobs = allJobs.filter { job ->
            isConnectionStringMatched(datasource.connectionString, job.dbReader) ||
            isConnectionStringMatched(datasource.connectionString, job.dbWriter)
        }
        
        logger.info("6e2d8f4c | 过滤完成: 找到${matchedJobs.size}个匹配的数据交互作业")
        
        return matchedJobs
    }
    
    /**
     * 检查两个连接字符串是否匹配 (Check if two connection strings match)
     * 
     * 通过比较JDBC URL的关键组件（协议、主机、端口、数据库名）来判断是否匹配
     * 
     * @param datasourceConnectionString 血缘数据源的连接字符串
     * @param jobConnectionString 数据交互作业的连接字符串
     * @return 如果匹配则返回true，否则返回false
     */
    private fun isConnectionStringMatched(
        datasourceConnectionString: String,
        jobConnectionString: String
    ): Boolean {
        return try {
            // 直接字符串比较（最精确）
            if (datasourceConnectionString.equals(jobConnectionString, ignoreCase = true)) {
                return true
            }
            
            // 提取并比较关键组件
            val datasourceComponents = parseJdbcUrl(datasourceConnectionString)
            val jobComponents = parseJdbcUrl(jobConnectionString)
            
            // 比较协议、主机、端口、数据库名
            datasourceComponents != null && jobComponents != null &&
                    datasourceComponents.protocol.equals(jobComponents.protocol, ignoreCase = true) &&
                    datasourceComponents.host.equals(jobComponents.host, ignoreCase = true) &&
                    datasourceComponents.port == jobComponents.port &&
                    datasourceComponents.database.equals(jobComponents.database, ignoreCase = true)
            
        } catch (e: Exception) {
            logger.debug("1d5c9f7a | 连接字符串匹配时发生异常: datasource=$datasourceConnectionString, job=$jobConnectionString", e)
            false
        }
    }
    
    /**
     * 解析JDBC URL的关键组件 (Parse key components from JDBC URL)
     * 
     * @param jdbcUrl JDBC连接字符串
     * @return 解析出的组件，如果解析失败则返回null
     */
    private fun parseJdbcUrl(jdbcUrl: String): JdbcUrlComponents? {
        return try {
            val url = jdbcUrl.lowercase()
            
            when {
                url.startsWith("jdbc:mysql://") -> {
                    val pattern = Regex("jdbc:mysql://([^:]+):(\\d+)/(.+)")
                    val match = pattern.find(url)
                    match?.let {
                        JdbcUrlComponents("mysql", it.groupValues[1], it.groupValues[2].toInt(), it.groupValues[3])
                    }
                }
                url.startsWith("jdbc:oracle:thin:@//") -> {
                    val pattern = Regex("jdbc:oracle:thin:@//([^:]+):(\\d+)/(.+)")
                    val match = pattern.find(url)
                    match?.let {
                        JdbcUrlComponents("oracle", it.groupValues[1], it.groupValues[2].toInt(), it.groupValues[3])
                    }
                }
                url.startsWith("jdbc:hive2://") -> {
                    // Handle Hive2 URLs like: ******************************* or ***********************,host2:port/database
                    val pattern = Regex("jdbc:hive2://([^/]+)/(\\w+)")
                    val match = pattern.find(url)
                    match?.let {
                        val hostPort = it.groupValues[1].split(",")[0] // Take first host if multiple
                        val hostPortParts = hostPort.split(":")
                        val host = hostPortParts[0]
                        val port = if (hostPortParts.size > 1) hostPortParts[1].toInt() else 10000
                        JdbcUrlComponents("hive2", host, port, it.groupValues[2])
                    }
                }
                else -> null
            }
        } catch (e: Exception) {
            logger.debug("4a6e2f8d | 解析JDBC URL时发生异常: $jdbcUrl", e)
            null
        }
    }


    /**
     * 智能处理作业血缘 (只在有变更时更新数据库)
     */
    fun processJobLineageWithChangeDetection(job: DataExchangeJob, taskId: Long? = null): LineageProcessResult {
        val startTime = System.currentTimeMillis()
        val jobKey = "${job.readerJobId}_${job.writeJobId}"

        return try {
            // 1. 转换为血缘信息
            val lineageResult = DataExchangeJobLineageConverter.convertToLineage(job)

            if (!lineageResult.success) {
                return createFailureResult(job, lineageResult, startTime, "血缘转换失败")
            }

            val dataLineage = lineageResult.lineage!!

            // 2. 检测变更
            val changeDetection = changeDetectionService.detectChanges(jobKey, dataLineage)

            val processingResult = if (changeDetection.hasChanges) {
                // 3a. 有变更：更新数据库
                logger.info("cef0b830 | 检测到血缘变更，更新数据库: $jobKey")
                lineageRepository.updateLineageInDatabase(jobKey, dataLineage, taskId)
                ProcessingResult.UPDATED
            } else {
                // 3b. 无变更：仅更新处理时间
                logger.info("a0024b24 | 未检测到血缘变更，跳过数据库更新: $jobKey")
                ProcessingResult.NO_CHANGE
            }

            // 4. 记录处理历史
            recordProcessingHistory(
                jobKey = jobKey,
                job = job,
                result = processingResult,
                lineageHash = changeDetection.currentHash,
                processingTime = System.currentTimeMillis() - startTime
            )

            LineageProcessResult(
                job = job,
                lineageResult = lineageResult,
                processingTimeMs = System.currentTimeMillis() - startTime,
                hasChanges = changeDetection.hasChanges,
                processingResult = processingResult
            )

        } catch (e: Exception) {
            logger.error("16ad855c | 处理作业血缘时发生异常: $jobKey", e)
            createFailureResult(job, null, startTime, "处理异常: ${e.message}")
        }
    }


    /**
     * 记录处理历史
     */
    private fun recordProcessingHistory(
        jobKey: String,
        job: DataExchangeJob,
        result: ProcessingResult,
        lineageHash: String,
        processingTime: Long
    ) {
        val history = JobProcessingHistory(
            jobKey = jobKey,
            jobType = JobType.DATA_EXCHANGE,
            readerJobId = job.readerJobId,
            writeJobId = job.writeJobId,
            processingResult = result,
            changesDetected = result == ProcessingResult.UPDATED,
            processingDurationMs = processingTime,
            lineageHash = lineageHash
        )

        processingHistoryRepository.save(history)
    }

    private fun createFailureResult(
        job: DataExchangeJob,
        lineageResult: LineageResult?,
        startTime: Long,
        errorMessage: String
    ): LineageProcessResult {
        val failureResult = lineageResult ?: LineageResult(
            lineage = null,
            warnings = emptyList(),
            errors = listOf(errorMessage),
            success = false
        )

        return LineageProcessResult(
            job = job,
            lineageResult = failureResult,
            processingTimeMs = System.currentTimeMillis() - startTime,
            hasChanges = false,
            processingResult = ProcessingResult.FAILED
        )
    }
}

/**
 * 增强的血缘处理结果 (Enhanced Lineage Process Result)
 */
data class LineageProcessResult(
    val job: DataExchangeJob,
    val lineageResult: LineageResult,
    val processingTimeMs: Long,
    val hasChanges: Boolean = false,
    val processingResult: ProcessingResult = ProcessingResult.NO_CHANGE
)

/**
 * JDBC URL组件 (JDBC URL Components)
 * 
 * 用于解析和比较JDBC连接字符串的关键组件
 */
data class JdbcUrlComponents(
    val protocol: String,   // mysql, oracle, hive2
    val host: String,       // 主机名或IP
    val port: Int,          // 端口号
    val database: String    // 数据库名
)

/**
 * 血缘数据源详情 (Lineage Datasource Details)
 * 
 * 包含连接字符串的完整数据源信息，用于连接字符串匹配
 */
data class LineageDatasourceDetails(
    val id: Long,
    val datasourceName: String,
    val dbType: String,
    val host: String,
    val port: Int,
    val databaseName: String,
    val connectionString: String,
    val status: String,
    val systemId: Long?
)