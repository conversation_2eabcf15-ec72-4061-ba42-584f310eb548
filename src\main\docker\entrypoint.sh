#!/bin/sh
set -e

# 检查 EXTRA_HOSTS 环境变量是否存在并且不为空
if [ -n "$EXTRA_HOSTS" ]; then
    echo "--- [INFO] entrypoint.sh: Appending extra hosts from EXTRA_HOSTS environment variable ---"
    # 将 EXTRA_HOSTS 的内容追加到 /etc/hosts
    # 使用 printf '%b\n' 来解析换行符 \n，避免在某些 shell 中将 '-e' 当作文本输出
    printf '%b\n' "$EXTRA_HOSTS" >> /etc/hosts
    echo "--- [DEBUG] entrypoint.sh: /etc/hosts content after modification: ---"
    cat /etc/hosts
    echo "--- [DEBUG] entrypoint.sh: /etc/hosts content end ---"
else
    echo "--- [INFO] entrypoint.sh: EXTRA_HOSTS environment variable not set or empty. Skipping host modification. ---"
fi

# 执行原始的 Java 启动命令
# 使用 exec 会让 Java 进程替换当前的 shell 进程，成为 PID 1，以便正确处理信号
exec java -XX:+UseG1GC -Xmx1536m -Xms512m -XX:+ExplicitGCInvokesConcurrent -XX:+ParallelRefProcEnabled -XX:MaxGCPauseMillis=200 -XX:+DisableExplicitGC -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/app/heapdump.hprof -Djava.security.egd=file:/dev/./urandom -Duser.timezone=Asia/Shanghai -jar /app/dgp-lineage-collector-0.0.1-SNAPSHOT.jar
