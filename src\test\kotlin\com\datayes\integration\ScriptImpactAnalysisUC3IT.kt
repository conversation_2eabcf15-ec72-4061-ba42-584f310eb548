package com.datayes.integration

import io.restassured.RestAssured
import io.restassured.RestAssured.given
import io.restassured.http.ContentType
import io.restassured.path.json.JsonPath
import org.assertj.core.api.Assertions.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.*
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.server.LocalServerPort
import java.util.concurrent.TimeUnit

/**
 * UC-3 脚本影响分析专项端到端测试 (UC-3 Script Impact Analysis Dedicated E2E Test)
 *
 * 专门测试 UC-3: 获取脚本影响分析结果的功能，包括：
 * - 分析状态转换 (Analysis state transitions)
 * - 异步分析处理 (Async analysis processing) 
 * - 手动触发分析 (Manual analysis trigger)
 * - 分析结果结构验证 (Analysis result structure validation)
 * - 错误情况处理 (Error case handling)
 *
 * 测试策略 (Testing Strategy):
 * 每个测试都是独立的，包括：
 * 1. 上传测试脚本
 * 2. 执行特定的测试逻辑
 * 3. 清理测试数据
 */
@DisplayName("UC-3: 脚本影响分析专项测试")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class ScriptImpactAnalysisUC3IT {

    companion object {
        private const val TEST_UPLOAD_USER = "uc3-analysis-test-user"
        
        // 复杂的测试SQL，包含多表关联和多种操作
        private val COMPLEX_SQL_CONTENT = """
            -- 测试分析用的复杂SQL脚本
            DROP TABLE IF EXISTS tmp_analysis_result;
            
            CREATE TABLE tmp_analysis_result AS
            SELECT 
                s.store_id,
                s.store_name,
                p.product_id,
                p.product_name,
                c.category_name,
                SUM(f.sales_amount) as total_sales,
                COUNT(f.transaction_id) as transaction_count,
                AVG(f.sales_amount) as avg_sales
            FROM fact_sales f
            INNER JOIN dim_store s ON f.store_id = s.store_id
            INNER JOIN dim_product p ON f.product_id = p.product_id
            INNER JOIN dim_category c ON p.category_id = c.category_id
            LEFT JOIN dim_time t ON f.time_id = t.time_id
            WHERE f.sales_date >= '2024-01-01'
              AND s.store_status = 'ACTIVE'
              AND p.product_status = 'AVAILABLE'
            GROUP BY s.store_id, s.store_name, p.product_id, p.product_name, c.category_name
            HAVING SUM(f.sales_amount) > 1000;
            
            INSERT INTO summary_sales_by_category
            SELECT 
                c.category_id,
                c.category_name,
                SUM(tar.total_sales) as category_total_sales,
                COUNT(DISTINCT tar.store_id) as store_count
            FROM tmp_analysis_result tar
            INNER JOIN dim_category c ON tar.category_name = c.category_name
            GROUP BY c.category_id, c.category_name;
        """.trimIndent()
        
        // 测试Shell脚本，包含Hive SQL
        private val SHELL_SCRIPT_CONTENT = """
            #!/bin/bash
            
            # 设置变量
            export yesterday=$(date -d '1 day ago' '+%Y-%m-%d')
            export hive_db="test_analysis_db"
            
            echo "开始处理日期: ${'$'}yesterday"
            
            # 执行Hive分析
            hive -e "
                USE ${'$'}{hive_db};
                
                INSERT OVERWRITE TABLE daily_sales_summary PARTITION(dt='${'$'}{yesterday}')
                SELECT 
                    store_id,
                    product_id,
                    SUM(sales_amount) as daily_sales,
                    COUNT(*) as transaction_count
                FROM raw_sales_data 
                WHERE sales_date = '${'$'}{yesterday}'
                GROUP BY store_id, product_id;
                
                INSERT INTO sales_trend_analysis
                SELECT 
                    '${'$'}{yesterday}' as analysis_date,
                    dss.store_id,
                    dss.product_id,
                    dss.daily_sales,
                    LAG(dss.daily_sales, 1) OVER (PARTITION BY dss.store_id, dss.product_id ORDER BY dss.dt) as prev_day_sales
                FROM daily_sales_summary dss
                WHERE dss.dt = '${'$'}{yesterday}';
            "
            
            echo "分析完成"
        """.trimIndent()
    }

    @LocalServerPort
    private var port: Int = 0

    @BeforeEach
    fun setupRestAssured() {
        val host = System.getProperty("test.api.host") ?: System.getenv("TEST_API_HOST") ?: "localhost"
        RestAssured.baseURI = "http://$host"
        RestAssured.port = port
        RestAssured.basePath = "/api"
    }

    private fun uploadTestSqlScript(userName: String, scriptFileName: String, scriptContent: String): Pair<Long, String> {
        val tempFile = TestFileUtils.createTempSqlFile(scriptFileName, scriptContent)
        try {
            val response = given()
                .contentType(ContentType.MULTIPART)
                .multiPart("file", tempFile, "application/octet-stream")
                .multiPart("uploadUser", userName)
                .`when`()
                .post("/v1/script-impact/upload")
                .then()
                .statusCode(200)
                .contentType(ContentType.JSON)
                .extract()
                .response()

            val jsonPath = JsonPath.from(response.asString())
            val scriptId = if (jsonPath.getBoolean("success")) {
                // New upload successful
                jsonPath.getLong("data.scriptId")
            } else {
                // File already exists, get the existing script ID from conflict response
                jsonPath.getLong("data.scriptId")
            }
            
            val scriptName = jsonPath.getString("data.scriptName")
            return Pair(scriptId, scriptName)
        } finally {
            tempFile.delete()
        }
    }

    private fun uploadTestShellScript(userName: String, scriptFileName: String, scriptContent: String): Pair<Long, String> {
        val tempFile = TestFileUtils.createTempShellFile(scriptFileName, scriptContent)
        try {
            val response = given()
                .contentType(ContentType.MULTIPART)
                .multiPart("file", tempFile, "application/octet-stream")
                .multiPart("uploadUser", userName)
                .`when`()
                .post("/v1/script-impact/upload")
                .then()
                .statusCode(200)
                .contentType(ContentType.JSON)
                .extract()
                .response()

            val jsonPath = JsonPath.from(response.asString())
            val scriptId = if (jsonPath.getBoolean("success")) {
                // New upload successful
                jsonPath.getLong("data.scriptId")
            } else {
                // File already exists, get the existing script ID from conflict response
                jsonPath.getLong("data.scriptId")
            }
            
            val scriptName = jsonPath.getString("data.scriptName")
            return Pair(scriptId, scriptName)
        } finally {
            tempFile.delete()
        }
    }

    private fun deleteTestScript(scriptId: Long) {
        given()
            .pathParam("scriptId", scriptId)
            .`when`()
            .delete("/v1/script-impact/scripts/{scriptId}")
            .then()
            .statusCode(anyOf(equalTo(200), equalTo(404)))
    }

    private fun waitForAnalysisCompletion(scriptId: Long, maxWaitSeconds: Int = 60): Boolean {
        var analysisCompleted = false
        var attempts = 0

        println("k7l8m9n0 | 开始等待分析完成，最大等待时间: ${maxWaitSeconds}秒")

        while (!analysisCompleted && attempts < maxWaitSeconds) {
            TimeUnit.SECONDS.sleep(1)
            attempts++

            val response = given()
                .pathParam("scriptId", scriptId)
                .`when`()
                .get("/v1/script-impact/scripts/{scriptId}/analysis")
                .then()
                .extract()
                .response()

            val statusCode = response.statusCode
            when (statusCode) {
                200 -> {
                    // Now all responses are 200, check success field and message to determine if analysis is complete
                    val jsonPath = JsonPath.from(response.asString())
                    val success = jsonPath.getBoolean("success")
                    val message = jsonPath.getString("message")
                    
                    if (success) {
                        analysisCompleted = true
                    } else if (message.contains("分析中") || message.contains("尚未开始")) {
                        // Continue waiting if analysis is still in progress or pending
                    } else {
                        // Analysis failed
                        println("s5t6u7v8 | 分析失败: $message")
                        break
                    }
                }
                500 -> {
                    println("s5t6u7v8 | 分析失败，状态码: $statusCode")
                    break
                }
            }

            if (attempts % 10 == 0) {
                println("o1p2q3r4 | 等待中... 尝试次数: $attempts")
            }
        }

        return analysisCompleted
    }

    @Test
    @DisplayName("UC-3: 验证SQL脚本分析状态转换")
    fun `should verify SQL script analysis status transitions`() {
        val uniqueContent = "-- Analysis status test ${System.currentTimeMillis()}\n$COMPLEX_SQL_CONTENT"
        val (scriptId, _) = uploadTestSqlScript(TEST_UPLOAD_USER, "status_test.sql", uniqueContent)
        
        try {
            // 验证初始状态
            given()
                .pathParam("scriptId", scriptId)
                .`when`()
                .get("/v1/script-impact/scripts/{scriptId}/analysis")
                .then()
                .statusCode(200)
                
            println("u1v2w3x4 | SQL脚本分析状态验证完成")
        } finally {
            deleteTestScript(scriptId)
        }
    }

    @Test
    @DisplayName("UC-3: 手动触发SQL脚本分析")
    fun `should manually trigger SQL script analysis`() {
        val uniqueContent = "-- Manual trigger test ${System.currentTimeMillis()}\n$COMPLEX_SQL_CONTENT"
        val (scriptId, _) = uploadTestSqlScript(TEST_UPLOAD_USER, "trigger_test.sql", uniqueContent)
        
        try {
            // 检查当前状态
            val statusResponse = given()
                .pathParam("scriptId", scriptId)
                .`when`()
                .get("/v1/script-impact/scripts/{scriptId}")
                .then()
                .statusCode(200)
                .extract()
                .response()

            val currentStatus = JsonPath.from(statusResponse.asString()).getString("data.analysisStatus")
            println("y5z6a7b8 | 当前分析状态: $currentStatus")

            // 触发分析
            given()
                .pathParam("scriptId", scriptId)
                .`when`()
                .post("/v1/script-impact/scripts/{scriptId}/analyze")
                .then()
                .statusCode(200)

            println("c9d0e1f2 | 成功触发SQL脚本分析")
        } finally {
            deleteTestScript(scriptId)
        }
    }

    @Test
    @DisplayName("UC-3: 等待分析完成并验证结果结构")
    fun `should wait for analysis completion and verify result structure`() {
        val uniqueContent = "-- Result structure test ${System.currentTimeMillis()}\n$COMPLEX_SQL_CONTENT"
        val (scriptId, _) = uploadTestSqlScript(TEST_UPLOAD_USER, "result_test.sql", uniqueContent)
        
        try {
            // 触发分析
            given()
                .pathParam("scriptId", scriptId)
                .`when`()
                .post("/v1/script-impact/scripts/{scriptId}/analyze")
                .then()
                .statusCode(200)

            // 等待分析完成
            val analysisCompleted = waitForAnalysisCompletion(scriptId, 60)

            if (analysisCompleted) {
                val finalResponse = given()
                    .pathParam("scriptId", scriptId)
                    .`when`()
                    .get("/v1/script-impact/scripts/{scriptId}/analysis")
                    .then()
                    .statusCode(200)
                    .body("success", equalTo(true))
                    .body("data.scriptId", equalTo(scriptId.toInt()))
                    .body("data.scriptName", notNullValue())
                    .body("data.scriptType", equalTo("SQL"))
                    .body("data.analysisStatus", equalTo("COMPLETED"))
                    .body("data.analysisResult", notNullValue())
                    .body("data.analyzedAt", notNullValue())
                    .extract()
                    .response()

                // 验证分析结果JSON结构
                val jsonPath = JsonPath.from(finalResponse.asString())
                val analysisResultJson = jsonPath.getString("data.analysisResult")
                
                assertThat(analysisResultJson).isNotNull()
                
                // 解析分析结果JSON并验证结构
                val analysisResult = JsonPath.from(analysisResultJson as String)
                
                // 验证分析结果的核心结构
                assertThat(analysisResult.getString("analyzedAt")).isNotNull()
                assertThat(analysisResult.getList<Any>("directRelationships")).isNotNull()
                assertThat(analysisResult.getList<Any>("extendedLineage")).isNotNull()
                assertThat(analysisResult.getMap<String, Any>("summary")).isNotNull()
                
                // 验证摘要信息
                val summary = analysisResult.getMap<String, Any>("summary")
                assertThat(summary as Map<String, Any>).containsKeys("totalTablesFound", "uniqueTablesFound", "tablesWithLineageData")
                
                println("w9x0y1z2 | 分析完成并验证成功")
                println("a3b4c5d6 | 分析结果摘要 - 总表数: ${summary["totalTablesFound"]}, 唯一表数: ${summary["uniqueTablesFound"]}")
            } else {
                println("e7f8g9h0 | 分析未在预期时间内完成")
                Assertions.fail<String>("分析未在60秒内完成")
            }
        } finally {
            deleteTestScript(scriptId)
        }
    }

    @Test
    @DisplayName("UC-3: 验证Shell脚本分析功能")
    fun `should verify Shell script analysis functionality`() {
        val uniqueContent = "-- Shell analysis test ${System.currentTimeMillis()}\n$SHELL_SCRIPT_CONTENT"
        val (scriptId, _) = uploadTestShellScript(TEST_UPLOAD_USER, "shell_test.sh", uniqueContent)
        
        try {
            // 触发Shell脚本分析
            given()
                .pathParam("scriptId", scriptId)
                .`when`()
                .post("/v1/script-impact/scripts/{scriptId}/analyze")
                .then()
                .statusCode(200)

            // 等待一段时间后检查状态
            TimeUnit.SECONDS.sleep(5)

            val response = given()
                .pathParam("scriptId", scriptId)
                .`when`()
                .get("/v1/script-impact/scripts/{scriptId}/analysis")
                .then()
                .extract()
                .response()

            // Shell脚本分析应该能够完成或至少进入分析状态
            assertThat(response.statusCode).isIn(200, 500)
            
            if (response.statusCode == 200) {
                val jsonPath = JsonPath.from(response.asString())
                assertThat(jsonPath.getString("data.analysisResult")).isNotNull()
                println("i1j2k3l4 | Shell脚本分析已完成")
            } else {
                println("m5n6o7p8 | Shell脚本分析状态: ${response.statusCode}")
            }
        } finally {
            deleteTestScript(scriptId)
        }
    }

    @Test
    @DisplayName("UC-3: 测试不存在脚本的分析请求")
    fun `should handle analysis request for non-existent script`() {
        val nonExistentId = 999999L

        given()
            .pathParam("scriptId", nonExistentId)
            .`when`()
            .get("/v1/script-impact/scripts/{scriptId}/analysis")
            .then()
            .statusCode(404)
            .body("success", equalTo(false))
            .body("message", containsString("不存在"))

        println("q9r0s1t2 | 不存在脚本分析请求测试完成")
    }

    @Test
    @DisplayName("UC-3: 测试重复触发分析的处理")
    fun `should handle duplicate analysis trigger properly`() {
        val uniqueContent = "-- Duplicate trigger test ${System.currentTimeMillis()}\n$COMPLEX_SQL_CONTENT"
        val (scriptId, _) = uploadTestSqlScript(TEST_UPLOAD_USER, "duplicate_test.sql", uniqueContent)
        
        try {
            // 首先触发一次分析
            given()
                .pathParam("scriptId", scriptId)
                .`when`()
                .post("/v1/script-impact/scripts/{scriptId}/analyze")
                .then()
                .statusCode(200)

            // 立即再次触发，应该返回冲突或成功
            given()
                .pathParam("scriptId", scriptId)
                .`when`()
                .post("/v1/script-impact/scripts/{scriptId}/analyze")
                .then()
                .statusCode(200)

            println("u3v4w5x6 | 重复触发分析测试完成")
        } finally {
            deleteTestScript(scriptId)
        }
    }

    @Test
    @DisplayName("UC-3: 验证分析结果的数据完整性")
    fun `should verify analysis result data integrity`() {
        val uniqueContent = "-- Data integrity test ${System.currentTimeMillis()}\n$COMPLEX_SQL_CONTENT"
        val (scriptId, _) = uploadTestSqlScript(TEST_UPLOAD_USER, "integrity_test.sql", uniqueContent)
        
        try {
            // 触发分析
            given()
                .pathParam("scriptId", scriptId)
                .`when`()
                .post("/v1/script-impact/scripts/{scriptId}/analyze")
                .then()
                .statusCode(200)

            // 等待一段时间让分析有机会完成
            TimeUnit.SECONDS.sleep(10)

            // 获取当前分析结果
            val response = given()
                .pathParam("scriptId", scriptId)
                .`when`()
                .get("/v1/script-impact/scripts/{scriptId}/analysis")
                .then()
                .extract()
                .response()

            if (response.statusCode == 200) {
                val jsonPath = JsonPath.from(response.asString())
                val analysisResultJson = jsonPath.getString("data.analysisResult")
                val analysisResult = JsonPath.from(analysisResultJson as String)

                // 验证直接关系数据
                val directRelationships = analysisResult.getList<Map<String, Any>>("directRelationships")
                if (directRelationships.isNotEmpty()) {
                    val firstRelation = directRelationships[0]
                    assertThat(firstRelation as Map<String, Any>).containsKeys(
                        "tableName", "statementType", "statementIndex", "isSource", "isTarget"
                    )
                    println("y7z8a9b0 | 发现直接表关系: ${directRelationships.size}个")
                }

                // 验证扩展血缘数据
                val extendedLineage = analysisResult.getList<Map<String, Any>>("extendedLineage")
                if (extendedLineage.isNotEmpty()) {
                    val firstLineage = extendedLineage[0]
                    assertThat(firstLineage as Map<String, Any>).containsKeys("tableFullName", "upstreamTables", "downstreamTables")
                    println("c1d2e3f4 | 发现扩展血缘: ${extendedLineage.size}个表")
                }

                // 验证摘要数据的逻辑一致性
                val summary = analysisResult.getMap<String, Any>("summary")
                val totalTables = summary["totalTablesFound"] as Int
                val uniqueTables = summary["uniqueTablesFound"] as Int
                
                assertThat(uniqueTables).isLessThanOrEqualTo(totalTables)
                println("g5h6i7j8 | 数据完整性验证通过 - 总表数: $totalTables, 唯一表数: $uniqueTables")
            } else {
                println("k9l0m1n2 | 分析尚未完成，跳过数据完整性验证")
            }
        } finally {
            deleteTestScript(scriptId)
        }
    }
}