package com.datayes.lineage

import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * 手动血缘维护数据传输对象 (Manual Lineage Maintenance DTOs)
 *
 * UC-14, UC-15, UC-16: 用于创建、编辑和删除手动血缘记录的数据结构
 */

/**
 * 列映射操作类型枚举 (Column Mapping Action Enum)
 */
enum class ColumnMappingAction {
    CREATE,          // 创建新映射
    UPDATE,          // 更新现有映射
    DELETE,          // 删除映射
    UPSERT           // 插入或更新（根据mappingId判断）
}

/**
 * 手动血缘详情DTO (Manual Lineage Details DTO)
 *
 * 用于返回手动血缘的详细信息，包含编辑权限检查
 */
data class ManualLineageDetailsDto(
    val relationshipId: Long,              // 血缘关系ID
    val sourceTable: TableInfoDto,         // 源表信息
    val targetTable: TableInfoDto,         // 目标表信息
    val lineageType: String,               // 血缘类型
    val description: String?,              // 血缘描述
    val confidenceScore: BigDecimal?,      // 置信度分数
    val sourceType: String,                // 来源类型
    val createdBy: String?,                // 创建者
    val createdAt: LocalDateTime?,         // 创建时间
    val lastUpdatedBy: String?,            // 最后更新者
    val lastUpdatedAt: LocalDateTime?,     // 最后更新时间
    val isEditable: Boolean,               // 是否可编辑（只有MANUAL_INPUT可编辑）
    val columnMappings: List<ColumnMappingDetailsDto>, // 列映射详情
    val editPermissions: EditPermissionsDto // 编辑权限信息
)

/**
 * 表信息DTO (Table Info DTO)
 *
 * 简化的表信息，用于手动血缘管理
 */
data class TableInfoDto(
    val tableId: Long,                     // 表ID
    val tableName: String,                 // 表名
    val schema: String?,                   // 模式名
    val datasource: String,                // 数据源
    val system: String?,                   // 系统名
    val chineseName: String?,              // 中文名
    val description: String?               // 表描述
)

/**
 * 列映射详情DTO (Column Mapping Details DTO)
 */
data class ColumnMappingDetailsDto(
    val relationshipId: Long,              // 关系ID
    val sourceColumn: String,              // 源列名
    val sourceDataType: String?,           // 源列数据类型
    val targetColumn: String,              // 目标列名
    val targetDataType: String?,           // 目标列数据类型
    val transformationType: String?,       // 转换类型
    val transformationDescription: String?, // 转换描述
    val transformationExpression: String?, // 转换表达式
    val confidenceScore: BigDecimal?,      // 置信度分数
    val isEditable: Boolean                // 是否可编辑
)

/**
 * 编辑权限DTO (Edit Permissions DTO)
 */
data class EditPermissionsDto(
    val canEdit: Boolean,                  // 是否可编辑
    val canDelete: Boolean,                // 是否可删除
    val canAddColumns: Boolean,            // 是否可添加列映射
    val restrictions: List<String>         // 限制说明
)

/**
 * 手动血缘搜索请求DTO (Manual Lineage Search Request DTO)
 */
data class ManualLineageSearchDto(
    val sourceTableName: String?,          // 源表名搜索
    val targetTableName: String?,          // 目标表名搜索
    val lineageType: String?,              // 血缘类型过滤
    val createdBy: String?,                // 创建者过滤
    val createdAfter: LocalDateTime?,      // 创建时间后
    val createdBefore: LocalDateTime?,     // 创建时间前
    val minConfidenceScore: BigDecimal?,   // 最小置信度
    val page: Int = 0,                     // 页码
    val size: Int = 20,                    // 页大小
    val sortBy: String = "createdAt",      // 排序字段
    val sortDirection: String = "DESC"     // 排序方向
)

/**
 * 手动血缘统计DTO (Manual Lineage Statistics DTO)
 */
data class ManualLineageStatisticsDto(
    val totalManualRelationships: Int,     // 手动血缘关系总数
    val totalColumnMappings: Int,          // 列映射总数
    val avgConfidenceScore: BigDecimal?,   // 平均置信度
    val topCreators: List<CreatorStatDto>, // 顶级创建者
    val lineageTypeDistribution: Map<String, Int>, // 血缘类型分布
    val recentActivity: List<RecentActivityDto> // 最近活动
)

/**
 * 创建者统计DTO (Creator Statistics DTO)
 */
data class CreatorStatDto(
    val createdBy: String,                 // 创建者
    val relationshipCount: Int,            // 创建的关系数
    val lastActivity: LocalDateTime?       // 最后活动时间
)

/**
 * 最近活动DTO (Recent Activity DTO)
 */
data class RecentActivityDto(
    val relationshipId: Long,              // 关系ID
    val action: String,                    // 操作类型 (CREATE, UPDATE, DELETE)
    val performer: String,                 // 操作者
    val timestamp: LocalDateTime,          // 时间戳
    val description: String                // 描述
)

/**
 * 列映射与血缘信息 (Column Mapping with Lineage Info)
 *
 * 包含源列、目标列和转换规则的完整信息
 */
data class ColumnMappingWithLineage(
    val mappingId: Long?, // nullable - 用于标识现有的列映射关系

    // 源列信息 (Source Column Info)
    val sourceColumnName: String,
    val sourceDataType: String,
    val sourceColumnComment: String?,
    val sourceIsPrimaryKey: Boolean = false,
    val sourceIsNullable: Boolean = true,
    val sourceDefaultValue: String?,
    val sourceColumnOrder: Int?,

    // 目标列信息 (Target Column Info)
    val targetColumnName: String,
    val targetDataType: String,
    val targetColumnComment: String?,
    val targetIsPrimaryKey: Boolean = false,
    val targetIsNullable: Boolean = true,
    val targetDefaultValue: String?,
    val targetColumnOrder: Int?,

    // 列级血缘信息 (Column-level Lineage Info)
    val transformationType: String?, // DIRECT_COPY, EXPRESSION, AGGREGATION, etc.
    val transformationExpression: String?,
    val transformationDescription: String?,
    val columnConfidenceScore: BigDecimal?,

    val action: ColumnMappingAction = ColumnMappingAction.UPSERT // 列映射操作类型
)