# LineageTaskController#rerunTask 功能增强摘要

## 问题描述 (Problem Description)

原始的 `LineageTaskController#rerunTask` 方法只支持数据同步任务（`DATA_EXCHANGE_PLATFORM`），但不支持 HDFS 脚本任务（`BASH_SCRIPT`）。

## 解决方案 (Solution)

### 1. 架构重构 (Architecture Refactoring)

将原本单一的 `rerunTask` 方法重构为基于任务类型的策略模式（strategy pattern）：

```kotlin
fun rerunTask(taskId: Long, request: RerunTaskRequest): TaskExecutionResult {
    // 根据任务类型选择相应的处理逻辑
    return when (task.taskType) {
        TaskType.DATA_EXCHANGE_PLATFORM -> rerunDataExchangeTask(task, request, jobKey)
        TaskType.BASH_SCRIPT -> rerunHdfsScriptTask(task, request, jobKey)
        else -> throw IllegalStateException("不支持的任务类型重跑: ${task.taskType}")
    }
}
```

### 2. 新增依赖服务 (Added Dependencies)

在 `LineageTaskService` 中添加了 `HdfsShellScriptService` 依赖：

```kotlin
class LineageTaskService(
    // ... 现有依赖 ...
    private val hdfsShellScriptService: HdfsShellScriptService
)
```

### 3. 实现的关键方法 (Key Methods Implemented)

#### `rerunDataExchangeTask()` - 数据交换任务重跑
- 解析数据交换作业键（`readerJobId_writeJobId` 格式）
- 使用 `DataExchangeJobService` 处理血缘分析
- 保持原有的处理逻辑和错误处理

#### `rerunHdfsScriptTask()` - HDFS 脚本任务重跑
- 重构 `HdfsShellScriptJob` 对象从 `LineageTask` 信息
- 使用 `HdfsShellScriptService` 处理血缘分析
- 适配结果格式兼容现有的任务执行结果更新机制

#### `reconstructHdfsShellScriptJob()` - HDFS 作业对象重构
- 从任务的存储信息中提取脚本内容、文件路径等
- 智能解析脚本名称和 ZIP 文件路径
- 处理数据格式转换

#### `adaptHdfsResultToDataExchangeFormat()` - 结果格式适配
- 将 `HdfsLineageProcessResult` 转换为 `LineageProcessResult`
- 创建虚拟的 `DataExchangeJob` 对象保持兼容性
- 正确映射处理结果状态

### 4. 辅助方法 (Helper Methods)

- `extractScriptNameFromTask()` - 从任务信息中提取脚本名称
- `extractZipFilePathFromTask()` - 从任务信息中提取 ZIP 文件路径  
- `handleTaskExecutionFailure()` - 统一的失败处理逻辑

## 技术细节 (Technical Details)

### 数据流 (Data Flow)

1. **输入验证** - 检查任务状态和类型
2. **类型分派** - 根据 `TaskType` 选择处理策略
3. **服务调用** - 调用相应的血缘处理服务
4. **结果适配** - 统一结果格式
5. **状态更新** - 更新任务执行状态和日志

### 兼容性保证 (Compatibility)

- 保持现有 API 接口不变
- 数据交换任务的处理逻辑完全不变
- 复用现有的错误处理和日志记录机制
- 统一的任务状态管理

### 错误处理 (Error Handling)

- 任务类型不支持时抛出明确的异常
- HDFS 任务缺少必要信息时提供详细错误消息
- 保持与原有错误处理机制的一致性

## 测试验证 (Testing)

### 编译验证
- ✅ 项目成功编译，无语法错误
- ✅ 所有依赖正确注入

### 单元测试
- ✅ HDFS 相关的单元测试全部通过
- ✅ Shell 脚本解析测试正常

### 集成测试状态
- ⚠️ 集成测试需要运行服务器环境（未在此次验证中执行）

## 使用示例 (Usage Example)

### 重跑数据交换任务
```bash
POST /api/v1/lineage/tasks/{taskId}/rerun
{
    "executedBy": "user123",
    "reason": "修复数据问题"
}
```

### 重跑 HDFS 脚本任务
```bash
POST /api/v1/lineage/tasks/{taskId}/rerun
{
    "executedBy": "user123", 
    "reason": "重新处理脚本血缘"
}
```

## 收益 (Benefits)

1. **功能完整性** - 支持所有主要任务类型的重跑
2. **代码可维护性** - 清晰的类型分派和责任分离
3. **扩展性** - 容易添加新的任务类型支持
4. **一致性** - 统一的 API 和错误处理

## 后续改进建议 (Future Improvements)

1. 添加更多任务类型支持（如 `MANUAL_IMPORT`, `EXCEL_IMPORT`）
2. 增强 HDFS 任务信息的存储和恢复机制
3. 添加专门的集成测试覆盖新增功能
4. 考虑添加批量重跑功能

## 总结 (Summary)

此次增强成功解决了 `rerunTask` 方法只支持数据同步任务的限制，通过架构重构和适配器模式，实现了对 HDFS 脚本任务的完整支持，同时保持了向后兼容性和代码质量。 