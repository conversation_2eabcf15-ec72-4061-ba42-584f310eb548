<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DGP Lineage Collector - Log Viewer</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- HTMX for dynamic content -->
    <script src="https://unpkg.com/htmx.org@1.9.6"></script>
    <style>
        .log-level-INFO { color: #0d6efd; }
        .log-level-WARN { color: #ffc107; }
        .log-level-ERROR { color: #dc3545; }
        .log-level-DEBUG { color: #6c757d; }
        .log-table { font-size: 0.85rem; }
        .log-table td { white-space: pre-wrap; word-break: break-word; }
        .log-message { max-width: 500px; }
        .log-logger { max-width: 250px; }
        .operation-success { color: #198754; font-weight: 500; }
        .operation-error { color: #dc3545; font-weight: 500; }
        .card { border: 1px solid #dee2e6; }
        .card-title { margin-bottom: 1rem; color: #495057; }
    </style>
</head>
<body>
    <div class="container-fluid py-3">
        <h1 class="mb-4">DGP Lineage Collector - Log Viewer</h1>
        
        <div class="row mb-3">
            <div class="col-md-6">
                <div class="input-group">
                    <input 
                        type="text" 
                        class="form-control" 
                        placeholder="Search logs..." 
                        aria-label="Search logs" 
                        id="search-input"
                        th:hx-get="${pathPrefix + '/logs/htmx/logs-table'}"
                        hx-trigger="keyup changed delay:500ms, search"
                        hx-target="#logs-table-container"
                        name="query">
                    <button 
                        class="btn btn-outline-secondary" 
                        type="button"
                        th:hx-get="${pathPrefix + '/logs/htmx/logs-table'}"
                        hx-include="#search-input"
                        hx-target="#logs-table-container">
                        Search
                    </button>
                </div>
            </div>
            <div class="col-md-6 text-end">
                <button 
                    class="btn btn-primary"
                    th:hx-get="${pathPrefix + '/logs/htmx/logs-table'}"
                    hx-target="#logs-table-container">
                    Refresh
                </button>
                <button 
                    class="btn btn-danger"
                    th:hx-post="${pathPrefix + '/logs/api/clear'}"
                    hx-confirm="Are you sure you want to clear all logs?"
                    hx-target="#logs-table-container"
                    hx-trigger="click"
                    hx-swap="none"
                    onclick="document.getElementById('search-input').value = ''; htmx.trigger('#search-input', 'search');">
                    Clear Logs
                </button>
            </div>
        </div>
        
        <!-- Keep Last N Minutes Section -->
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end align-items-center gap-2 flex-wrap">
                                    <label for="minutes-input" class="form-label mb-0">Minutes 分钟数</label>
                                    <input type="number" class="form-control" style="width: 120px;" id="minutes-input" name="minutes" placeholder="e.g., 30" min="1" max="1440" value="30">
                                    <button class="btn btn-warning" th:hx-post="${pathPrefix + '/logs/api/keep-last-minutes'}" hx-include="#minutes-input" hx-confirm="Are you sure you want to keep only logs from the last N minutes? Older logs will be permanently removed." hx-target="#operation-result" hx-swap="innerHTML" onclick="setTimeout(() => { htmx.trigger('#search-input', 'search'); }, 500);">
                                        Keep Last N Minutes
                                    </button>
                                </div>
                                <div id="operation-result" class="small text-end mt-1"></div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="logs-table-container" th:hx-get="${pathPrefix + '/logs/htmx/logs-table'}" hx-trigger="load">
            <!-- Logs table will be loaded here -->
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Handle HTMX response for keep-last-minutes operation
        document.body.addEventListener('htmx:afterRequest', function(evt) {
            if (evt.detail.target.id === 'operation-result') {
                try {
                    const response = JSON.parse(evt.detail.xhr.responseText);
                    const resultDiv = document.getElementById('operation-result');
                    
                    if (response.status === 'success') {
                        resultDiv.innerHTML = `
                            <div class="operation-success">
                                ✓ ${response.message}<br>
                                Removed ${response.removedCount} log entries
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="operation-error">
                                ✗ ${response.message}
                            </div>
                        `;
                    }
                    
                    // Clear result after 5 seconds
                    setTimeout(() => {
                        resultDiv.innerHTML = '';
                    }, 5000);
                    
                } catch (e) {
                    console.error('Error parsing response:', e);
                }
            }
        });
        
        // Validate minutes input
        document.getElementById('minutes-input').addEventListener('input', function(e) {
            const value = parseInt(e.target.value);
            const button = document.querySelector('button.btn-warning');
            
            if (isNaN(value) || value < 1 || value > 1440) {
                e.target.classList.add('is-invalid');
                button.disabled = true;
            } else {
                e.target.classList.remove('is-invalid');
                button.disabled = false;
            }
        });
    </script>
</body>
</html>