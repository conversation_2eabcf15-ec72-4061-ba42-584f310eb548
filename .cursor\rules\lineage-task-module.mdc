---
description:
globs:
alwaysApply: false
---
# 血缘任务模块 (Lineage Task Module)

## 模块概述 (Module Overview)
血缘任务模块负责管理数据血缘收集任务的生命周期 (lifecycle management of data lineage collection tasks)，包括任务创建、调度、执行和监控。

## 核心组件 (Core Components)

### 1. 控制器层 (Controller Layer)
- [LineageTaskController.kt](mdc:src/main/kotlin/com/datayes/lineagetask/LineageTaskController.kt) - REST API 控制器 (REST API controller)

### 2. 服务层 (Service Layer)
- [LineageTaskService.kt](mdc:src/main/kotlin/com/datayes/lineagetask/LineageTaskService.kt) - 核心业务逻辑 (core business logic)
- [DataExchangeJobService.kt](mdc:src/main/kotlin/com/datayes/lineagetask/DataExchangeJobService.kt) - 数据交换作业服务 (data exchange job service)
- [LineageCollectorService.kt](mdc:src/main/kotlin/com/datayes/lineagetask/LineageCollectorService.kt) - 血缘收集服务 (lineage collection service)

### 3. 数据层 (Data Layer)
- [LineageTaskRepository.kt](mdc:src/main/kotlin/com/datayes/lineagetask/LineageTaskRepository.kt) - 血缘任务数据访问 (lineage task data access)
- [DataExchangeJobRepository.kt](mdc:src/main/kotlin/com/datayes/lineagetask/DataExchangeJobRepository.kt) - 数据交换作业数据访问 (data exchange job data access)
- [JobProcessingHistoryRepository.kt](mdc:src/main/kotlin/com/datayes/lineagetask/JobProcessingHistoryRepository.kt) - 作业处理历史 (job processing history)

### 4. 处理器 (Processors)
- [DataExchangeJobProcessor.kt](mdc:src/main/kotlin/com/datayes/lineagetask/DataExchangeJobProcessor.kt) - 数据交换作业处理器 (data exchange job processor)

### 5. 数据模型 (Data Models)
- [LineageTask.kt](mdc:src/main/kotlin/com/datayes/lineagetask/LineageTask.kt) - 血缘任务实体 (lineage task entity)
- [DataExchangeJob.kt](mdc:src/main/kotlin/com/datayes/lineagetask/DataExchangeJob.kt) - 数据交换作业实体 (data exchange job entity)
- [DataExchangeDataSource.kt](mdc:src/main/kotlin/com/datayes/lineagetask/DataExchangeDataSource.kt) - 数据源配置 (data source configuration)

## 功能特性 (Features)
- 任务生命周期管理 (task lifecycle management)
- 作业调度和执行 (job scheduling and execution)
- 处理历史追踪 (processing history tracking)
- 数据源配置管理 (data source configuration management)

## 设计原则 (Design Principles)
- 单一职责原则 (Single Responsibility Principle)
- 数据为中心的设计 (data-centric design)
- 函数式核心，命令式外壳 (functional core, imperative shell)
