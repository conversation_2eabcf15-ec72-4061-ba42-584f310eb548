package com.datayes

import com.datayes.dataexchange.DataExchangeJobLineageConverter
import com.datayes.dataexchange.DataExchangeColumnMapping
import com.datayes.dataexchange.DataExchangeJob
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*

class DataModelTest {

    private val objectMapper = jacksonObjectMapper().registerKotlinModule()

    @Test
    fun `should parse data exchange job from JSON`() {
        val jsonString = """
        {
          "reader_job_id": "1",
          "reader_job_name": "data-exc生产测试（Mysql-localfile）",
          "db_r": "*****************************************",
          "reader_table_name": "sys_role_menu_copy",
          "reader_sql": "select `role_id`, `menu_id` from sys_role_menu_copy where 1=1",
          "write_job_id": "2",
          "write_job_name": "data-exc生产测试（localfile-Mysql）",
          "db_w": "*****************************************",
          "columns": [
            {
              "columnIndex": 0,
              "columnRemark": "角色ID",
              "dstColumnName": "role_id",
              "dstColumnType": "string",
              "srcColumnName": "role_id",
              "srcColumnType": "bigint"
            },
            {
              "columnIndex": 1,
              "columnRemark": "菜单ID",
              "dstColumnName": "menu_id",
              "dstColumnType": "string",
              "srcColumnName": "menu_id",
              "srcColumnType": "bigint"
            }
          ],
          "writer_table_name": "sys_role_menu_copy1"
        }
        """.trimIndent()

        val dataExchangeJob = objectMapper.readValue<DataExchangeJob>(jsonString)

        // 验证基本属性
        assertEquals("1", dataExchangeJob.readerJobId)
        assertEquals("data-exc生产测试（Mysql-localfile）", dataExchangeJob.readerJobName)
        assertEquals("*****************************************", dataExchangeJob.dbReader)
        assertEquals("sys_role_menu_copy", dataExchangeJob.readerTableName)
        assertEquals("sys_role_menu_copy1", dataExchangeJob.writerTableName)

        // 验证列映射
        assertEquals(2, dataExchangeJob.columns.size)

        val firstColumn = dataExchangeJob.columns[0]
        assertEquals(0, firstColumn.columnIndex)
        assertEquals("角色ID", firstColumn.columnRemark)
        assertEquals("role_id", firstColumn.srcColumnName)
        assertEquals("bigint", firstColumn.srcColumnType)
        assertEquals("role_id", firstColumn.dstColumnName)
        assertEquals("string", firstColumn.dstColumnType)
    }

    @Test
    fun `should parse database info from JDBC URL`() {
        val jdbcUrl = "*****************************************"
        val databaseInfo = com.datayes.lineage.DatabaseInfo.parseFromJdbcUrl(jdbcUrl)

        assertNotNull(databaseInfo)
        assertEquals("mysql", databaseInfo!!.dbType)
        assertEquals("***********", databaseInfo.host)
        assertEquals(3306, databaseInfo.port)
        assertEquals("dc_data_exc", databaseInfo.databaseName)
        assertEquals(jdbcUrl, databaseInfo.originalConnectionString)
    }

    @Test
    fun `should handle invalid JDBC URL`() {
        val invalidUrl = "invalid-jdbc-url"
        val databaseInfo = com.datayes.lineage.DatabaseInfo.parseFromJdbcUrl(invalidUrl)

        assertNull(databaseInfo)
    }

    @Test
    fun `should parse Oracle database info from JDBC URL`() {
        val jdbcUrl = "*****************************************"
        val databaseInfo = com.datayes.lineage.DatabaseInfo.parseFromJdbcUrl(jdbcUrl)

        assertNotNull(databaseInfo)
        assertEquals("oracle", databaseInfo!!.dbType)
        assertEquals("**********", databaseInfo.host)
        assertEquals(1520, databaseInfo.port)
        assertEquals("lisdb", databaseInfo.databaseName)
        assertEquals(jdbcUrl, databaseInfo.originalConnectionString)
    }

    @Test
    fun `should parse Hive2 database info from JDBC URL`() {
        val jdbcUrl = "******************************,***********:10000/ms_ods_ebs"
        val databaseInfo = com.datayes.lineage.DatabaseInfo.parseFromJdbcUrl(jdbcUrl)

        assertNotNull(databaseInfo)
        assertEquals("hive2", databaseInfo!!.dbType)
        assertEquals("***********", databaseInfo.host)
        assertEquals(10000, databaseInfo.port)
        assertEquals("ms_ods_ebs", databaseInfo.databaseName)
        assertEquals(jdbcUrl, databaseInfo.originalConnectionString)
    }

    @Test
    fun `should convert data exchange job to lineage`() {
        val dataExchangeJob = DataExchangeJob(
            readerJobId = "1",
            readerJobName = "测试读取作业",
            dbReader = "*************************************",
            readerTableName = "source_table",
            readerSql = "select id, name from source_table where active = 1",
            writeJobId = "2",
            writeJobName = "测试写入作业",
            dbWriter = "*************************************",
            columns = listOf(
                DataExchangeColumnMapping(
                    columnIndex = 0,
                    columnRemark = "主键ID",
                    srcColumnName = "id",
                    srcColumnType = "bigint",
                    dstColumnName = "id",
                    dstColumnType = "bigint"
                ),
                DataExchangeColumnMapping(
                    columnIndex = 1,
                    columnRemark = "名称",
                    srcColumnName = "name",
                    srcColumnType = "varchar",
                    dstColumnName = "name",
                    dstColumnType = "string"
                )
            ),
            writerTableName = "target_table"
        )

        val result = DataExchangeJobLineageConverter.convertToLineage(dataExchangeJob)

        assertTrue(result.success)
        assertNotNull(result.lineage)
        assertTrue(result.errors.isEmpty())

        val lineage = result.lineage!!
        assertEquals("1-2", lineage.jobId)
        assertEquals("测试读取作业 -> 测试写入作业", lineage.jobName)
        assertEquals(com.datayes.lineage.LineageType.FILTER, lineage.tableLineage.lineageType) // 因为SQL包含WHERE
        assertEquals(2, lineage.columnLineages.size)

        // 验证表级血缘
        assertEquals("source_table", lineage.tableLineage.sourceTables[0].tableName)
        assertEquals("target_table", lineage.tableLineage.targetTable.tableName)
        assertEquals("source_db", lineage.tableLineage.sourceTables[0].database.databaseName)
        assertEquals("target_db", lineage.tableLineage.targetTable.database.databaseName)

        // 验证列级血缘
        val firstColumnLineage = lineage.columnLineages[0]
        assertEquals("id", firstColumnLineage.sourceColumn.columnName)
        assertEquals("id", firstColumnLineage.targetColumn.columnName)
        assertEquals("bigint", firstColumnLineage.sourceColumn.dataType)
        assertEquals("bigint", firstColumnLineage.targetColumn.dataType)
        assertNull(firstColumnLineage.transformation) // 无转换

        val secondColumnLineage = lineage.columnLineages[1]
        assertEquals("name", secondColumnLineage.sourceColumn.columnName)
        assertEquals("name", secondColumnLineage.targetColumn.columnName)
        assertEquals("varchar", secondColumnLineage.sourceColumn.dataType)
        assertEquals("string", secondColumnLineage.targetColumn.dataType)
        assertNotNull(secondColumnLineage.transformation) // 有类型转换
        assertEquals(com.datayes.lineage.TransformationType.TYPE_CAST, secondColumnLineage.transformation!!.transformationType)
    }

    @Test
    fun `should generate fully qualified names correctly`() {
        val databaseInfo = com.datayes.lineage.DatabaseInfo(
            dbType = "mysql",
            host = "localhost",
            port = 3306,
            databaseName = "test_db",
            originalConnectionString = "***********************************"
        )

        val tableInfo = com.datayes.lineage.TableInfo(
            schema = "public",
            tableName = "test_table",
            database = databaseInfo
        )

        val columnInfo = com.datayes.lineage.ColumnInfo(
            columnName = "test_column",
            dataType = "varchar",
            comment = "测试列",
            table = tableInfo
        )

        assertEquals("test_db.public.test_table", tableInfo.getFullyQualifiedName())
        assertEquals("test_db.public.test_table.test_column", columnInfo.getFullyQualifiedName())

        // 测试无schema的情况
        val tableWithoutSchema = com.datayes.lineage.TableInfo(
            schema = null,
            tableName = "simple_table",
            database = databaseInfo
        )

        assertEquals("test_db.simple_table", tableWithoutSchema.getFullyQualifiedName())
    }
} 