package com.datayes.shell

import org.slf4j.LoggerFactory

object ShellScriptParser {

    private val log = LoggerFactory.getLogger(this.javaClass)

    // Regex patterns
    // For variable assignment: export VAR=... or VAR=...
    // It captures: (1: "export "), (2: VAR_NAME), (3: everything after '=')
    private val variableAssignmentRegex = """^\s*(export\s+)?([a-zA-Z_][a-zA-Z0-9_]*)=(.*)$""".toRegex()

    // For variable usage: $VAR or ${VAR}
    // Fixed regex to avoid catastrophic backtracking by making it more specific
    val variablePattern = """\$(?:\{([a-zA-Z_][a-zA-Z0-9_]*)\}|([a-zA-Z_][a-zA-Z0-9_]*))""".toRegex()

    // For hive command: ${hive} -e "..." or hive -e "..."
    // It captures: (1: the SQL part, still needs outer quotes trimmed)
    val hiveCommandPattern = """^\s*(?:\$\{\s*hive\s*\}|hive)\s*-e\s*"(.*)$""".toRegex()

    fun substituteVariables(text: String, variables: Map<String, String>): String {
        var result = text
        var changed: Boolean
        var iterations = 0
        val maxIterations = 10 // Safety limit to prevent infinite loops
        val startTime = System.currentTimeMillis()
        
        // Create monitoring state using AtomicReference for thread-safe access
        val monitoringState = java.util.concurrent.atomic.AtomicReference(
            Triple(true, 0, text) // isLoopRunning, currentIteration, currentResultSnapshot
        )
        
        val monitorThread = Thread {
            try {
                while (monitoringState.get().first) {
                    Thread.sleep(10000) // Check every 10 seconds
                    val state = monitoringState.get()
                    if (state.first) {
                        val elapsedTime = System.currentTimeMillis() - startTime
                        log.warn("8f4d2c3b | Variable substitution loop running for {}ms, iteration {}, text length: {}, variables count: {}, current text preview: '{}'", 
                            elapsedTime, state.second, state.third.length, variables.size, 
                            if (state.third.length > 100) state.third.take(100) + "..." else state.third)
                    }
                }
            } catch (e: InterruptedException) {
                // Thread interrupted, exit gracefully
            }
        }.apply {
            isDaemon = true
            name = "VariableSubstitution-Monitor"
        }
        
        monitorThread.start()
        
        try {
            do {
                if (iterations >= maxIterations) {
                    log.warn("32a4f7b1 | Variable substitution reached maximum iterations ({}), stopping to prevent infinite loop", maxIterations)
                    break
                }
                
                // Update monitoring state
                monitoringState.set(Triple(true, iterations, result))
                
                val currentResult = variablePattern.replace(result) { matchResult ->
                    val varNameBraced = matchResult.groups[1]?.value
                    val varNameSimple = matchResult.groups[2]?.value
                    val varName = varNameBraced ?: varNameSimple

                    // 获取替换值，如果不存在则保持原样
                    val replacement = if (varName != null && variables.containsKey(varName)) {
                        variables[varName]!!
                    } else {
                        matchResult.value
                    }

                    replacement
                }

                // 仅当结果发生实际变化时才继续循环，避免自引用变量导致(无限循环)(infinite loop)
                changed = currentResult != result
                result = currentResult
                iterations++
            } while (changed)
        } finally {
            // Stop monitoring
            monitoringState.set(Triple(false, iterations, result))
            monitorThread.interrupt()
        }
        
        return result
    }

    fun parseShellScriptForSql(scriptContent: String, initialVariables: Map<String, String> = emptyMap()): List<String> {
        val lines = scriptContent.lines()
        val shellVariables = initialVariables.toMutableMap()
        val extractedSqls = mutableListOf<String>()

        if (!shellVariables.containsKey("hive")) {
            shellVariables["hive"] = "hive"
        }

        // todo wujie
        // For script argument $1, if it's used to set 'yesterday'
        // The script itself does `export yesterday=$1`, so we need to provide "1"
        // 使用更准确的检查方式来检测 $1 的使用
        // if ((scriptContent.contains("$1") || scriptContent.contains("\${1}")) && !shellVariables.containsKey("1")) {
        //     // 或者更彻底的方式是使用正则表达式
        //     // val containsDollar1 = """\$\{?1\}?""".toRegex().containsMatchIn(scriptContent)
        //     // if (containsDollar1 && !shellVariables.containsKey("1")) {
        //     // This is a common way to pass the first argument.
        //     // If initialVariables contains "yesterday_arg", we can use it for "1".
        //     if (initialVariables.containsKey("yesterday_arg")) {
        //         shellVariables["1"] = initialVariables["yesterday_arg"]!!
        //     } else {
        //         log.warn("Script uses \$1, but '1' or 'yesterday_arg' not provided in initialVariables. Using a placeholder.")
        //         shellVariables["1"] = "YYYY-MM-DD_placeholder" // Default placeholder
        //     }
        // }

        var i = 0
        while (i < lines.size) {
            val line = lines[i] // Keep original line for accurate multi-line parsing

            // Skip comments and empty lines
            if (line.trimStart().startsWith("#") || line.isBlank()) {
                i++
                continue
            }

            // 1. Handle variable assignments
            val varAssignMatch = variableAssignmentRegex.find(line.trimStart())
            if (varAssignMatch != null) {
                val varName = varAssignMatch.groupValues[2]
                var varValueRaw = varAssignMatch.groupValues[3].trimEnd() // Trim trailing space from value part

                // Handle multi-line variable assignments (e.g., base_field="...")
                // Check if the value starts with a quote and doesn't end with the *same* quote on the same line
                // (unless it's an escaped quote at the end)
                if ((varValueRaw.startsWith("\"") && (!varValueRaw.endsWith("\"") || varValueRaw.endsWith("\\\"")) && varValueRaw.length > 1) ||
                    (varValueRaw.startsWith("'") && !varValueRaw.endsWith("'") && varValueRaw.length > 1)
                ) {
                    val quoteChar = varValueRaw.first()
                    val valueBuilder = StringBuilder(varValueRaw.substring(1)) // Drop leading quote
                    var multilineEnd = false
                    for (j in i + 1 until lines.size) {
                        val nextLine = lines[j]
                        valueBuilder.appendLine() // Add newline, shell does this
                        valueBuilder.append(nextLine)
                        // Check if the current accumulated string (trimmed) ends with the quote
                        // and it's not an escaped quote
                        if (valueBuilder.toString().trimEnd().endsWith(quoteChar) && !valueBuilder.toString().trimEnd()
                                .endsWith("\\$quoteChar")
                        ) {
                            i = j // Advance outer loop index
                            multilineEnd = true
                            varValueRaw = valueBuilder.toString().trimEnd().dropLast(1) // Drop trailing quote
                            break
                        }
                    }
                    if (!multilineEnd) {
                        // Unterminated multi-line variable, take what we have but without the leading quote
                        varValueRaw = valueBuilder.toString()
                        log.warn("Unterminated multi-line variable '{}'", varName)
                    }
                } else {
                    // Simple single line assignment, remove surrounding quotes if they are the very start/end
                    log.info("8a9f4c12 | Processing single line assignment - varName: '{}', varValueRaw: '{}', length: {}", 
                        varName, varValueRaw, varValueRaw.length)
                    
                    if (varValueRaw.startsWith("\"") && varValueRaw.endsWith("\"")) {
                        if (varValueRaw.length < 2) {
                            log.warn("ef7b2a93 | Invalid double-quoted value with length < 2 for variable '{}': '{}', treating as empty string", 
                                varName, varValueRaw)
                            varValueRaw = ""
                        } else {
                            log.info("d4c8f159 | Removing double quotes from variable '{}': '{}' -> '{}'", 
                                varName, varValueRaw, varValueRaw.substring(1, varValueRaw.length - 1))
                            varValueRaw = varValueRaw.substring(1, varValueRaw.length - 1)
                        }
                    } else if (varValueRaw.startsWith("'") && varValueRaw.endsWith("'")) {
                        if (varValueRaw.length < 2) {
                            log.warn("7c3d8e45 | Invalid single-quoted value with length < 2 for variable '{}': '{}', treating as empty string", 
                                varName, varValueRaw)
                            varValueRaw = ""
                        } else {
                            log.info("9b1a5f72 | Removing single quotes from variable '{}': '{}' -> '{}'", 
                                varName, varValueRaw, varValueRaw.substring(1, varValueRaw.length - 1))
                            varValueRaw = varValueRaw.substring(1, varValueRaw.length - 1)
                        }
                    }
                }

                val finalVarValue =
                    if (varValueRaw.startsWith("`") && varValueRaw.endsWith("`") || varValueRaw.startsWith("$(") && varValueRaw.endsWith(
                            ")"
                        )
                    ) {
                        substituteVariables(varValueRaw, shellVariables)
                    } else {
                        substituteVariables(varValueRaw, shellVariables)
                    }
                shellVariables[varName] = finalVarValue
                i++
                continue
            }

            // 2. Handle `source` command
            if (line.trimStart().startsWith("source ")) {
                val sourcedFile = line.substringAfter("source ").trim()
                log.info("Encountered 'source {}'. Variables from this file should be pre-loaded or handled manually.", sourcedFile)
                i++
                continue
            }

            // 3. Handle `hive -e "SQL..."`
            val hiveMatch = hiveCommandPattern.find(line.trimStart()) // Use trimmed line for pattern matching
            if (hiveMatch != null) {
                val contentCapturedByRegex = hiveMatch.groupValues[1] // Content after `hive -e "` from the regex
                log.debug("Found hive command. Line: '{}'", line)
                log.debug("contentCapturedByRegex: '{}'", contentCapturedByRegex)

                val sqlBuilder = StringBuilder()

                // Determine if the SQL part captured by regex itself ends with a quote.
                val capturedEndsWithQuote = contentCapturedByRegex.trimEnd().endsWith("\"")
                val capturedEndsWithEscapedQuote = contentCapturedByRegex.trimEnd().endsWith("\\\"")

                log.debug("capturedEndsWithQuote: {}", capturedEndsWithQuote)
                log.debug("capturedEndsWithEscapedQuote: {}", capturedEndsWithEscapedQuote)

                // If it DOESN'T end with a quote, OR it ends with an ESCAPED quote,
                // it means the SQL command is expected to continue on subsequent lines.
                val isMultiline = !capturedEndsWithQuote || capturedEndsWithEscapedQuote
                log.debug("isMultiline decision: {}", isMultiline)

                if (isMultiline) {
                    // Multi-line SQL command
                    log.debug("Treating as multi-line SQL.")
                    sqlBuilder.appendLine(contentCapturedByRegex) // Append the first part (which might be empty if line is just `hive -e "`)
                    var j = i + 1
                    var foundClosingQuote = false
                    while (j < lines.size) {
                        val sqlBlockLine = lines[j]
                        // Check if this line contains the closing quote for the hive -e command
                        // The closing quote must not be escaped.
                        if (sqlBlockLine.trimEnd().endsWith("\"") && !sqlBlockLine.trimEnd().endsWith("\\\"")) {
                            sqlBuilder.append(sqlBlockLine.removeSuffix("\"")) // Append and remove the quote
                            i = j // Advance outer loop index
                            foundClosingQuote = true
                            break
                        } else {
                            sqlBuilder.appendLine(sqlBlockLine) // Append the raw line
                        }
                        j++
                    }
                    if (!foundClosingQuote) {
                        log.warn("Unterminated HiveQL block starting near line {}. Taking content as is, might be incomplete.", i + 1)
                    }
                } else {
                    // Single-line SQL command
                    // The regex (.*)$ has captured the entire SQL content for the single line,
                    // including its own closing quote. We need to remove this closing quote.
                    log.debug("Treating as single-line SQL.")
                    sqlBuilder.append(contentCapturedByRegex.removeSuffix("\""))
                }

                val rawSql = sqlBuilder.toString()
                val substitutedSql = substituteVariables(rawSql, shellVariables)
                extractedSqls.add(substitutedSql)
            }
            i++
        }
        return extractedSqls
    }
}