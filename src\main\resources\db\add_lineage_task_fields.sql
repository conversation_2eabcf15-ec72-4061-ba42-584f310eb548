-- ====================================================================
-- Lineage Tasks Management Database Schema Enhancement
-- 血缘任务管理数据库模式增强
-- ====================================================================

-- 为lineage_tasks表添加任务管理功能所需的字段
-- 基于现有表结构增强，支持任务管理功能

-- 1. 添加新字段以支持任务管理功能
ALTER TABLE lineage_tasks ADD COLUMN IF NOT EXISTS job_key VARCHAR(500) COMMENT '作业唯一标识符';
ALTER TABLE lineage_tasks ADD COLUMN IF NOT EXISTS processing_time_ms BIGINT COMMENT '处理耗时(毫秒)';
ALTER TABLE lineage_tasks ADD COLUMN IF NOT EXISTS has_changes BOOLEAN DEFAULT FALSE COMMENT '是否有血缘变更';
ALTER TABLE lineage_tasks ADD COLUMN IF NOT EXISTS batch_id VARCHAR(100) COMMENT '批处理标识';
ALTER TABLE lineage_tasks ADD COLUMN IF NOT EXISTS execution_count INT DEFAULT 1 COMMENT '执行次数';
ALTER TABLE lineage_tasks ADD COLUMN IF NOT EXISTS last_execution_id VARCHAR(100) COMMENT '最后执行ID';

-- 2. 添加性能优化索引
CREATE INDEX IF NOT EXISTS idx_lineage_tasks_job_key ON lineage_tasks(job_key);
CREATE INDEX IF NOT EXISTS idx_lineage_tasks_batch_id ON lineage_tasks(batch_id);
CREATE INDEX IF NOT EXISTS idx_lineage_tasks_status_enabled ON lineage_tasks(task_status, is_enabled);
CREATE INDEX IF NOT EXISTS idx_lineage_tasks_created_at ON lineage_tasks(created_at);
CREATE INDEX IF NOT EXISTS idx_lineage_tasks_executed_at ON lineage_tasks(executed_at);

-- 3. 更新现有任务状态枚举（如果需要）
-- ALTER TABLE lineage_tasks MODIFY COLUMN task_status ENUM('PENDING', 'RUNNING', 'SUCCESS', 'FAILED', 'CANCELLED') NOT NULL DEFAULT 'PENDING';

-- 4. 为现有记录初始化新字段
UPDATE lineage_tasks 
SET 
    execution_count = COALESCE(execution_count, 1),
    has_changes = COALESCE(has_changes, FALSE)
WHERE execution_count IS NULL OR has_changes IS NULL;

-- 5. 添加约束（可选）
-- ALTER TABLE lineage_tasks ADD CONSTRAINT chk_processing_time_positive CHECK (processing_time_ms IS NULL OR processing_time_ms >= 0);
-- ALTER TABLE lineage_tasks ADD CONSTRAINT chk_execution_count_positive CHECK (execution_count > 0);

COMMIT; 