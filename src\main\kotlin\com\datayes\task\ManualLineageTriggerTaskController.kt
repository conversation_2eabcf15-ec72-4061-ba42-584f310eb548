package com.datayes.task

import io.swagger.v3.oas.annotations.responses.ApiResponses
import org.slf4j.LoggerFactory
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import java.time.LocalDateTime

/**
 * 手动触发血缘任务控制器 (Manual Lineage Trigger Task Controller)
 *
 * 提供手动触发血缘任务的REST API接口
 */
@RestController
@RequestMapping("/api/v1/lineage/manual-tasks")
@CrossOrigin(origins = ["*"])
class ManualLineageTriggerTaskController(
    private val manualLineageTriggerTaskService: ManualLineageTriggerTaskService
) {

    private val logger = LoggerFactory.getLogger(ManualLineageTriggerTaskController::class.java)

    /**
     * 手动触发血缘任务 (Trigger manual lineage task)
     * 
     * 同步执行血缘任务，等待处理完成后返回任务摘要
     *
     * POST /api/v1/lineage/manual-tasks/trigger
     */
    @PostMapping("/trigger")
    fun triggerManualLineageTask(
        @RequestBody request: ManualLineageTriggerRequest
    ): ResponseEntity<ManualTaskSummary> {
        return try {
            logger.info("a1b2c3 | 接收到手动触发血缘任务请求，数据源ID: ${request.datasourceId}")
            
            val response = manualLineageTriggerTaskService.triggerManualLineageTask(request)
            
            logger.info("d4e5f6 | 手动触发血缘任务执行完成，任务UUID: ${response.taskUuid}")
            ResponseEntity.ok(response)
            
        } catch (e: IllegalArgumentException) {
            logger.warn("g7h8i9 | 手动触发血缘任务请求参数无效: ${e.message}")
            ResponseEntity.badRequest().body(
                ManualTaskSummary(
                    id = 0,
                    taskUuid = "",
                    datasourceId = request.datasourceId,
                    datasourceName = null,
                    triggerUser = request.triggerUser,
                    taskStatus = ManualTaskStatus.FAILED,
                    successCount = 0,
                    failureCount = 0,
                    totalCount = 0,
                    executionTimeMs = null,
                    createdAt = java.time.LocalDateTime.now(),
                    completedAt = null
                )
            )
        } catch (e: Exception) {
            logger.error("j0k1l2 | 手动触发血缘任务失败", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                ManualTaskSummary(
                    id = 0,
                    taskUuid = "",
                    datasourceId = request.datasourceId,
                    datasourceName = null,
                    triggerUser = request.triggerUser,
                    taskStatus = ManualTaskStatus.FAILED,
                    successCount = 0,
                    failureCount = 0,
                    totalCount = 0,
                    executionTimeMs = null,
                    createdAt = java.time.LocalDateTime.now(),
                    completedAt = null
                )
            )
        }
    }

    /**
     * 查询手动任务列表 (Query manual task list)
     *
     * GET /api/v1/lineage/manual-tasks
     */
    @GetMapping
    fun queryManualTasks(
        @RequestParam(required = false) datasourceId: Long?,
        @RequestParam(required = false) triggerUser: String?,
        @RequestParam(required = false) taskStatus: ManualTaskStatus?,
        @RequestParam(required = false) startDate: String?,
        @RequestParam(required = false) endDate: String?,
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "20") size: Int,
        @RequestParam(defaultValue = "createdAt") sortBy: String,
        @RequestParam(defaultValue = "desc") sortDir: String
    ): ResponseEntity<ManualTaskListResponse> {
        return try {
            logger.info("m3n4o5 | 查询手动任务列表，数据源ID: $datasourceId, 用户: $triggerUser, 状态: $taskStatus")
            
            val criteria = ManualTaskQueryCriteria(
                datasourceId = datasourceId,
                triggerUser = triggerUser,
                taskStatus = taskStatus,
                startDate = startDate?.let { LocalDateTime.parse(it) },
                endDate = endDate?.let { LocalDateTime.parse(it) }
            )
            
            val direction = if (sortDir.lowercase() == "desc") Sort.Direction.DESC else Sort.Direction.ASC
            val sort = Sort.by(direction, sortBy)
            val pageable = PageRequest.of(page, size, sort)
            
            val response = manualLineageTriggerTaskService.queryManualTasks(criteria, pageable)
            
            logger.info("p6q7r8 | 查询到 ${response.totalElements} 个手动任务")
            ResponseEntity.ok(response)
            
        } catch (e: Exception) {
            logger.error("s9t0u1 | 查询手动任务列表失败", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                ManualTaskListResponse(
                    tasks = emptyList(),
                    totalElements = 0,
                    totalPages = 0,
                    currentPage = page,
                    size = size
                )
            )
        }
    }

    /**
     * 根据UUID获取任务详情 (Get task detail by UUID)
     *
     * GET /api/v1/lineage/manual-tasks/{taskUuid}
     */
    @GetMapping("/{taskUuid}")
    fun getTaskDetail(@PathVariable taskUuid: String): ResponseEntity<ManualTaskDetailResponse> {
        return try {
            logger.info("v2w3x4 | 获取任务详情，UUID: $taskUuid")
            
            val detail = manualLineageTriggerTaskService.getTaskDetail(taskUuid)
            
            if (detail != null) {
                logger.info("y5z6a7 | 成功获取任务详情，UUID: $taskUuid")
                ResponseEntity.ok(detail)
            } else {
                logger.warn("b8c9d0 | 任务不存在，UUID: $taskUuid")
                ResponseEntity.notFound().build()
            }
            
        } catch (e: Exception) {
            logger.error("e1f2g3 | 获取任务详情失败，UUID: $taskUuid", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()
        }
    }

    /**
     * 取消手动任务 (Cancel manual task)
     *
     * PUT /api/v1/lineage/manual-tasks/{taskUuid}/cancel
     */
    @PutMapping("/{taskUuid}/cancel")
    fun cancelManualTask(@PathVariable taskUuid: String): ResponseEntity<Map<String, Any>> {
        return try {
            logger.info("h4i5j6 | 取消手动任务，UUID: $taskUuid")
            
            val success = manualLineageTriggerTaskService.cancelManualTask(taskUuid)
            
            if (success) {
                logger.info("k7l8m9 | 手动任务取消成功，UUID: $taskUuid")
                ResponseEntity.ok(mapOf<String, Any>(
                    "success" to true,
                    "message" to "任务已成功取消",
                    "taskUuid" to taskUuid
                ))
            } else {
                logger.warn("n0o1p2 | 手动任务取消失败，UUID: $taskUuid")
                ResponseEntity.badRequest().body(mapOf<String, Any>(
                    "success" to false,
                    "message" to "任务取消失败",
                    "taskUuid" to taskUuid
                ))
            }
            
        } catch (e: IllegalArgumentException) {
            logger.warn("q3r4s5 | 任务不存在或状态不允许取消，UUID: $taskUuid, 错误: ${e.message}")
            ResponseEntity.badRequest().body(mapOf<String, Any>(
                "success" to false,
                "message" to (e.message ?: "任务状态不允许取消"),
                "taskUuid" to taskUuid
            ))
        } catch (e: IllegalStateException) {
            logger.warn("t6u7v8 | 任务状态不允许取消，UUID: $taskUuid, 错误: ${e.message}")
            ResponseEntity.badRequest().body(mapOf<String, Any>(
                "success" to false,
                "message" to (e.message ?: "任务状态不允许取消"),
                "taskUuid" to taskUuid
            ))
        } catch (e: Exception) {
            logger.error("w9x0y1 | 取消手动任务时发生错误，UUID: $taskUuid", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(mapOf<String, Any>(
                "success" to false,
                "message" to "服务器内部错误: ${e.message}",
                "taskUuid" to taskUuid
            ))
        }
    }

    /**
     * 获取数据源任务统计信息 (Get datasource task statistics)
     *
     * GET /api/v1/lineage/manual-tasks/datasource/{datasourceId}/statistics
     */
    @GetMapping("/datasource/{datasourceId}/statistics")
    fun getDatasourceTaskStatistics(
        @PathVariable datasourceId: Long
    ): ResponseEntity<DatasourceTaskStatistics> {
        return try {
            logger.info("z2a3b4 | 获取数据源任务统计，ID: $datasourceId")
            
            val statistics = manualLineageTriggerTaskService.getDatasourceTaskStatistics(datasourceId)
            
            logger.info("c5d6e7 | 成功获取数据源任务统计，ID: $datasourceId")
            ResponseEntity.ok(statistics)
            
        } catch (e: Exception) {
            logger.error("f8g9h0 | 获取数据源任务统计失败，ID: $datasourceId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()
        }
    }

    /**
     * 根据数据源ID查询任务列表 (Query tasks by datasource ID)
     *
     * GET /api/v1/lineage/manual-tasks/datasource/{datasourceId}
     */
    @GetMapping("/datasource/{datasourceId}")
    fun queryTasksByDatasourceId(
        @PathVariable datasourceId: Long,
        @RequestParam(required = false) taskStatus: ManualTaskStatus?,
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "20") size: Int,
        @RequestParam(defaultValue = "createdAt") sortBy: String,
        @RequestParam(defaultValue = "desc") sortDir: String
    ): ResponseEntity<ManualTaskListResponse> {
        return try {
            logger.info("i1j2k3 | 根据数据源ID查询任务列表，ID: $datasourceId, 状态: $taskStatus")
            
            val criteria = ManualTaskQueryCriteria(
                datasourceId = datasourceId,
                taskStatus = taskStatus
            )
            
            val direction = if (sortDir.lowercase() == "desc") Sort.Direction.DESC else Sort.Direction.ASC
            val sort = Sort.by(direction, sortBy)
            val pageable = PageRequest.of(page, size, sort)
            
            val response = manualLineageTriggerTaskService.queryManualTasks(criteria, pageable)
            
            logger.info("l4m5n6 | 根据数据源ID查询到 ${response.totalElements} 个任务")
            ResponseEntity.ok(response)
            
        } catch (e: Exception) {
            logger.error("o7p8q9 | 根据数据源ID查询任务列表失败，ID: $datasourceId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                ManualTaskListResponse(
                    tasks = emptyList(),
                    totalElements = 0,
                    totalPages = 0,
                    currentPage = page,
                    size = size
                )
            )
        }
    }
}