# 血缘管理项目架构设计文档

## 1. 项目概述 (Project Overview)

本项目旨在构建一个数据治理系统中的血缘管理模块。核心功能是解析和展示数据表及字段间的上下游关系，帮助用户理解数据流向、追溯数据来源、评估变更影响。主要包括血缘任务管理 (Lineage Task Management)、血缘目录 (Lineage Catalog) 和脚本影响分析 (Script Impact Analysis) 三大功能模块。

## 2. 原始需求概要 (Original Requirements Summary)

需求主要来源于用户提供的 "血缘管理.pdf" 文档，核心功能点如下：

### 2.1 血缘任务管理 (Lineage Task Management)

* **任务定义与调度 (Task Definition & Scheduling)**:
    * 系统预设两类固定采集任务：
        1.  解析内部数据交互平台(DataExchangePlatform)：从平台库表中提取SQL并解析。
        2.  解析大数据平台Shell脚本：提取SQL并解析。
    * 任务不可删除，可调整采集时间、暂停/启用。
    * 支持手动触发执行。
* **状态与结果监控 (Status & Result Monitoring)**:
    * 任务状态：启用 (Enable)、停止 (Stop)。
    * 采集结果：成功 (Success)、采集中 (Collecting)、失败 (Failure)。
* **日志管理 (Log Management)**:
    * 记录采集任务的执行历史和详细日志。
    * 支持按条件筛选和查看日志（如近一个月记录）。
* **操作管理 (Operations Management)**:
    * 启用/暂停任务。
    * 设置采集时间。
    * 查看采集结果和日志。
    * 手动执行采集。
    * 对于"采集中"的任务，支持手动终止 (Kill) 或刷新状态。

### 2.2 血缘目录 (Lineage Catalog)

* **系统与数据源管理 (System & Data Source Management)**:
    * 按归属系统对采集到的数据源进行分类展示，形成系统目录树。
    * 展示数据源详细信息（名称、类型、链接、采集来源、库/Schema、状态等）。
    * 支持数据源搜索、批量采集、通过Excel模板人工导入血缘关系、刷新状态、查看日志、删除人工导入的数据源。
* **血缘关系展示与交互 (Lineage Relationship Display & Interaction)**:
    * 列表形式展示表级别血缘关系。
    * **图形化血缘 (Graphical Lineage)**:
        * 以当前表为中心，图形化展示其上下游表关系（默认2层，可逐级展开）。
        * 节点颜色区分层级。
        * 点击表节点，显示详细属性（中英文名、所属系统、数据库链接、类型、库名、Schema、同步频率、加工规则SQL、数据同步范围、软开需求编号等），部分信息可编辑。
    * **字段级血缘 (Field-Level Lineage)**:
        * 支持展开表字段间的加工关系。
        * 区分系统采集（实线箭头）和人工维护（虚线箭头）的关系。
        * 点击字段高亮其关联路径。
    * **关系维护 (Relationship Maintenance)**:
        * 支持在图形界面通过右键菜单对表节点新增/编辑/删除人工维护的上下游表字段关系。
        * 选择目标表（支持关键字搜索）并配置字段映射。
        * 保存时校验关系是否已存在。系统采集的字段关系不可编辑。
    * **视图操作 (View Operations)**: 支持按表名搜索、全部展开节点、按系统分组展示。

### 2.3 脚本影响分析 (Script Impact Analysis)

* **脚本上传与管理 (Script Upload & Management)**:
    * 用户上传SQL脚本 (.sql) 或Shell脚本 (.sh)。
    * 记录脚本名称、类型、上传人、上传时间。
* **影响分析与展示 (Impact Analysis & Display)**:
    * 解析上传脚本，提取涉及的表和字段，形成临时血缘关系（脚本解析出的节点用特定颜色标识，关系用虚线）。
    * 结合元数据仓库中已有的血缘关系，将脚本涉及的节点及其已存在的上下游关系一并展示。
    * 清晰展示执行该脚本可能影响到的系统、表和字段范围。
    * 若脚本包含多个独立的血缘关系链，分块展示。
* **脚本删除 (Script Deletion)**: 支持删除已上传的脚本文件。

## 3. 技术栈与开发约定 (Technical Stack & Conventions)

* **后端框架 (Backend Framework)**: Spring Boot
* **编程语言 (Programming Language)**: Kotlin
* **构建工具 (Build Tool)**: Maven
* **项目结构 (Project Structure)**: 单模块 (Single Module)。为简化初期开发和部署，本项目采用单模块结构。通过**扁平化包结构 (Flattened Package Structure)** 来保证代码的逻辑分离和模块化，类似于Go项目的组织方式。
* **包组织原则 (Package Organization Principles)**: 
    * 采用扁平化包结构，避免深层次嵌套
    * 按功能领域 (functional domain) 进行顶级包划分
    * 包内文件按类型和职责组织，而不是按架构层次
    * 优先使用清晰的文件命名而非复杂的包层次结构
* **接口使用 (Interface Usage)**: 当某个组件只有一个实现时，不创建接口。
* **实现类命名 (Implementation Class Naming)**: 不使用 `Impl` 或类似后缀。
* **包命名 (Package Naming)**: 纯小写，多单词直接连接，不使用下划线 (`_`)。

## 4. 架构设计 (Architecture Design)

### 4.1 总体架构与包结构 (Overall Architecture & Package Structure)

项目采用**扁平化包结构 (Flattened Package Structure)**，结合领域驱动设计思想，但避免深层次的包嵌套。采用数据为中心的设计原则 (Data-Oriented Design)，将核心数据模型和相关功能就近组织。

```
com.datayes                       // 根包 (Root Package)
├── App.kt                        // Spring Boot 主启动类和配置 (Main Application Class & Configuration)
│
├── parser/                       // 解析器模块 (Parser Module) - 核心共享功能
│   ├── Lineage.kt               // 核心数据模型定义 (Core Data Models)
│   │                            //   - DataLineage, TableLineage, ColumnLineage
│   │                            //   - TableInfo, ColumnInfo, DatabaseInfo
│   │                            //   - DataTransformation, LineageResult
│   ├── SqlParser.kt             // SQL解析器 (SQL Parser)
│   ├── SqlParseResult.kt        // SQL解析结果模型 (SQL Parse Result Models)
│   ├── LineageConverter.kt      // 血缘转换器 (Lineage Converter)
│   ├── DataExchangeJob.kt       // 数据交互作业模型 (Data Exchange Job Model)
│   └── SqlParsingException.kt   // 解析异常定义 (Parsing Exceptions)
│
├── lineagetask/                    // 血缘任务管理 (Lineage Task Management)
│   ├── DataExchangeJobService.kt      // 数据交互作业服务 (Service Layer)
│   ├── DataExchangeJobRepository.kt   // 数据访问层 (Data Access Layer)
│   ├── DataExchangeJobProcessor.kt    // 作业处理器 (Job Processor)
│   └── DataExchangeDataSource.kt      // 数据源配置 (Data Source Configuration)
│
├── lineagecatalog/                // 血缘目录 (Lineage Catalog) - 待实现
│   ├── LineageCatalogService.kt       // 血缘目录服务
│   ├── LineageGraphService.kt         // 血缘图谱服务
│   ├── LineageCatalogRepository.kt    // 数据访问层
│   └── ExcelLineageImporter.kt        // Excel导入功能
│
└── scriptimpactanalysis/         // 脚本影响分析 (Script Impact Analysis) - 待实现
    ├── ScriptImpactService.kt         // 脚本影响分析服务
    ├── ImpactAnalysisEngine.kt        // 影响分析引擎
    ├── ScriptImpactRepository.kt      // 数据访问层
    └── UploadedScript.kt              // 上传脚本模型
```

### 4.2 扁平化包结构的设计理念 (Flattened Package Structure Design Philosophy)

* **减少包层次复杂性 (Reduced Package Hierarchy Complexity)**: 避免传统的 `api/service/repository/dto/entity` 等多层嵌套结构，减少导航复杂性。

* **就近原则 (Proximity Principle)**: 相关功能的代码文件放置在同一包中，提高代码的内聚性 (cohesion) 和可读性。

* **清晰的文件命名 (Clear File Naming)**: 使用具有明确语义的文件名来表达其职责，如 `DataExchangeJobService.kt`、`LineageConverter.kt`。

* **功能导向的包划分 (Function-Oriented Package Division)**: 按照业务功能领域进行顶级包划分，而不是按技术架构层次。

* **核心模型集中化 (Centralized Core Models)**: 将核心数据模型（如 `Lineage.kt`）放置在共享包中，避免循环依赖。

### 4.3 核心组件与职责 (Core Component Responsibilities)

* **`App.kt`**: 
    * Spring Boot 主启动类
    * 基础配置类（如 Jackson 配置）
    * 避免配置分散，保持简洁

* **`parser` 包**: 作为核心共享模块 (Core Shared Module)
    * `Lineage.kt`: 定义整个系统的核心数据模型，包括 `DataLineage`、`TableLineage`、`ColumnLineage` 等
    * `SqlParser.kt`: SQL解析核心逻辑，支持多种数据库方言
    * `LineageConverter.kt`: 将原始作业数据转换为标准血缘模型的转换器
    * `DataExchangeJob.kt`: 数据交互平台作业的数据模型
    * `SqlParseResult.kt`: SQL解析结果的数据传输对象

* **`lineagetask` 包**: 血缘任务管理领域
    * `DataExchangeJobService.kt`: 核心业务服务，实现函数式核心、命令式外壳 (functional core, imperative shell) 架构
    * `DataExchangeJobRepository.kt`: 数据访问层，封装数据库查询逻辑
    * `DataExchangeJobProcessor.kt`: 批量作业处理器
    * `DataExchangeDataSource.kt`: 数据源配置和连接管理

* **待实现的包**:
    * `lineagecatalog`: 血缘目录展示、图谱构建、人工维护功能
    * `scriptimpactanalysis`: 脚本影响分析、临时血缘构建功能

### 4.4 关键设计决策 (Key Design Decisions)

* **函数式核心、命令式外壳架构 (Functional Core, Imperative Shell)**:
    * **函数式核心**: 如 `LineageConverter.convertToLineage()` 等核心算法实现为纯函数，易于测试和复用
    * **命令式外壳**: 服务层处理数据库访问、日志记录等副作用操作

* **数据为中心的设计 (Data-Oriented Design)**:
    * 优先定义核心数据结构（`Lineage.kt` 中的各种数据类）
    * 围绕数据转换构建业务逻辑
    * 使用 Kotlin 的数据类 (Data Classes) 特性，提供不可变性 (immutability)

* **单一职责原则 (Single Responsibility Principle)**:
    * 每个文件专注于单一功能点
    * `SqlParser.kt` 只负责SQL解析
    * `LineageConverter.kt` 只负责数据转换
    * `DataExchangeJobService.kt` 只负责作业管理业务逻辑

* **避免过度抽象 (Avoid Over-Abstraction)**:
    * 不为简单功能创建不必要的接口
    * 使用具体类实现，提高代码的直观性

### 4.5 实际项目状态 (Current Project Status)

目前已实现的核心功能包括：

1. **SQL解析引擎 (SQL Parsing Engine)**: 
   - 支持多种数据库方言的SQL解析
   - 提取表和字段的依赖关系
   - 生成结构化的解析结果

2. **数据交互平台集成 (Data Platform Integration)**:
   - 从内部数据交互平台(DataExchangePlatform)提取作业信息
   - 解析作业中的SQL语句
   - 构建表级和字段级血缘关系

3. **血缘转换器 (Lineage Converter)**:
   - 将原始作业数据转换为标准化的血缘模型
   - 支持复杂的字段映射和数据转换关系
   - 提供详细的错误和警告信息

4. **完整的测试覆盖 (Complete Test Coverage)**:
   - SQL解析器的单元测试
   - 血缘转换逻辑的测试
   - 数据模型的验证测试

这种扁平化的包结构已经在实际开发中证明了其有效性，降低了代码导航的复杂性，提高了开发效率。

## 5. 后续步骤 (Next Steps)

1.  搭建项目骨架，配置Maven依赖。
2.  详细设计数据库表结构（或图数据库模型）。
3.  优先实现核心的SQL解析和血缘关系存储与查询功能。
4.  逐步实现血缘任务管理、血缘目录展示和脚本影响分析模块。
5.  编写单元测试和集成测试。
