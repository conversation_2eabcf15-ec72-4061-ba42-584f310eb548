package com.datayes.sql

/**
 * SQL解析结果 (SQL parsing result)
 *
 * @property tables 提取的表信息列表
 * @property columns 提取的列信息列表
 */
data class ParseResult(
    val tables: List<TableReference>,
    val columns: List<ColumnReference>,
)

/**
 * 提取的表信息 (Extracted table information)
 *
 * @property schema 模式名，可能为null
 * @property name 表名
 * @property alias 表别名，可能为null
 */
data class TableReference(
    val schema: String?,
    val name: String,
    val alias: String?,
)

/**
 * 提取的列信息 (Extracted column information)
 *
 * @property name 列名或表达式
 * @property tablePrefix 表前缀，可能为null
 * @property alias 列别名，可能为null
 * @property originalExpression 原始表达式字符串
 * @property isWildcard 是否为通配符列 (*)
 */
data class ColumnReference(
    val name: String,
    val tablePrefix: String?,
    val alias: String?,
    val originalExpression: String,
    val isWildcard: Boolean,
)

/**
 * 数据修改语句解析结果 (Data modification statement parsing result)
 * 用于INSERT/UPDATE/DELETE等包含源表和目标表的语句
 *
 * @property targetTable 目标表信息
 * @property targetColumns 目标列信息列表，可能为null（如INSERT INTO table SELECT * 情况）
 * @property sourceTables 源表信息列表
 * @property sourceColumns 源列信息列表
 * @property columnMappings 列映射关系（源列到目标列）
 */
data class DataModificationResult(
    val targetTable: TableReference,
    val targetColumns: List<String>?, // 目标列名列表，INSERT INTO table (col1, col2) 情况
    val sourceTables: List<TableReference>,
    val sourceColumns: List<ColumnReference>,
    val columnMappings: List<ColumnMapping>,
)

/**
 * 列映射关系 (Column mapping)
 * 表示源列到目标列的映射
 *
 * @property sourceColumn 源列引用
 * @property targetColumnName 目标列名
 * @property targetColumnIndex 目标列在SELECT列表中的索引位置
 */
data class ColumnMapping(
    val sourceColumn: ColumnReference,
    val targetColumnName: String?,
    val targetColumnIndex: Int,
)