#!/bin/bash
hdfs dfs -cat /project/dwd/tbds_config.properties > tbds_config.properties
source tbds_config.properties
hdfs dfs -cat /project/dwd/tbds_config.sh > tbds_config.sh
source tbds_config.sh

export yesterday=$1
export nominalformateDate=`date -d "${yesterday} +1 days" "+%Y-%m-%d"`


# TODO 浮动利率类型 码表缺少映射
#所属主题：保全信息
#功能描述；保单还款信息表
#创建者；王海洋
#创建日期；2025-01-02
#修改日志
#修改日期                    修改人                    修改内容
#
#表字段

#group_contract_no                  团体保单号
#contract_no                        个人保单号
#product_no                         保单险种号
#accept_no                          保全受理编号
#endor_no                           保全批单编号
#relato_endor_no                    原借款或自垫保全批单号
#interest_type_code                 借款利率方式代码
#interest_type                      借款利率方式
#interest_mode_code                 固定利率类型代码
#interest_mode                      固定利率类型
#floatrate_type_code                浮动利率类型代码
#floatrate_type                     浮动利率类型
#interest_rate                      利率
#amount                             借款已领金额
#repay_type_code                    还款方式代码
#repay_type                         还款方式
#payoff_flag                        还清标志
#payoff_date                        还清日期
#repay_amount                       还款本金
#repay_interest                     还款利息
#receivable_interest                应还款利息
#balance_amount                     余额
#operator                           操作人员
#make_time                          入机时间
#modify_time                        修改时间
#first_hadoop_opt_time              首次入表时间
#last_hadoop_opt_time               最后一次更新时间

${hive} -e "
with lpedoritem_group_eece as(
select edoracceptno,edortype,contno,edorno
  from dorado_endorse.full_lpedoritem
 group by edoracceptno,edortype,contno,edorno
)
insert overwrite table dwd.temp_dwd_endorse_ljloanrepayment_item
select pk,contno,edorno,edoracceptno,edortype from (select edorno as pk,contno,edorno,edoracceptno,edortype from lpedoritem_group_eece
  union all select edoracceptno as pk,contno,edorno,edoracceptno,edortype from lpedoritem_group_eece where edorno<>edoracceptno) a group by pk,contno,edorno,edoracceptno,edortype;
 
insert overwrite table dwd.temp_dwd_endorse_ljloanrepayment_item
select pk,contno,edorno,edoracceptno,edortype
from dwd.temp_dwd_endorse_ljloanrepayment_item a
where exists
    (select 1
     from
       (select contno
        , pk
        , count(1) as aa
        from dwd.temp_dwd_endorse_ljloanrepayment_item
        group by contno
        , pk
        having count(1) >=2) b
     where a.contno=b.contno
     and a.pk=b.pk )
and EDORTYPE in ('RF', 'TR')
union all
select *
From dwd.temp_dwd_endorse_ljloanrepayment_item a
where exists
    (select 1
     from
       (select contno
        , pk
        , count(1) as aa
        from dwd.temp_dwd_endorse_ljloanrepayment_item
        group by contno
        , pk
        having count(1)=1) b
     where a.contno=b.contno
     and a.pk=b.pk);  
"
exitCodeCheck $? '还款保全临时数据提取  失败' '还款保全临时数据提取  成功'

${hive} -e "
insert overwrite table dwd.dwd_endorse_ljloanrepayment
select null                                         as group_contract_no  
      ,lor.contno                                   as contract_no        
      ,lor.polno                                    as product_no         
      ,nvl(item.edoracceptno,lor.edorno)            as accept_no
      ,nvl(item.edorno,lor.edorno)                  as endor_no
      ,lor.loanno                                   as relato_endor_no    
      ,lor.interesttype                             as interest_type_code 
      ,ld1.value                                    as interest_type
      ,lor.interestmode                             as interest_mode_code 
      ,ld2.value                                    as interest_mode
      ,lor.ratecaltype                              as floatrate_type_code
      ,ld4.value                                    as floatrate_type
      ,lor.interestrate                             as interest_rate      
      ,lor.summoney                                 as amount             
      ,lor.repayment                                as repay_type_code    
      ,ld3.value                                    as repay_type
      ,lor.payoffflag                               as payoff_flag        
      ,nvl(lor.payoffdate,lor.modifydate)           as payoff_date        
      ,lor.returnmoney                              as repay_amount       
      ,lor.returninterest                           as repay_interest     
      ,lor.receivableinterest                       as receivable_interest
      ,lor.leavemoney                               as balance_amount     
      ,lor.operator                                 as operator           
      ,concat(lor.makedate,' ',lor.maketime)        as make_time        
      ,concat(lor.modifydate,' ',lor.modifytime)    as modify_time        
      ,from_unixtime(unix_timestamp(current_timestamp), 'YYYY-MM-dd HH:mm:ss') as first_hadoop_opt_time
      ,from_unixtime(unix_timestamp(current_timestamp), 'YYYY-MM-dd HH:mm:ss') as last_hadoop_opt_time
from gemini_plcy.full_loreturnloan lor
left join dwd.temp_dwd_endorse_ljloanrepayment_item item
on item.contno = lor.contno 
and item.edortype not in('LN','LR','CB','PC')
and item.pk = lor.edorno
left join dwd.dim_dictionary ld1
on ld1.type = 'interesttype'
and lor.interesttype = ld1.code
left join dwd.dim_dictionary ld2
on ld2.type = 'interestmode'
and lor.interestmode = ld2.code
left join dwd.dim_dictionary ld3
on ld3.type = 'return_type'
and lor.repayment = ld3.code
left join dwd.dim_dictionary ld4
on ld4.type = 'floatrate_type'
and lor.ratecaltype = ld4.code "
exitCodeCheck $? '提取个险还款/还垫信息  失败' '提取个险还款/还垫信息  成功'

