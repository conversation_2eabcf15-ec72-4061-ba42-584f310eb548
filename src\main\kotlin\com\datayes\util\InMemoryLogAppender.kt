package com.datayes.util

import ch.qos.logback.classic.spi.ILoggingEvent
import ch.qos.logback.core.AppenderBase
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * In-memory log appender that stores log events in a circular buffer.
 * This allows the application to display recent logs in the web UI.
 */
class InMemoryLogAppender : AppenderBase<ILoggingEvent>() {
    companion object {
        // Circular buffer to store log events (limited to last 10000 entries)
        private val logEvents = ConcurrentLinkedQueue<LogEntry>()
        private const val MAX_SIZE = 10000

        /**
         * Get all stored log events
         */
        fun getLogs(): List<LogEntry> {
            return logEvents.toList()
        }

        /**
         * Search logs by text
         */
        fun searchLogs(searchText: String): List<LogEntry> {
            return if (searchText.isBlank()) {
                logEvents.toList()
            } else {
                logEvents.filter { 
                    it.message.contains(searchText, ignoreCase = true) || 
                    it.loggerName.contains(searchText, ignoreCase = true) ||
                    it.level.contains(searchText, ignoreCase = true)
                }
            }
        }

        /**
         * Clear all stored logs
         */
        fun clearLogs() {
            logEvents.clear()
        }

        /**
         * Keep only logs from the last N minutes and remove older ones
         * 保留最近 N 分钟的日志，移除更早的日志
         * 
         * @param minutes 要保留的分钟数 (number of minutes to keep)
         * @return 被移除的日志条数 (number of logs removed)
         */
        fun keepLastMinutesLogs(minutes: Int): Int {
            val cutoffTime = System.currentTimeMillis() - (minutes * 60 * 1000L)
            val originalSize = logEvents.size
            
            // Remove logs older than the cutoff time
            val iterator = logEvents.iterator()
            while (iterator.hasNext()) {
                val log = iterator.next()
                if (log.timestamp < cutoffTime) {
                    iterator.remove()
                } else {
                    // Since logs are added in chronological order, we can break early
                    // 由于日志是按时间顺序添加的，我们可以提前退出
                    break
                }
            }
            
            return originalSize - logEvents.size
        }
    }

    override fun append(event: ILoggingEvent) {
        // Create a log entry from the event
        val logEntry = LogEntry(
            timestamp = event.timeStamp,
            level = event.level.toString(),
            loggerName = event.loggerName,
            message = event.formattedMessage,
            threadName = event.threadName,
            requestId = event.mdcPropertyMap["requestId"] ?: "",
            fileName = event.callerData.firstOrNull()?.fileName ?: "",
            lineNumber = event.callerData.firstOrNull()?.lineNumber ?: 0
        )

        // Add to the circular buffer
        logEvents.add(logEntry)

        // Ensure we don't exceed the maximum size
        while (logEvents.size > MAX_SIZE) {
            logEvents.poll()
        }
    }
}

/**
 * Data class representing a log entry
 */
data class LogEntry(
    val timestamp: Long,
    val level: String,
    val loggerName: String,
    val message: String,
    val threadName: String,
    val requestId: String,
    val fileName: String,
    val lineNumber: Int
) {
    val formattedTime: String
        get() {
            val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")
                .withZone(ZoneId.of("Asia/Shanghai"))
            return formatter.format(Instant.ofEpochMilli(timestamp))
        }
}