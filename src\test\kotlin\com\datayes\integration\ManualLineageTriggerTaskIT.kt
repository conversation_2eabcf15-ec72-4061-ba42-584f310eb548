package com.datayes.integration

import io.restassured.RestAssured.given
import io.restassured.http.ContentType
import io.restassured.path.json.JsonPath
import org.assertj.core.api.Assertions.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.*
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.*

/**
 * 手动触发血缘任务API集成测试 (Manual Lineage Trigger Task API Integration Tests)
 *
 * 测试手动触发血缘任务的完整API功能，包括任务创建、查询、统计和取消等操作
 * 
 * 测试原则：
 * - 只进行只读操作，不删除或更新现有数据
 * - 假设数据库中存在有效的数据源记录
 * - 使用真实的HTTP请求测试运行中的应用程序
 * - 每个日志消息使用唯一UUID前缀以便追踪
 */
@DisplayName("手动触发血缘任务API集成测试 - Manual Lineage Trigger Task API Integration Tests")
@TestMethodOrder(MethodOrderer.OrderAnnotation::class)
class ManualLineageTriggerTaskIT : RestApiIntegrationTestBase() {

    companion object {
        private const val TEST_TRIGGER_USER = "integration-test-user"
        private const val MANUAL_TASKS_BASE_PATH = "/v1/lineage/manual-tasks"
        
        // 测试用的数据源ID - 假设数据库中存在ID为1的数据源
        private const val TEST_DATASOURCE_ID = 1L
        private const val INVALID_DATASOURCE_ID = 999999L
        
        // 用于存储测试过程中创建的任务UUID
        private var createdTaskUuid: String? = null
        private val dateTimeFormatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME
    }

    @Test
    @Order(1)
    @DisplayName("应该成功触发手动血缘任务 - Should successfully trigger manual lineage task")
    fun `should successfully trigger manual lineage task`() {
        println("a1b2c3d4 | 开始测试手动触发血缘任务")

        val requestBody = mapOf(
            "datasourceId" to TEST_DATASOURCE_ID,
            "triggerUser" to TEST_TRIGGER_USER
        )

        val response = given()
            .contentType(ContentType.JSON)
            .body(requestBody)
            .`when`()
            .post("$MANUAL_TASKS_BASE_PATH/trigger")
            .then()
            .statusCode(200)
            .contentType("application/json")
            .body("taskUuid", notNullValue())
            .body("datasourceId", equalTo(TEST_DATASOURCE_ID.toInt()))
            .body("taskStatus", equalTo("PENDING"))
            .body("message", notNullValue())
            .body("createdAt", notNullValue())
            .extract()
            .response()

        // 使用JsonPath进行详细验证
        val jsonPath = JsonPath.from(response.asString())
        val taskUuid = jsonPath.getString("taskUuid")
        val taskStatus = jsonPath.getString("taskStatus")
        val message = jsonPath.getString("message")

        // 使用AssertJ进行复杂断言
        assertThat(taskUuid)
            .isNotNull()
            .matches("[a-f0-9-]{36}")  // UUID格式验证

        assertThat(taskStatus).isEqualTo("PENDING")
        assertThat(message).contains("手动血缘任务")

        // 保存任务UUID供后续测试使用
        createdTaskUuid = taskUuid

        println("e5f6g7h8 | 手动血缘任务触发成功: taskUuid=$taskUuid, status=$taskStatus")
        
        // 注意：任务现在会处理两种类型的作业：
        // 1. Data Exchange Jobs - 基于数据源连接字符串匹配
        // 2. HDFS Shell Script Jobs - 占位符实现（当HDFS路径与表血缘关联功能准备好后实现）
    }

    @Test
    @Order(2)
    @DisplayName("应该验证触发请求参数 - Should validate trigger request parameters")
    fun `should validate trigger request parameters`() {
        println("i9j0k1l2 | 开始测试触发请求参数验证")

        // 测试缺少datasourceId的情况
        val invalidRequestBody = mapOf(
            "triggerUser" to TEST_TRIGGER_USER
            // 缺少datasourceId
        )

        given()
            .contentType(ContentType.JSON)
            .body(invalidRequestBody)
            .`when`()
            .post("$MANUAL_TASKS_BASE_PATH/trigger")
            .then()
            .statusCode(400)

        println("m3n4o5p6 | 参数验证测试完成")
    }

    @Test
    @Order(3)
    @DisplayName("应该成功查询任务列表 - Should successfully query manual task list")
    fun `should successfully query manual task list`() {
        println("q7r8s9t0 | 开始测试查询任务列表")

        val response = given()
            .queryParam("page", 0)
            .queryParam("size", 10)
            .queryParam("sortBy", "createdAt")
            .queryParam("sortDir", "desc")
            .`when`()
            .get(MANUAL_TASKS_BASE_PATH)
            .then()
            .statusCode(200)
            .contentType("application/json")
            .body("tasks", notNullValue())
            .body("totalElements", greaterThanOrEqualTo(0))
            .body("totalPages", greaterThanOrEqualTo(0))
            .body("currentPage", equalTo(0))
            .body("size", equalTo(10))
            .extract()
            .response()

        // 验证响应结构
        val jsonPath = JsonPath.from(response.asString())
        val tasks = jsonPath.getList<Map<String, Any>>("tasks")
        val totalElements = jsonPath.getLong("totalElements")

        assertThat(tasks).isNotNull()
        assertThat(totalElements).isGreaterThanOrEqualTo(0)

        // 如果有任务，验证任务结构
        if (tasks.isNotEmpty()) {
            val firstTask = tasks[0]
            assertThat(firstTask).containsKeys("id", "taskUuid", "datasourceId", "taskStatus", "createdAt")
        }

        println("u1v2w3x4 | 任务列表查询成功: totalElements=$totalElements, tasksCount=${tasks.size}")
    }

    @Test
    @Order(4)
    @DisplayName("应该支持任务列表过滤查询 - Should support filtered task list query")
    fun `should support filtered task list query`() {
        println("y5z6a7b8 | 开始测试过滤查询")

        // 按数据源ID过滤
        val response = given()
            .queryParam("datasourceId", TEST_DATASOURCE_ID)
            .queryParam("triggerUser", TEST_TRIGGER_USER)
            .queryParam("page", 0)
            .queryParam("size", 5)
            .`when`()
            .get(MANUAL_TASKS_BASE_PATH)
            .then()
            .statusCode(200)
            .contentType("application/json")
            .body("tasks", notNullValue())
            .extract()
            .response()

        val jsonPath = JsonPath.from(response.asString())
        val tasks = jsonPath.getList<Map<String, Any>>("tasks")

        // 验证过滤结果
        if (tasks.isNotEmpty()) {
            tasks.forEach { task ->
                assertThat(task["datasourceId"]).isEqualTo(TEST_DATASOURCE_ID.toInt())
                if (task["triggerUser"] != null) {
                    assertThat(task["triggerUser"]).isEqualTo(TEST_TRIGGER_USER)
                }
            }
        }

        println("c9d0e1f2 | 过滤查询测试完成: 找到${tasks.size}个匹配的任务")
    }

    @Test
    @Order(5)
    @DisplayName("应该成功获取任务详情 - Should successfully get task detail")
    fun `should successfully get task detail`() {
        println("g3h4i5j6 | 开始测试获取任务详情")

        // 如果有创建的任务UUID，测试获取详情
        if (createdTaskUuid != null) {
            val response = given()
                .pathParam("taskUuid", createdTaskUuid!!)
                .`when`()
                .get("$MANUAL_TASKS_BASE_PATH/{taskUuid}")
                .then()
                .statusCode(200)
                .contentType("application/json")
                .body("id", notNullValue())
                .body("taskUuid", equalTo(createdTaskUuid))
                .body("datasourceId", equalTo(TEST_DATASOURCE_ID.toInt()))
                .body("taskStatus", notNullValue())
                .body("createdAt", notNullValue())
                .extract()
                .response()

            val jsonPath = JsonPath.from(response.asString())
            val taskStatus = jsonPath.getString("taskStatus")
            val successCount = jsonPath.getInt("successCount")
            val failureCount = jsonPath.getInt("failureCount")

            assertThat(taskStatus).isIn("PENDING", "RUNNING", "SUCCESS", "FAILED")
            assertThat(successCount).isGreaterThanOrEqualTo(0)
            assertThat(failureCount).isGreaterThanOrEqualTo(0)

            println("k7l8m9n0 | 任务详情获取成功: taskUuid=$createdTaskUuid, status=$taskStatus")
            
            // 验证是否包含两种类型的作业处理结果
            if (jsonPath.getList<Map<String, Any>>("successResults").isNotEmpty()) {
                val successResults = jsonPath.getList<Map<String, Any>>("successResults")
                val hasDataExchangeJobs = successResults.any { it["itemType"] == "DATA_EXCHANGE_JOB" }
                val hasHdfsShellScriptJobs = successResults.any { it["itemType"] == "HDFS_SHELL_SCRIPT_JOB" }
                
                println("o1p2q3r4 | 处理结果类型统计: 数据交换作业=$hasDataExchangeJobs, HDFS脚本作业=$hasHdfsShellScriptJobs")
            }
        } else {
            println("o1p2q3r4 | 跳过任务详情测试，因为没有可用的任务UUID")
        }
    }

    @Test
    @Order(6)
    @DisplayName("应该处理不存在的任务UUID - Should handle non-existent task uuid")
    fun `should handle non-existent task uuid`() {
        println("s5t6u7v8 | 开始测试不存在的任务UUID")

        val nonExistentUuid = UUID.randomUUID().toString()

        given()
            .pathParam("taskUuid", nonExistentUuid)
            .`when`()
            .get("$MANUAL_TASKS_BASE_PATH/{taskUuid}")
            .then()
            .statusCode(404)

        println("w9x0y1z2 | 不存在的任务UUID测试完成: uuid=$nonExistentUuid")
    }

    @Test
    @Order(7)
    @DisplayName("应该成功获取数据源任务统计 - Should successfully get datasource task statistics")
    fun `should successfully get datasource task statistics`() {
        println("a3b4c5d6 | 开始测试数据源任务统计")

        val response = given()
            .pathParam("datasourceId", TEST_DATASOURCE_ID)
            .`when`()
            .get("$MANUAL_TASKS_BASE_PATH/datasource/{datasourceId}/statistics")
            .then()
            .statusCode(200)
            .contentType("application/json")
            .body("datasourceId", equalTo(TEST_DATASOURCE_ID.toInt()))
            .body("totalTasks", greaterThanOrEqualTo(0))
            .body("successfulTasks", greaterThanOrEqualTo(0))
            .body("failedTasks", greaterThanOrEqualTo(0))
            .body("runningTasks", greaterThanOrEqualTo(0))
            .body("pendingTasks", greaterThanOrEqualTo(0))
            .extract()
            .response()

        val jsonPath = JsonPath.from(response.asString())
        val totalTasks = jsonPath.getInt("totalTasks")
        val successfulTasks = jsonPath.getInt("successfulTasks")
        val failedTasks = jsonPath.getInt("failedTasks")

        assertThat(totalTasks).isEqualTo(successfulTasks + failedTasks + jsonPath.getInt("runningTasks") + jsonPath.getInt("pendingTasks"))

        println("e7f8g9h0 | 数据源统计获取成功: datasourceId=$TEST_DATASOURCE_ID, totalTasks=$totalTasks")
    }

    @Test
    @Order(8)
    @DisplayName("应该支持按数据源ID查询任务 - Should support query tasks by datasource id")
    fun `should support query tasks by datasource id`() {
        println("i1j2k3l4 | 开始测试按数据源ID查询任务")

        val response = given()
            .pathParam("datasourceId", TEST_DATASOURCE_ID)
            .queryParam("page", 0)
            .queryParam("size", 10)
            .`when`()
            .get("$MANUAL_TASKS_BASE_PATH/datasource/{datasourceId}")
            .then()
            .statusCode(200)
            .contentType("application/json")
            .body("tasks", notNullValue())
            .body("totalElements", greaterThanOrEqualTo(0))
            .extract()
            .response()

        val jsonPath = JsonPath.from(response.asString())
        val tasks = jsonPath.getList<Map<String, Any>>("tasks")

        // 验证所有返回的任务都属于指定的数据源
        tasks.forEach { task ->
            assertThat(task["datasourceId"]).isEqualTo(TEST_DATASOURCE_ID.toInt())
        }

        println("m5n6o7p8 | 按数据源ID查询完成: datasourceId=$TEST_DATASOURCE_ID, 找到${tasks.size}个任务")
    }

    @Test
    @Order(9)
    @DisplayName("应该支持分页查询参数 - Should support pagination parameters")
    fun `should support pagination parameters`() {
        println("q9r0s1t2 | 开始测试分页查询参数")

        // 测试第一页
        val firstPageResponse = given()
            .queryParam("page", 0)
            .queryParam("size", 2)
            .queryParam("sortBy", "createdAt")
            .queryParam("sortDir", "desc")
            .`when`()
            .get(MANUAL_TASKS_BASE_PATH)
            .then()
            .statusCode(200)
            .body("currentPage", equalTo(0))
            .body("size", equalTo(2))
            .extract()
            .response()

        val firstPageJsonPath = JsonPath.from(firstPageResponse.asString())
        val firstPageTasks = firstPageJsonPath.getList<Map<String, Any>>("tasks")
        val totalElements = firstPageJsonPath.getLong("totalElements")

        // 如果有足够的数据，测试第二页
        if (totalElements > 2) {
            val secondPageResponse = given()
                .queryParam("page", 1)
                .queryParam("size", 2)
                .queryParam("sortBy", "createdAt")
                .queryParam("sortDir", "desc")
                .`when`()
                .get(MANUAL_TASKS_BASE_PATH)
                .then()
                .statusCode(200)
                .body("currentPage", equalTo(1))
                .body("size", equalTo(2))
                .extract()
                .response()

            val secondPageJsonPath = JsonPath.from(secondPageResponse.asString())
            val secondPageTasks = secondPageJsonPath.getList<Map<String, Any>>("tasks")

            // 验证分页数据不重复
            if (firstPageTasks.isNotEmpty() && secondPageTasks.isNotEmpty()) {
                val firstPageUuids = firstPageTasks.map { it["taskUuid"] }
                val secondPageUuids = secondPageTasks.map { it["taskUuid"] }
                assertThat(firstPageUuids).doesNotContainAnyElementsOf(secondPageUuids)
            }
        }

        println("u3v4w5x6 | 分页查询测试完成: totalElements=$totalElements")
    }

    @Test
    @Order(10)
    @DisplayName("应该支持任务取消功能 - Should support task cancellation")
    fun `should support task cancellation`() {
        println("y7z8a9b0 | 开始测试任务取消功能")

        // 如果有创建的任务UUID，尝试取消它
        if (createdTaskUuid != null) {
            // 首先检查任务当前状态
            val detailResponse = given()
                .pathParam("taskUuid", createdTaskUuid!!)
                .`when`()
                .get("$MANUAL_TASKS_BASE_PATH/{taskUuid}")
                .then()
                .statusCode(200)
                .extract()
                .response()

            val currentStatus = JsonPath.from(detailResponse.asString()).getString("taskStatus")

            // 只有PENDING或RUNNING状态的任务可以取消
            if (currentStatus in listOf("PENDING", "RUNNING")) {
                val cancelResponse = given()
                    .pathParam("taskUuid", createdTaskUuid!!)
                    .`when`()
                    .put("$MANUAL_TASKS_BASE_PATH/{taskUuid}/cancel")
                    .then()
                    .statusCode(200)
                    .contentType("application/json")
                    .body("success", equalTo(true))
                    .body("taskUuid", equalTo(createdTaskUuid))
                    .extract()
                    .response()

                val jsonPath = JsonPath.from(cancelResponse.asString())
                val success = jsonPath.getBoolean("success")
                val message = jsonPath.getString("message")

                assertThat(success).isTrue()
                assertThat(message).contains("取消")

                println("c1d2e3f4 | 任务取消成功: taskUuid=$createdTaskUuid, message=$message")
            } else {
                // 尝试取消已完成的任务应该返回错误
                given()
                    .pathParam("taskUuid", createdTaskUuid!!)
                    .`when`()
                    .put("$MANUAL_TASKS_BASE_PATH/{taskUuid}/cancel")
                    .then()
                    .statusCode(400)
                    .body("success", equalTo(false))

                println("g5h6i7j8 | 任务状态不允许取消: taskUuid=$createdTaskUuid, status=$currentStatus")
            }
        } else {
            println("k9l0m1n2 | 跳过任务取消测试，因为没有可用的任务UUID")
        }
    }

    @Test
    @Order(11)
    @DisplayName("应该处理无效的数据源ID - Should handle invalid datasource id")
    fun `should handle invalid datasource id`() {
        println("o3p4q5r6 | 开始测试无效的数据源ID")

        // 测试无效的数据源ID进行统计查询
        given()
            .pathParam("datasourceId", INVALID_DATASOURCE_ID)
            .`when`()
            .get("$MANUAL_TASKS_BASE_PATH/datasource/{datasourceId}/statistics")
            .then()
            .statusCode(200)  // 应该返回空统计，而不是错误
            .body("datasourceId", equalTo(INVALID_DATASOURCE_ID.toInt()))
            .body("totalTasks", equalTo(0))

        println("s7t8u9v0 | 无效数据源ID测试完成: datasourceId=$INVALID_DATASOURCE_ID")
    }

    @Test
    @Order(12)
    @DisplayName("应该验证日期过滤参数 - Should validate date filter parameters")
    fun `should validate date filter parameters`() {
        println("w1x2y3z4 | 开始测试日期过滤参数")

        val now = LocalDateTime.now()
        val startDate = now.minusDays(7).format(dateTimeFormatter)
        val endDate = now.format(dateTimeFormatter)

        val response = given()
            .queryParam("startDate", startDate)
            .queryParam("endDate", endDate)
            .queryParam("page", 0)
            .queryParam("size", 10)
            .`when`()
            .get(MANUAL_TASKS_BASE_PATH)
            .then()
            .statusCode(200)
            .contentType("application/json")
            .body("tasks", notNullValue())
            .extract()
            .response()

        val jsonPath = JsonPath.from(response.asString())
        val tasks = jsonPath.getList<Map<String, Any>>("tasks")

        println("a5b6c7d8 | 日期过滤测试完成: startDate=$startDate, endDate=$endDate, 找到${tasks.size}个任务")
    }

    @Test
    @Order(99)
    @DisplayName("清理测试数据 - Cleanup test data")
    fun `cleanup test data`() {
        println("e9f0g1h2 | 开始清理测试数据")

        // 注意：根据只读测试原则，我们不实际删除数据
        // 只是记录测试完成的信息
        if (createdTaskUuid != null) {
            println("i3j4k5l6 | 测试完成，创建的任务UUID: $createdTaskUuid")
            println("m7n8o9p0 | 注意：根据只读测试原则，不删除测试数据")
        }

        println("q1r2s3t4 | 手动触发血缘任务API集成测试全部完成")
    }
}