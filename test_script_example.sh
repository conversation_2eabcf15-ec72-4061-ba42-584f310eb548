#!/bin/bash

# 示例Shell脚本：数据处理批次任务 (Example Shell Script: Data Processing Batch Job)
# 用于测试脚本影响分析系统的上传功能

set -e  # 遇到错误立即退出

# 配置变量
HDFS_INPUT_PATH="/data/input/user_data"
HDFS_OUTPUT_PATH="/data/output/processed_user_data"
LOCAL_TEMP_DIR="/tmp/data_processing_$$"
LOG_FILE="/var/log/data_processing.log"

# 日志函数
log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1" | tee -a $LOG_FILE
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1" | tee -a $LOG_FILE
}

# 创建临时目录
log_info "创建临时工作目录: $LOCAL_TEMP_DIR"
mkdir -p $LOCAL_TEMP_DIR

# 从HDFS下载数据
log_info "从HDFS下载输入数据: $HDFS_INPUT_PATH"
hdfs dfs -copyToLocal $HDFS_INPUT_PATH/* $LOCAL_TEMP_DIR/

# 检查数据文件
if [ ! -f "$LOCAL_TEMP_DIR/user_data.csv" ]; then
    log_error "输入数据文件不存在: user_data.csv"
    exit 1
fi

# 数据处理 - 清洗和过滤
log_info "开始数据清洗和过滤"
awk -F',' '
    BEGIN { OFS="," }
    NR == 1 { print; next }  # 保留表头
    $3 ~ /@/ && $4 != "" {   # 过滤有效邮箱和非空注册日期
        # 清洗用户名 - 去除特殊字符
        gsub(/[^a-zA-Z0-9_]/, "", $2)
        if (length($2) > 0) print
    }
' $LOCAL_TEMP_DIR/user_data.csv > $LOCAL_TEMP_DIR/cleaned_user_data.csv

# 生成处理统计
TOTAL_RECORDS=$(wc -l < $LOCAL_TEMP_DIR/user_data.csv)
CLEANED_RECORDS=$(wc -l < $LOCAL_TEMP_DIR/cleaned_user_data.csv)
FILTERED_COUNT=$((TOTAL_RECORDS - CLEANED_RECORDS))

log_info "数据处理完成 - 总记录数: $TOTAL_RECORDS, 清洗后: $CLEANED_RECORDS, 过滤掉: $FILTERED_COUNT"

# 创建处理报告
cat > $LOCAL_TEMP_DIR/processing_report.txt << EOF
数据处理报告 (Data Processing Report)
生成时间: $(date)
输入文件: $HDFS_INPUT_PATH/user_data.csv
输出文件: $HDFS_OUTPUT_PATH/cleaned_user_data.csv

统计信息:
- 原始记录数: $TOTAL_RECORDS
- 清洗后记录数: $CLEANED_RECORDS
- 过滤记录数: $FILTERED_COUNT
- 数据质量率: $(echo "scale=2; $CLEANED_RECORDS * 100 / $TOTAL_RECORDS" | bc)%

处理规则:
1. 过滤无效邮箱地址
2. 移除空的注册日期
3. 清洗用户名中的特殊字符
EOF

# 上传处理结果到HDFS
log_info "上传处理结果到HDFS: $HDFS_OUTPUT_PATH"
hdfs dfs -mkdir -p $HDFS_OUTPUT_PATH
hdfs dfs -copyFromLocal $LOCAL_TEMP_DIR/cleaned_user_data.csv $HDFS_OUTPUT_PATH/
hdfs dfs -copyFromLocal $LOCAL_TEMP_DIR/processing_report.txt $HDFS_OUTPUT_PATH/

# 清理临时文件
log_info "清理临时文件: $LOCAL_TEMP_DIR"
rm -rf $LOCAL_TEMP_DIR

# 发送完成通知（可选）
if command -v mail >/dev/null 2>&1; then
    echo "数据处理批次任务完成。处理了 $CLEANED_RECORDS 条记录。" | \
    mail -s "数据处理任务完成通知" <EMAIL>
fi

log_info "数据处理批次任务执行完成" 