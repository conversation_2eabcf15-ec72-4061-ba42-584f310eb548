# 系统特定血缘收集 REST API 产品需求文档
# System-Specific Lineage Collection REST API - Product Requirements Document

## 📋 概述 (Overview)

### 产品背景 (Background)
当前系统已支持批量处理所有活跃的数据交互平台作业，但缺乏针对特定系统类型的血缘收集触发机制。本功能旨在提供更精细化的血缘收集控制，支持按系统类型触发血缘收集任务。

### 产品目标 (Objectives)
1. **精细化控制**: 支持按系统类型触发血缘收集，提高操作精确性
2. **数据一致性**: 确保当前任务与历史记录的一致性管理
3. **性能优化**: 通过系统级别的处理减少不必要的全量扫描
4. **可扩展性**: 为未来支持更多系统类型奠定基础

### 核心价值 (Core Value)
- 提供系统级别的血缘收集触发能力
- 自动化任务状态管理和历史记录维护
- 支持增量更新和变更检测

---

## 🎯 功能需求 (Functional Requirements)

### 1. 系统特定血缘收集 API

#### 功能描述 (Feature Description)
提供 REST API 支持触发特定系统类型的血缘收集，初期支持数据交互平台 (DATA_EXCHANGE_PLATFORM) 系统。

#### 接口规格 (API Specification)
```http
POST /api/v1/lineage/systems/{systemType}/collect
Content-Type: application/json

{
  "executedBy": "system|username",
  "batchId": "optional-batch-identifier",
  "forceUpdate": false,
  "options": {
    "skipUnchanged": true,
    "maxConcurrency": 5
  }
}
```

#### 路径参数 (Path Parameters)
- `systemType`: 系统类型，当前支持 `DATA_EXCHANGE_PLATFORM`

#### 请求体参数 (Request Body Parameters)
- `executedBy` (string, required): 执行者标识
- `batchId` (string, optional): 批次标识，如未提供则自动生成
- `forceUpdate` (boolean, optional): 是否强制更新，默认 false
- `options` (object, optional): 执行选项
  - `skipUnchanged` (boolean): 是否跳过无变更的任务，默认 true
  - `maxConcurrency` (integer): 最大并发数，默认 5

#### 响应格式 (Response Format)
```json
{
  "success": true,
  "data": {
    "systemType": "DATA_EXCHANGE_PLATFORM",
    "batchId": "batch_20241219_001",
    "executionSummary": {
      "totalJobs": 25,
      "processedTasks": 25,
      "successful": 23,
      "failed": 2,
      "unchanged": 18,
      "updated": 5,
      "processingTimeMs": 15420
    },
    "taskResults": [
      {
        "taskId": 1001,
        "jobKey": "reader_123_writer_456",
        "taskName": "数据同步任务-表A到表B",
        "status": "SUCCESS",
        "processingTimeMs": 1250,
        "hasChanges": true,
        "processingResult": "UPDATED"
      }
    ],
    "failedTasks": [
      {
        "jobKey": "reader_789_writer_012",
        "errorMessage": "SQL解析失败: 语法错误",
        "errorCode": "SQL_PARSE_ERROR"
      }
    ]
  },
  "message": "系统血缘收集完成",
  "timestamp": 1703001234567
}
```

#### 业务流程 (Business Flow)
1. **参数验证**: 验证系统类型和请求参数
2. **获取当前任务**: 查询指定系统的所有活跃任务
3. **血缘收集处理**: 对每个任务执行血缘收集（包含内置的变更检测）
4. **任务记录更新**: 更新或创建 lineage_task 记录
5. **结果汇总**: 生成执行摘要和详细结果

---

## 🔧 技术需求 (Technical Requirements)

### 1. 数据模型扩展 (Data Model Extensions)

#### 系统类型枚举扩展
```kotlin
enum class TaskType {
    DATA_EXCHANGE_PLATFORM,
    BASH_SCRIPT,
    MANUAL_IMPORT,
    EXCEL_IMPORT,
    // 未来可扩展其他系统类型
}
```

#### 新增请求/响应模型
```kotlin
data class SystemLineageCollectionRequest(
    val executedBy: String,
    val batchId: String? = null,
    val forceUpdate: Boolean = false,
    val options: CollectionOptions = CollectionOptions()
)

data class CollectionOptions(
    val skipUnchanged: Boolean = true,
    val maxConcurrency: Int = 5
)

data class SystemLineageCollectionResponse(
    val systemType: TaskType,
    val batchId: String,
    val executionSummary: ExecutionSummary,
    val taskResults: List<TaskProcessResult>,
    val failedTasks: List<FailedTaskInfo>
)
```

### 2. 服务层设计 (Service Layer Design)

#### 新增服务方法
```kotlin
@Service
class LineageTaskService {
    
    /**
     * 处理指定系统类型的血缘收集
     */
    fun processSystemLineageCollection(
        systemType: TaskType,
        request: SystemLineageCollectionRequest
    ): SystemLineageCollectionResponse
    
    /**
     * 获取系统的活跃任务
     */
    private fun getActiveJobsBySystemType(systemType: TaskType): List<DataExchangeJob>
    
    /**
     * 批量处理系统任务
     */
    private fun processBatchSystemTasks(
        jobs: List<DataExchangeJob>,
        request: SystemLineageCollectionRequest
    ): List<TaskProcessResult>
}
```

### 3. 控制器扩展 (Controller Extensions)

#### 新增 API 端点
```kotlin
@RestController
@RequestMapping("/api/v1/lineage")
class LineageSystemController {
    
    @PostMapping("/systems/{systemType}/collect")
    fun collectSystemLineage(
        @PathVariable systemType: TaskType,
        @RequestBody request: SystemLineageCollectionRequest
    ): ResponseEntity<ApiResponse<SystemLineageCollectionResponse>>
}
```

---

## 🛡️ 非功能性需求 (Non-Functional Requirements)

### 1. 性能要求 (Performance Requirements)
- **响应时间**: API 调用响应 < 100ms（异步处理）
- **处理能力**: 支持 100+ 任务的并发处理
- **吞吐量**: 单个任务处理时间 < 500ms
- **资源使用**: 内存使用 < 2GB，CPU 使用率 < 80%

### 2. 可靠性要求 (Reliability Requirements)
- **成功率**: 任务处理成功率 ≥ 95%
- **容错性**: 单个任务失败不影响整体处理
- **数据一致性**: 确保任务状态与血缘数据的一致性
- **事务完整性**: 支持部分失败的回滚机制

### 3. 安全要求 (Security Requirements)
- **身份验证**: 所有 API 调用需要有效的认证
- **权限控制**: 基于角色的访问控制 (RBAC)
- **审计日志**: 记录所有操作的详细日志
- **数据保护**: 敏感信息的加密存储

### 4. 可扩展性要求 (Scalability Requirements)
- **系统扩展**: 支持新增系统类型的插件化扩展
- **水平扩展**: 支持多实例部署和负载均衡
- **配置灵活**: 支持运行时配置调整
- **监控集成**: 提供详细的监控指标

---

## 📊 错误处理 (Error Handling)

### 1. 错误分类 (Error Categories)

#### 客户端错误 (4xx)
- `400 Bad Request`: 请求参数错误
- `404 Not Found`: 系统类型不存在
- `409 Conflict`: 批次处理冲突

#### 服务端错误 (5xx)
- `500 Internal Server Error`: 内部处理错误
- `503 Service Unavailable`: 服务暂时不可用

### 2. 错误响应格式 (Error Response Format)
```json
{
  "success": false,
  "error": {
    "code": "INVALID_SYSTEM_TYPE",
    "message": "不支持的系统类型: UNKNOWN_SYSTEM",
    "details": {
      "supportedTypes": ["DATA_EXCHANGE_PLATFORM"],
      "providedType": "UNKNOWN_SYSTEM"
    }
  },
  "timestamp": 1703001234567
}
```

### 3. 重试机制 (Retry Mechanism)
- **指数退避**: 失败任务采用指数退避重试
- **最大重试**: 最多重试 3 次
- **熔断器**: 连续失败时启用熔断保护

---

## 🔍 监控与日志 (Monitoring & Logging)

### 1. 关键指标 (Key Metrics)
- **处理延迟**: 任务处理时间分布
- **成功率**: 按系统类型的成功率统计
- **吞吐量**: 每分钟处理的任务数量
- **错误率**: 各类错误的发生频率

### 2. 日志规范 (Logging Standards)
- **结构化日志**: 使用 JSON 格式的结构化日志
- **追踪标识**: 每个请求包含唯一的追踪 ID
- **敏感信息**: 避免记录敏感数据
- **日志级别**: 合理使用 DEBUG、INFO、WARN、ERROR 级别

---

## 🚀 实施计划 (Implementation Plan)

### Phase 1: 核心功能开发 (第1-2周)
- [ ] 实现 SystemLineageCollectionRequest/Response 模型
- [ ] 扩展 LineageTaskService 支持系统级处理
- [ ] 实现 LineageSystemController API 端点
- [ ] 添加基础的参数验证和错误处理

### Phase 2: 功能完善 (第3周)
- [ ] 实现并发控制和性能优化
- [ ] 添加详细的监控和日志
- [ ] 完善错误处理和重试机制
- [ ] 编写单元测试和集成测试

### Phase 3: 测试与优化 (第4周)
- [ ] 性能测试和调优
- [ ] 安全测试和漏洞扫描
- [ ] 文档编写和 API 规范
- [ ] 生产环境部署准备

---

## 📈 成功指标 (Success Metrics)

### 业务指标 (Business Metrics)
- **功能采用率**: ≥ 80% 的用户使用新功能
- **处理效率**: 相比全量处理提升 ≥ 50% 效率
- **错误减少**: 血缘收集错误率降低 ≥ 30%

### 技术指标 (Technical Metrics)
- **API 可用性**: ≥ 99.9% 的服务可用性
- **响应时间**: P95 响应时间 < 200ms
- **数据准确性**: 血缘数据准确率 ≥ 99%

---

## 🔮 未来扩展 (Future Extensions)

### 1. 多系统支持 (Multi-System Support)
- 支持 BASH_SCRIPT 系统类型
- 支持 MANUAL_IMPORT 系统类型
- 插件化的系统类型扩展机制

### 2. 高级功能 (Advanced Features)
- 定时触发的系统血缘收集
- 基于事件的自动触发机制
- 血缘收集的优先级队列

### 3. 集成能力 (Integration Capabilities)
- 与外部调度系统集成
- 支持 Webhook 通知机制
- 提供 GraphQL API 支持

---

## 🏗️ 详细技术设计 (Detailed Technical Design)

### 1. 执行顺序决策 (Execution Order Decision)

#### 推荐执行顺序
**步骤1 → 步骤3 → 步骤4 → 步骤2**

1. **查询当前任务** (Query Current Tasks)
2. **处理血缘收集** (Process Lineage Collection) - 包含内置变更检测
3. **更新任务记录** (Update Task Records)
4. **比较历史数据** (Compare with Historical Data) - 用于统计和报告

#### 决策理由 (Decision Rationale)

**数据一致性优势**:
- 现有的 `processJobLineageWithChangeDetection` 已内置哈希比较机制
- 避免重复的变更检测逻辑，减少数据不一致风险
- 统一的事务管理，确保血缘数据和任务状态的原子性更新

**性能优化考虑**:
- 基于哈希的变更检测比数据库查询比较更高效
- 减少数据库往返次数，提升整体处理性能
- 内存中的哈希比较避免了复杂的 SQL 查询

**错误处理简化**:
- 统一的错误处理流程，便于维护和调试
- 失败时的状态回滚更加可控
- 符合现有架构的错误处理模式

### 2. 数据库事务设计 (Database Transaction Design)

#### 事务边界 (Transaction Boundaries)
```kotlin
@Transactional
fun processSystemLineageCollection(
    systemType: TaskType,
    request: SystemLineageCollectionRequest
): SystemLineageCollectionResponse {
    // 外层事务：管理整体批次状态

    jobs.forEach { job ->
        // 内层事务：每个任务独立事务
        processJobWithTransaction(job, request)
    }
}

@Transactional(propagation = Propagation.REQUIRES_NEW)
private fun processJobWithTransaction(
    job: DataExchangeJob,
    request: SystemLineageCollectionRequest
): TaskProcessResult {
    // 独立事务确保单个任务失败不影响其他任务
}
```

#### 并发控制 (Concurrency Control)
- 使用 `@Async` 注解实现任务并发处理
- 通过 `Semaphore` 控制最大并发数
- 数据库层面使用乐观锁避免冲突

### 3. 缓存策略 (Caching Strategy)

#### 多层缓存设计
```kotlin
@Cacheable("activeJobs", key = "#systemType")
fun getActiveJobsBySystemType(systemType: TaskType): List<DataExchangeJob>

@CacheEvict("activeJobs", key = "#systemType")
fun invalidateJobsCache(systemType: TaskType)
```

#### 缓存失效策略
- **时间失效**: 缓存有效期 5 分钟
- **手动失效**: 任务更新后主动清除缓存
- **版本控制**: 基于数据版本的缓存失效

### 4. 安全设计 (Security Design)

#### 认证机制 (Authentication)
```kotlin
@PreAuthorize("hasRole('LINEAGE_ADMIN') or hasRole('SYSTEM_OPERATOR')")
@PostMapping("/systems/{systemType}/collect")
fun collectSystemLineage(...)
```

#### 审计日志 (Audit Logging)
```kotlin
@AuditLog(
    operation = "SYSTEM_LINEAGE_COLLECTION",
    resource = "#{systemType}",
    details = "#{request.batchId}"
)
fun processSystemLineageCollection(...)
```

### 5. 监控集成 (Monitoring Integration)

#### 指标收集 (Metrics Collection)
```kotlin
@Timed(name = "lineage.collection.duration", description = "Lineage collection duration")
@Counted(name = "lineage.collection.total", description = "Total lineage collections")
fun processSystemLineageCollection(...)
```

#### 健康检查 (Health Checks)
```kotlin
@Component
class LineageCollectionHealthIndicator : HealthIndicator {
    override fun health(): Health {
        // 检查数据库连接、外部服务状态等
    }
}
```

---

## 📋 API 规范补充 (API Specification Supplement)

### 1. OpenAPI 规范 (OpenAPI Specification)

```yaml
paths:
  /api/v1/lineage/systems/{systemType}/collect:
    post:
      tags:
        - Lineage Collection
      summary: 触发系统特定的血缘收集
      description: 对指定系统类型的所有活跃任务执行血缘收集和更新
      parameters:
        - name: systemType
          in: path
          required: true
          schema:
            type: string
            enum: [DATA_EXCHANGE_PLATFORM]
          description: 系统类型标识
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SystemLineageCollectionRequest'
      responses:
        '200':
          description: 血缘收集成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '400':
          description: 请求参数错误
        '404':
          description: 系统类型不存在
        '500':
          description: 内部服务错误
```

### 2. 错误码定义 (Error Code Definitions)

| 错误码 | HTTP状态 | 描述 | 解决方案 |
|--------|----------|------|----------|
| `INVALID_SYSTEM_TYPE` | 400 | 不支持的系统类型 | 检查系统类型参数 |
| `BATCH_CONFLICT` | 409 | 批次处理冲突 | 等待当前批次完成 |
| `DATABASE_CONNECTION_ERROR` | 500 | 数据库连接失败 | 检查数据库服务状态 |
| `LINEAGE_PROCESSING_ERROR` | 500 | 血缘处理失败 | 查看详细错误日志 |
| `CONCURRENT_LIMIT_EXCEEDED` | 429 | 并发限制超出 | 降低并发请求数量 |

### 3. 限流策略 (Rate Limiting)

```kotlin
@RateLimiter(name = "lineage-collection", fallbackMethod = "fallbackCollectLineage")
fun collectSystemLineage(...)

fun fallbackCollectLineage(...): ResponseEntity<ApiResponse<String>> {
    return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS)
        .body(ApiResponse.error("请求过于频繁，请稍后重试"))
}
```

---

## 🧪 测试策略 (Testing Strategy)

### 1. 单元测试 (Unit Tests)
- **服务层测试**: 测试业务逻辑的正确性
- **控制器测试**: 测试 API 端点的行为
- **数据访问测试**: 测试数据库操作的准确性

### 2. 集成测试 (Integration Tests)
- **端到端测试**: 完整的 API 调用流程测试
- **数据库集成**: 真实数据库环境的测试
- **外部服务集成**: 与数据交互平台的集成测试

### 3. 性能测试 (Performance Tests)
- **负载测试**: 模拟高并发场景
- **压力测试**: 测试系统极限处理能力
- **稳定性测试**: 长时间运行的稳定性验证

### 4. 测试数据管理 (Test Data Management)
```kotlin
@TestConfiguration
class TestDataConfig {

    @Bean
    @Primary
    fun mockDataExchangeJobRepository(): DataExchangeJobRepository {
        return Mockito.mock(DataExchangeJobRepository::class.java)
    }
}
```

---

## 📚 文档和培训 (Documentation & Training)

### 1. 技术文档 (Technical Documentation)
- **API 文档**: 详细的接口说明和示例
- **架构文档**: 系统架构和设计决策说明
- **运维文档**: 部署、配置和故障排除指南

### 2. 用户文档 (User Documentation)
- **用户手册**: 功能使用说明和最佳实践
- **FAQ**: 常见问题和解决方案
- **变更日志**: 版本更新和功能变更记录

### 3. 培训计划 (Training Plan)
- **开发团队培训**: 新功能的技术实现培训
- **运维团队培训**: 系统监控和故障处理培训
- **用户培训**: 功能使用和操作培训
