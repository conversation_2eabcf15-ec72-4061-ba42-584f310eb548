package com.datayes.config

import io.swagger.v3.oas.models.OpenAPI
import io.swagger.v3.oas.models.info.Info
import io.swagger.v3.oas.models.servers.Server
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Profile

@Configuration
class OpenApiConfig {

    @Bean
    @Profile("qa")
    fun openApiForQa(): OpenAPI {
        return OpenAPI()
            .info(
                Info()
                    .title("DGP Lineage Collector API")
                    .description("Data lineage collection and analysis service")
                    .version("1.0.0")
            )
            .servers(
                listOf(
                    Server()
                        .url("https://dgpsit.minshenglife.com/dgp-lineage-collector")
                        .description("Production Server")
                )
            )
    }

    @Bean
    @Profile("!qa")
    fun openApiForLocal(): OpenAPI {
        return OpenAPI()
            .info(
                Info()
                    .title("DGP Lineage Collector API")
                    .description("Data lineage collection and analysis service")
                    .version("1.0.0")
            )
    }
}