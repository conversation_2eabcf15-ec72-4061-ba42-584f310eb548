package com.datayes.scheduler

import com.datayes.lineage.LineageService
import com.datayes.task.LineageTaskService
import com.datayes.task.SystemLineageCollectionRequest
import com.datayes.task.TaskType
import org.slf4j.LoggerFactory
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.core.env.Environment
import org.springframework.scheduling.TaskScheduler
import org.springframework.scheduling.support.CronExpression
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ScheduledFuture

/**
 * Cron作业调度服务 (Cron Job Scheduler Service)
 *
 * 基于lineage_systems表中的cron_expression自动创建和管理定时任务
 */
@Service
class CronJobSchedulerService(
    private val taskScheduler: TaskScheduler,
    private val lineageService: LineageService,
    private val lineageTaskService: LineageTaskService,
    private val environment: Environment
) {

    private val logger = LoggerFactory.getLogger(CronJobSchedulerService::class.java)

    // 存储已调度的任务，key为systemId
    private val scheduledTasks = ConcurrentHashMap<Long, ScheduledFuture<*>>()

    /**
     * 应用启动后初始化所有定时任务 (仅在qa环境)
     */
    @EventListener(ApplicationReadyEvent::class)
    fun initializeScheduledTasks() {
        val activeProfiles = environment.activeProfiles.joinToString(", ")
        val defaultProfiles = environment.defaultProfiles.joinToString(", ")

        logger.info("profile-check | 活跃profiles: [$activeProfiles], 默认profiles: [$defaultProfiles]")

        // 只在 qa 环境或没有指定 profile 时执行
        // val shouldExecute = activeProfiles.contains("qa")
        val shouldExecute = false // todo wujie disable this for now

        if (!shouldExecute) {
            logger.info("profile-check | 当前环境不是qa，跳过定时任务初始化")
            return
        }

        logger.info("profile-check | 开始初始化定时任务...")

        try {
            logger.info("09567406 | 开始初始化定时任务...")

            // 获取所有活跃且有cron表达式的系统
            val systems = lineageService.findSystemsWithCronExpression()

            logger.info("c18b71a7 | 找到${systems.size}个需要调度的系统")

            systems.forEach { system ->
                try {
                    scheduleSystemTask(system)
                    logger.info("9387ad08 | 系统定时任务调度成功: systemId=${system.id}, systemName=${system.systemName}, cronExpression=${system.cronExpression}")
                } catch (e: Exception) {
                    logger.error(
                        "d95110f9 | 系统定时任务调度失败: systemId=${system.id}, systemName=${system.systemName}",
                        e
                    )
                }
            }
            logger.info("ac0cf974 | 定时任务初始化完成，成功调度${scheduledTasks.size}个任务")
        } catch (e: Exception) {
            logger.error("f6203f88 | 定时任务初始化失败", e)
        }
    }

    /**
     * 为指定系统创建定时任务
     */
    fun scheduleSystemTask(system: SystemScheduleInfo) {
        val logPrefix = "e5f6g7h8"

        if (system.cronExpression.isNullOrBlank()) {
            logger.warn("$logPrefix | 系统没有有效的cron表达式: systemId=${system.id}")
            return
        }

        try {
            // 验证cron表达式
            val cronExpression = CronExpression.parse(system.cronExpression)

            // 取消已存在的任务
            cancelSystemTask(system.id)

            // 创建新的定时任务
            val scheduledTask = taskScheduler.schedule(
                createSystemLineageCollectionTask(system)
            ) { triggerContext ->
                val lastCompletion = triggerContext.lastCompletionTime()
                val baseTime = if (lastCompletion != null) {
                    LocalDateTime.ofInstant(lastCompletion.toInstant(), java.time.ZoneId.systemDefault())
                } else {
                    LocalDateTime.now()
                }
                cronExpression.next(baseTime)?.atZone(java.time.ZoneId.systemDefault())?.toInstant()
            }

            // 保存任务引用
            scheduledTask?.let { scheduledTasks[system.id] = it }

            // 计算下次执行时间
            val nextExecutionTime = cronExpression.next(LocalDateTime.now())
            logger.info(
                "$logPrefix | 系统定时任务已调度: systemId=${system.id}, systemName=${system.systemName}, " +
                        "cronExpression=${system.cronExpression}, nextExecution=$nextExecutionTime"
            )

        } catch (e: Exception) {
            logger.error(
                "$logPrefix | 调度系统定时任务时出错: systemId=${system.id}, cronExpression=${system.cronExpression}",
                e
            )
            throw e
        }
    }

    /**
     * 取消指定系统的定时任务
     */
    fun cancelSystemTask(systemId: Long) {
        val logPrefix = "i9j0k1l2"

        scheduledTasks[systemId]?.let { task ->
            try {
                task.cancel(false)
                scheduledTasks.remove(systemId)
                logger.info("$logPrefix | 系统定时任务已取消: systemId=$systemId")
            } catch (e: Exception) {
                logger.error("$logPrefix | 取消系统定时任务时出错: systemId=$systemId", e)
            }
        }
    }

    /**
     * 重新调度指定系统的定时任务
     */
    fun rescheduleSystemTask(systemId: Long) {
        val logPrefix = "m3n4o5p6"

        try {
            logger.info("$logPrefix | 开始重新调度系统任务: systemId=$systemId")

            // 获取系统信息
            val system = lineageService.findSystemScheduleInfo(systemId)
            if (system == null) {
                logger.warn("$logPrefix | 系统不存在: systemId=$systemId")
                return
            }

            if (system.status != "ACTIVE") {
                logger.info("$logPrefix | 系统状态非活跃，取消定时任务: systemId=$systemId, status=${system.status}")
                cancelSystemTask(systemId)
                return
            }

            if (system.cronExpression.isNullOrBlank()) {
                logger.info("$logPrefix | 系统没有cron表达式，取消定时任务: systemId=$systemId")
                cancelSystemTask(systemId)
                return
            }

            // 重新调度任务
            scheduleSystemTask(system)

        } catch (e: Exception) {
            logger.error("$logPrefix | 重新调度系统任务时出错: systemId=$systemId", e)
        }
    }

    /**
     * 获取所有已调度的任务信息
     */
    fun getScheduledTasksInfo(): List<ScheduledTaskInfo> {
        return scheduledTasks.map { (systemId, task) ->
            val system = lineageService.findSystemScheduleInfo(systemId)
            ScheduledTaskInfo(
                systemId = systemId,
                systemName = system?.systemName ?: "Unknown",
                systemCode = system?.systemCode ?: "Unknown",
                cronExpression = system?.cronExpression ?: "",
                status = if (task.isCancelled) "CANCELLED" else "SCHEDULED",
                nextExecutionTime = system?.cronExpression?.let {
                    try {
                        CronExpression.parse(it).next(LocalDateTime.now())
                    } catch (e: Exception) {
                        null
                    }
                }
            )
        }
    }

    /**
     * 创建系统血缘收集任务
     */
    private fun createSystemLineageCollectionTask(system: SystemScheduleInfo): Runnable {
        return Runnable {
            val logPrefix = "q7r8s9t0"

            try {
                logger.info("$logPrefix | 开始执行定时系统血缘收集: systemId=${system.id}, systemName=${system.systemName}")

                // 根据系统代码确定任务类型
                val taskType = when (system.systemCode) {
                    "DATA_EXCHANGE_PLATFORM" -> TaskType.DATA_EXCHANGE_PLATFORM
                    else -> {
                        logger.warn("$logPrefix | 不支持的系统类型: systemCode=${system.systemCode}")
                        return@Runnable
                    }
                }

                // 创建收集请求
                val request = SystemLineageCollectionRequest(
                    executedBy = "CRON_SCHEDULER"
                )

                // 执行血缘收集
                val result = lineageTaskService.processSystemLineageCollection(taskType, request)

                logger.info(
                    "$logPrefix | 定时系统血缘收集完成: systemId=${system.id}, " +
                            "batchId=${result.batchId}, 成功=${result.executionSummary.successful}, " +
                            "失败=${result.executionSummary.failed}"
                )

            } catch (e: Exception) {
                logger.error(
                    "$logPrefix | 定时系统血缘收集执行失败: systemId=${system.id}, systemName=${system.systemName}",
                    e
                )
            }
        }
    }
}

/**
 * 系统调度信息 (System Schedule Info)
 */
data class SystemScheduleInfo(
    val id: Long,
    val systemName: String,
    val systemCode: String,
    val status: String,
    val cronExpression: String?,
    val description: String?
)

/**
 * 已调度任务信息 (Scheduled Task Info)
 */
data class ScheduledTaskInfo(
    val systemId: Long,
    val systemName: String,
    val systemCode: String,
    val cronExpression: String,
    val status: String,
    val nextExecutionTime: LocalDateTime?
)