# 数据交互作业血缘增量更新 PRD

## 1. 概述 (Overview)

### 1.1 背景 (Background)
当前系统每次处理数据交互作业时都会创建新的血缘记录，导致重复数据 (duplicate data) 和存储浪费。需要实现智能检测机制，只有当血缘信息真正发生变化时才更新数据库。

### 1.2 目标 (Objectives)
- 避免重复的血缘记录创建
- 减少数据库存储空间占用
- 提高处理性能 (processing performance)
- 保持数据的准确性和一致性 (accuracy and consistency)

## 2. 功能需求 (Functional Requirements)

### 2.1 核心功能 (Core Features)

#### 2.1.1 血缘变更检测 (Lineage Change Detection)
- **功能描述**: 比较新解析的血缘信息与数据库中现有记录
- **检测范围**:
  - 表级血缘关系 (table-level lineage relationships)
  - 列级血缘关系 (column-level lineage relationships)  
  - 数据转换逻辑 (data transformation logic)

#### 2.1.2 智能更新策略 (Intelligent Update Strategy)
- **无变更 (No Changes)**: 仅更新最后处理时间 (last processed time)
- **有变更 (Has Changes)**: 
  - 标记旧记录为非活跃 (mark old records as inactive)
  - 插入新的血缘记录 (insert new lineage records)
  - 清理不再使用的表/列元数据 (cleanup unused table/column metadata)

#### 2.1.3 处理状态跟踪 (Processing Status Tracking)
- 记录每次处理的结果和统计信息
- 跟踪变更类型和影响范围 (change types and impact scope)

## 3. 数据模型调整 (Data Model Adjustments)

### 3.1 现有表结构增强 (Existing Table Enhancement)

```sql
-- 血缘关系表增加字段
ALTER TABLE lineage_relationships ADD COLUMN (
    job_key VARCHAR(255) NOT NULL COMMENT '作业唯一标识',
    last_processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后处理时间',
    content_hash VARCHAR(64) COMMENT '内容哈希值，用于变更检测',
    INDEX idx_job_key (job_key),
    INDEX idx_content_hash (content_hash)
);

-- 表元数据增加引用计数
ALTER TABLE lineage_tables ADD COLUMN (
    reference_count INT DEFAULT 0 COMMENT '被引用次数',
    last_referenced_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后引用时间'
);

-- 列元数据增加引用计数  
ALTER TABLE lineage_columns ADD COLUMN (
    reference_count INT DEFAULT 0 COMMENT '被引用次数',
    last_referenced_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后引用时间'
);
```

### 3.2 处理历史记录表 (Processing History Table)

```sql
-- 作业处理历史表
CREATE TABLE lineage_job_processing_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    job_key VARCHAR(255) NOT NULL COMMENT '作业唯一标识',
    reader_job_id VARCHAR(255) NOT NULL,
    write_job_id VARCHAR(255) NOT NULL,
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processing_result ENUM('NO_CHANGE', 'UPDATED', 'FAILED') NOT NULL,
    changes_detected BOOLEAN DEFAULT FALSE,
    processing_duration_ms BIGINT,
    lineage_hash VARCHAR(64) COMMENT '血缘内容哈希',
    error_message TEXT,
    
    INDEX idx_job_key (job_key),
    INDEX idx_processed_at (processed_at),
    INDEX idx_lineage_hash (lineage_hash)
) COMMENT='作业处理历史记录';
```

## 4. 技术实现方案 (Technical Implementation)

### 4.1 血缘哈希计算 (Lineage Hash Calculation)

```kotlin
/**
 * 血缘哈希计算器 (Lineage Hash Calculator)
 * 用于生成血缘信息的唯一标识，支持变更检测
 */
object LineageHashCalculator {
    
    /**
     * 计算数据血缘的哈希值
     * @param dataLineage 数据血缘对象
     * @return SHA-256 哈希值
     */
    fun calculateHash(dataLineage: DataLineage): String {
        val content = buildString {
            // 表级血缘信息
            append("tables:")
            dataLineage.tableLineage.sourceTables.sortedBy { "${it.database.host}:${it.schema}:${it.tableName}" }
                .forEach { table ->
                    append("${table.database.host}:${table.database.port}:${table.database.databaseName}:")
                    append("${table.schema}:${table.tableName};")
                }
            append("target:${dataLineage.tableLineage.targetTable.database.host}:")
            append("${dataLineage.tableLineage.targetTable.database.port}:")
            append("${dataLineage.tableLineage.targetTable.database.databaseName}:")
            append("${dataLineage.tableLineage.targetTable.schema}:")
            append("${dataLineage.tableLineage.targetTable.tableName};")
            
            // 列级血缘信息
            append("columns:")
            dataLineage.columnLineages.sortedBy { "${it.sourceColumn.columnName}:${it.targetColumn.columnName}" }
                .forEach { columnLineage ->
                    append("${columnLineage.sourceColumn.columnName}->")
                    append("${columnLineage.targetColumn.columnName}:")
                    append("${columnLineage.transformation?.transformationType?.name ?: "DIRECT"};")
                }
        }
        
        return MessageDigest.getInstance("SHA-256")
            .digest(content.toByteArray())
            .joinToString("") { "%02x".format(it) }
    }
}
```

### 4.2 变更检测服务 (Change Detection Service)

```kotlin
/**
 * 血缘变更检测服务 (Lineage Change Detection Service)
 */
@Service
class LineageChangeDetectionService(
    private val lineageRepository: LineageRepository,
    private val processingHistoryRepository: JobProcessingHistoryRepository
) {
    
    private val logger = LoggerFactory.getLogger(LineageChangeDetectionService::class.java)
    
    /**
     * 检测血缘是否发生变更
     * @param jobKey 作业标识
     * @param newLineage 新的血缘信息
     * @return 变更检测结果
     */
    fun detectChanges(jobKey: String, newLineage: DataLineage): ChangeDetectionResult {
        val newHash = LineageHashCalculator.calculateHash(newLineage)
        
        // 查询最近一次处理记录
        val lastProcessing = processingHistoryRepository.findLatestByJobKey(jobKey)
        
        return if (lastProcessing?.lineageHash == newHash) {
            // 无变更
            ChangeDetectionResult(
                hasChanges = false,
                currentHash = newHash,
                previousHash = lastProcessing.lineageHash
            )
        } else {
            // 有变更
            ChangeDetectionResult(
                hasChanges = true,
                currentHash = newHash,
                previousHash = lastProcessing?.lineageHash
            )
        }
    }
}

/**
 * 变更检测结果 (Change Detection Result)
 */
data class ChangeDetectionResult(
    val hasChanges: Boolean,
    val currentHash: String,
    val previousHash: String?
)
```

### 4.3 增强的血缘处理服务 (Enhanced Lineage Processing Service)

```kotlin
/**
 * 增强的数据交互作业服务 (Enhanced Data Exchange Job Service)
 */
@Service
class EnhancedDataExchangeJobService(
    private val dataExchangeJobRepository: DataExchangeJobRepository,
    private val lineageRepository: LineageRepository,
    private val changeDetectionService: LineageChangeDetectionService,
    private val processingHistoryRepository: JobProcessingHistoryRepository
) {
    
    private val logger = LoggerFactory.getLogger(EnhancedDataExchangeJobService::class.java)
    
    /**
     * 智能处理作业血缘 (只在有变更时更新数据库)
     */
    fun processJobLineageWithChangeDetection(job: DataExchangeJob): LineageProcessResult {
        val startTime = System.currentTimeMillis()
        val jobKey = "${job.readerJobId}_${job.writeJobId}"
        
        return try {
            // 1. 转换为血缘信息
            val lineageResult = LineageConverter.convertToLineage(job)
            
            if (!lineageResult.success) {
                return createFailureResult(job, lineageResult, startTime, "血缘转换失败")
            }
            
            val dataLineage = lineageResult.lineage!!
            
            // 2. 检测变更
            val changeDetection = changeDetectionService.detectChanges(jobKey, dataLineage)
            
            val processingResult = if (changeDetection.hasChanges) {
                // 3a. 有变更：更新数据库
                logger.info("检测到血缘变更，更新数据库: $jobKey")
                updateLineageInDatabase(jobKey, dataLineage, job)
                ProcessingResult.UPDATED
            } else {
                // 3b. 无变更：仅更新处理时间
                logger.debug("未检测到血缘变更，跳过数据库更新: $jobKey")
                ProcessingResult.NO_CHANGE
            }
            
            // 4. 记录处理历史
            recordProcessingHistory(
                jobKey = jobKey,
                job = job,
                result = processingResult,
                lineageHash = changeDetection.currentHash,
                processingTime = System.currentTimeMillis() - startTime
            )
            
            LineageProcessResult(
                job = job,
                lineageResult = lineageResult,
                processingTimeMs = System.currentTimeMillis() - startTime,
                hasChanges = changeDetection.hasChanges,
                processingResult = processingResult
            )
            
        } catch (e: Exception) {
            logger.error("处理作业血缘时发生异常: $jobKey", e)
            createFailureResult(job, null, startTime, "处理异常: ${e.message}")
        }
    }
    
    /**
     * 更新数据库中的血缘信息
     */
    @Transactional
    private fun updateLineageInDatabase(jobKey: String, dataLineage: DataLineage, job: DataExchangeJob): List<Long> {
        // 1. 标记旧的血缘关系为非活跃
        lineageRepository.deactivateLineageByJobKey(jobKey)
        
        // 2. 保存新的血缘信息
        val relationshipIds = lineageRepository.saveDataLineage(dataLineage, null)
        
        // 3. 更新作业标识和内容哈希
        val contentHash = LineageHashCalculator.calculateHash(dataLineage)
        lineageRepository.updateJobKeyAndHash(relationshipIds, jobKey, contentHash)
        
        // 4. 更新引用计数
        updateReferenceCounts(dataLineage)
        
        return relationshipIds
    }
    
    /**
     * 更新表和列的引用计数
     */
    private fun updateReferenceCounts(dataLineage: DataLineage) {
        // 增加新引用的计数
        dataLineage.tableLineage.sourceTables.forEach { table ->
            lineageRepository.incrementTableReferenceCount(table)
        }
        lineageRepository.incrementTableReferenceCount(dataLineage.tableLineage.targetTable)
        
        dataLineage.columnLineages.forEach { columnLineage ->
            lineageRepository.incrementColumnReferenceCount(columnLineage.sourceColumn)
            lineageRepository.incrementColumnReferenceCount(columnLineage.targetColumn)
        }
    }
    
    /**
     * 记录处理历史
     */
    private fun recordProcessingHistory(
        jobKey: String,
        job: DataExchangeJob,
        result: ProcessingResult,
        lineageHash: String,
        processingTime: Long
    ) {
        val history = JobProcessingHistory(
            jobKey = jobKey,
            readerJobId = job.readerJobId,
            writeJobId = job.writeJobId,
            processingResult = result,
            changesDetected = result == ProcessingResult.UPDATED,
            processingDurationMs = processingTime,
            lineageHash = lineageHash
        )
        
        processingHistoryRepository.save(history)
    }
    
    private fun createFailureResult(
        job: DataExchangeJob, 
        lineageResult: LineageResult?, 
        startTime: Long, 
        errorMessage: String
    ): LineageProcessResult {
        val failureResult = lineageResult ?: LineageResult(
            lineage = null,
            warnings = emptyList(),
            errors = listOf(errorMessage),
            success = false
        )
        
        return LineageProcessResult(
            job = job,
            lineageResult = failureResult,
            processingTimeMs = System.currentTimeMillis() - startTime,
            hasChanges = false,
            processingResult = ProcessingResult.FAILED
        )
    }
}

/**
 * 处理结果枚举 (Processing Result Enum)
 */
enum class ProcessingResult {
    NO_CHANGE,  // 无变更
    UPDATED,    // 已更新
    FAILED      // 处理失败
}

/**
 * 增强的血缘处理结果 (Enhanced Lineage Process Result)
 */
data class LineageProcessResult(
    val job: DataExchangeJob,
    val lineageResult: LineageResult,
    val processingTimeMs: Long,
    val hasChanges: Boolean = false,
    val processingResult: ProcessingResult = ProcessingResult.NO_CHANGE
)
```

### 4.4 Repository 层增强 (Repository Layer Enhancement)

```kotlin
/**
 * LineageRepository 增强方法
 */
@Repository
class LineageRepository(private val jdbcTemplate: JdbcTemplate) {
    
    /**
     * 根据作业标识停用血缘关系
     */
    @Transactional
    fun deactivateLineageByJobKey(jobKey: String): Int {
        val sql = """
            UPDATE lineage_relationships 
            SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP
            WHERE job_key = ? AND is_active = TRUE
        """.trimIndent()
        
        return jdbcTemplate.update(sql, jobKey)
    }
    
    /**
     * 更新血缘关系的作业标识和内容哈希
     */
    fun updateJobKeyAndHash(relationshipIds: List<Long>, jobKey: String, contentHash: String) {
        val sql = """
            UPDATE lineage_relationships 
            SET job_key = ?, content_hash = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """.trimIndent()
        
        relationshipIds.forEach { id ->
            jdbcTemplate.update(sql, jobKey, contentHash, id)
        }
    }
    
    /**
     * 增加表引用计数
     */
    fun incrementTableReferenceCount(tableInfo: com.datayes.parser.TableInfo) {
        val sql = """
            UPDATE lineage_tables 
            SET reference_count = reference_count + 1, 
                last_referenced_at = CURRENT_TIMESTAMP
            WHERE datasource_id = (
                SELECT id FROM lineage_datasources 
                WHERE host = ? AND port = ? AND database_name = ?
            ) AND table_name = ? AND (schema_name = ? OR (schema_name IS NULL AND ? IS NULL))
        """.trimIndent()
        
        jdbcTemplate.update(
            sql,
            tableInfo.database.host,
            tableInfo.database.port,
            tableInfo.database.databaseName,
            tableInfo.tableName,
            tableInfo.schema,
            tableInfo.schema
        )
    }
    
    /**
     * 增加列引用计数
     */
    fun incrementColumnReferenceCount(columnInfo: com.datayes.parser.ColumnInfo) {
        val sql = """
            UPDATE lineage_columns 
            SET reference_count = reference_count + 1,
                last_referenced_at = CURRENT_TIMESTAMP
            WHERE table_id IN (
                SELECT t.id FROM lineage_tables t
                JOIN lineage_datasources ds ON t.datasource_id = ds.id
                WHERE ds.host = ? AND ds.port = ? AND ds.database_name = ?
                  AND t.table_name = ? AND (t.schema_name = ? OR (t.schema_name IS NULL AND ? IS NULL))
            ) AND column_name = ?
        """.trimIndent()
        
        jdbcTemplate.update(
            sql,
            columnInfo.table.database.host,
            columnInfo.table.database.port,
            columnInfo.table.database.databaseName,
            columnInfo.table.tableName,
            columnInfo.table.schema,
            columnInfo.table.schema,
            columnInfo.columnName
        )
    }
}

/**
 * 作业处理历史仓库 (Job Processing History Repository)
 */
@Repository
class JobProcessingHistoryRepository(private val jdbcTemplate: JdbcTemplate) {
    
    /**
     * 查找作业的最新处理记录
     */
    fun findLatestByJobKey(jobKey: String): JobProcessingHistory? {
        val sql = """
            SELECT * FROM lineage_job_processing_history 
            WHERE job_key = ? 
            ORDER BY processed_at DESC 
            LIMIT 1
        """.trimIndent()
        
        return jdbcTemplate.query(sql, jobProcessingHistoryRowMapper, jobKey)
            .firstOrNull()
    }
    
    /**
     * 保存处理历史记录
     */
    fun save(history: JobProcessingHistory): Long {
        val keyHolder = GeneratedKeyHolder()
        jdbcTemplate.update({ connection ->
            val ps = connection.prepareStatement(
                """
                INSERT INTO lineage_job_processing_history 
                (job_key, reader_job_id, write_job_id, processing_result, changes_detected, 
                 processing_duration_ms, lineage_hash, error_message)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """.trimIndent(),
                Statement.RETURN_GENERATED_KEYS
            )
            ps.setString(1, history.jobKey)
            ps.setString(2, history.readerJobId)
            ps.setString(3, history.writeJobId)
            ps.setString(4, history.processingResult.name)
            ps.setBoolean(5, history.changesDetected)
            ps.setLong(6, history.processingDurationMs)
            ps.setString(7, history.lineageHash)
            ps.setString(8, history.errorMessage)
            ps
        }, keyHolder)
        
        return keyHolder.key!!.toLong()
    }
    
    private val jobProcessingHistoryRowMapper = RowMapper<JobProcessingHistory> { rs, _ ->
        JobProcessingHistory(
            id = rs.getLong("id"),
            jobKey = rs.getString("job_key"),
            readerJobId = rs.getString("reader_job_id"),
            writeJobId = rs.getString("write_job_id"),
            processedAt = rs.getTimestamp("processed_at").toLocalDateTime(),
            processingResult = ProcessingResult.valueOf(rs.getString("processing_result")),
            changesDetected = rs.getBoolean("changes_detected"),
            processingDurationMs = rs.getLong("processing_duration_ms"),
            lineageHash = rs.getString("lineage_hash"),
            errorMessage = rs.getString("error_message")
        )
    }
}

/**
 * 作业处理历史数据类 (Job Processing History Data Class)
 */
data class JobProcessingHistory(
    val id: Long = 0,
    val jobKey: String,
    val readerJobId: String,
    val writeJobId: String,
    val processedAt: LocalDateTime = LocalDateTime.now(),
    val processingResult: ProcessingResult,
    val changesDetected: Boolean,
    val processingDurationMs: Long,
    val lineageHash: String,
    val errorMessage: String? = null
)
```

## 5. 使用示例 (Usage Example)

```kotlin
/**
 * 使用示例：处理所有活跃作业
 */
@Service
class LineageCollectorService(
    private val enhancedDataExchangeJobService: EnhancedDataExchangeJobService
) {
    
    fun collectAllActiveJobsLineage(): CollectionSummary {
        val results = enhancedDataExchangeJobService.processAllActiveJobsLineage()
        
        return CollectionSummary(
            totalJobs = results.size,
            updatedJobs = results.count { it.hasChanges },
            unchangedJobs = results.count { !it.hasChanges && it.processingResult == ProcessingResult.NO_CHANGE },
            failedJobs = results.count { it.processingResult == ProcessingResult.FAILED },
            totalProcessingTime = results.sumOf { it.processingTimeMs }
        )
    }
}

data class CollectionSummary(
    val totalJobs: Int,
    val updatedJobs: Int,
    val unchangedJobs: Int,
    val failedJobs: Int,
    val totalProcessingTime: Long
)
```

## 6. 实施计划 (Implementation Plan)

### 6.1 阶段一：数据库结构调整
- 执行DDL语句，增加必要的字段和索引
- 创建处理历史记录表
- 为现有数据设置默认值

### 6.2 阶段二：核心功能开发
- 实现血缘哈希计算器 (LineageHashCalculator)
- 开发变更检测服务 (LineageChangeDetectionService)
- 创建处理历史仓库 (JobProcessingHistoryRepository)

### 6.3 阶段三：服务层增强
- 增强数据交互作业服务 (EnhancedDataExchangeJobService)
- 扩展血缘仓库方法 (LineageRepository)
- 添加引用计数管理功能

### 6.4 阶段四：测试和验证
- 单元测试覆盖所有新功能
- 集成测试验证端到端流程
- 性能测试确保优化效果

## 7. 预期效果 (Expected Benefits)

### 7.1 性能提升
- 减少不必要的数据库写入操作
- 降低存储空间使用量
- 提高处理吞吐量 (throughput)

### 7.2 数据质量
- 避免重复血缘记录
- 保持数据的一致性
- 提供准确的变更历史

### 7.3 运维便利
- 减少数据库维护负担
- 提供清晰的处理统计信息
- 支持问题排查和性能调优

---

这个简化的方案专注于核心需求：只有当血缘信息真正发生变化时才更新数据库。通过哈希比较 (hash comparison) 实现高效的变更检测，避免了复杂的版本管理系统，同时保持了代码的简洁性和可维护性 (maintainability)。 