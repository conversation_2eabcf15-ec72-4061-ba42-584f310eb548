package com.datayes.task

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.data.jdbc.repository.query.Query
import org.springframework.data.repository.CrudRepository
import org.springframework.data.repository.PagingAndSortingRepository
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.jdbc.core.RowMapper
import org.springframework.stereotype.Repository
import java.sql.ResultSet
import java.time.LocalDateTime

/**
 * 手动触发血缘任务仓库接口 (Manual Lineage Trigger Task Repository Interface)
 *
 * 基于 Spring Data JDBC 的手动触发血缘任务数据访问接口
 */
@Repository
interface ManualLineageTriggerTaskRepository : CrudRepository<ManualLineageTriggerTask, Long>, PagingAndSortingRepository<ManualLineageTriggerTask, Long> {

    /**
     * 根据任务UUID查找任务 (Find task by task UUID)
     */
    @Query("SELECT * FROM manual_lineage_trigger_tasks WHERE task_uuid = :taskUuid")
    fun findByTaskUuid(taskUuid: String): ManualLineageTriggerTask?

    /**
     * 根据数据源ID查找任务 (Find tasks by datasource ID)
     */
    @Query("SELECT * FROM manual_lineage_trigger_tasks WHERE datasource_id = :datasourceId ORDER BY created_at DESC")
    fun findByDatasourceId(datasourceId: Long): List<ManualLineageTriggerTask>

    /**
     * 根据状态查找任务 (Find tasks by status)
     */
    @Query("SELECT * FROM manual_lineage_trigger_tasks WHERE task_status = :taskStatus ORDER BY created_at DESC")
    fun findByTaskStatus(taskStatus: ManualTaskStatus): List<ManualLineageTriggerTask>

    /**
     * 根据触发用户查找任务 (Find tasks by trigger user)
     */
    @Query("SELECT * FROM manual_lineage_trigger_tasks WHERE trigger_user = :triggerUser ORDER BY created_at DESC")
    fun findByTriggerUser(triggerUser: String): List<ManualLineageTriggerTask>

    /**
     * 查找指定时间范围内的任务 (Find tasks within date range)
     */
    @Query("SELECT * FROM manual_lineage_trigger_tasks WHERE created_at BETWEEN :startDate AND :endDate ORDER BY created_at DESC")
    fun findByCreatedAtBetween(startDate: LocalDateTime, endDate: LocalDateTime): List<ManualLineageTriggerTask>

    /**
     * 根据数据源ID和状态查找任务 (Find tasks by datasource ID and status)
     */
    @Query("SELECT * FROM manual_lineage_trigger_tasks WHERE datasource_id = :datasourceId AND task_status = :taskStatus ORDER BY created_at DESC")
    fun findByDatasourceIdAndTaskStatus(datasourceId: Long, taskStatus: ManualTaskStatus): List<ManualLineageTriggerTask>
}

/**
 * 手动触发血缘任务自定义仓库实现 (Custom Manual Lineage Trigger Task Repository Implementation)
 *
 * 提供复杂查询和分页功能
 */
@Repository
class ManualLineageTriggerTaskCustomRepository(
    private val jdbcTemplate: JdbcTemplate,
    private val objectMapper: ObjectMapper
) {

    /**
     * 分页查询任务 (Find tasks with pagination and criteria)
     */
    fun findTasksWithCriteria(criteria: ManualTaskQueryCriteria, pageable: Pageable): Page<ManualTaskDetailResponse> {
        val whereClause = buildWhereClause(criteria)
        val orderClause = buildOrderClause(pageable)

        // 构建查询SQL，包含数据源信息
        val countSql = """
            SELECT COUNT(*) 
            FROM manual_lineage_trigger_tasks mt
            LEFT JOIN lineage_datasources ld ON mt.datasource_id = ld.id
            $whereClause
        """.trimIndent()

        val dataSql = """
            SELECT mt.*, ld.datasource_name
            FROM manual_lineage_trigger_tasks mt
            LEFT JOIN lineage_datasources ld ON mt.datasource_id = ld.id
            $whereClause 
            $orderClause 
            LIMIT ${pageable.pageSize} OFFSET ${pageable.offset}
        """.trimIndent()

        // 获取参数
        val params = buildParameters(criteria)

        // 执行查询
        val totalElements = jdbcTemplate.queryForObject(countSql, Long::class.java, *params.toTypedArray()) ?: 0L
        val content = if (totalElements > 0) {
            jdbcTemplate.query(dataSql, ManualTaskDetailRowMapper(), *params.toTypedArray())
        } else {
            emptyList()
        }

        return PageImpl(content, pageable, totalElements)
    }

    /**
     * 根据任务UUID获取详细信息 (Get task detail by task UUID)
     */
    fun findTaskDetailByUuid(taskUuid: String): ManualTaskDetailResponse? {
        val sql = """
            SELECT mt.*, ld.datasource_name
            FROM manual_lineage_trigger_tasks mt
            LEFT JOIN lineage_datasources ld ON mt.datasource_id = ld.id
            WHERE mt.task_uuid = ?
        """.trimIndent()

        return try {
            jdbcTemplate.queryForObject(sql, ManualTaskDetailRowMapper(), taskUuid)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 更新任务状态 (Update task status)
     */
    fun updateTaskStatus(
        taskUuid: String,
        status: ManualTaskStatus,
        startedAt: LocalDateTime? = null,
        completedAt: LocalDateTime? = null,
        executionTimeMs: Long? = null,
        errorMessage: String? = null
    ): Int {
        val sql = """
            UPDATE manual_lineage_trigger_tasks 
            SET task_status = ?, 
                started_at = ?, 
                completed_at = ?, 
                execution_time_ms = ?,
                error_message = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE task_uuid = ?
        """.trimIndent()

        return jdbcTemplate.update(
            sql,
            status.name,
            startedAt,
            completedAt,
            executionTimeMs,
            errorMessage,
            taskUuid
        )
    }

    /**
     * 更新任务结果 (Update task results)
     */
    fun updateTaskResults(
        taskUuid: String,
        successCount: Int,
        failureCount: Int,
        totalCount: Int,
        successResults: List<TaskResultItem>? = null,
        failureResults: List<TaskResultItem>? = null,
        errorDetails: JsonNode? = null
    ): Int {
        val sql = """
            UPDATE manual_lineage_trigger_tasks 
            SET success_count = ?, 
                failure_count = ?, 
                total_count = ?,
                success_results = ?,
                failure_results = ?,
                error_details = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE task_uuid = ?
        """.trimIndent()

        return jdbcTemplate.update(
            sql,
            successCount,
            failureCount,
            totalCount,
            successResults?.let { objectMapper.writeValueAsString(it) },
            failureResults?.let { objectMapper.writeValueAsString(it) },
            errorDetails?.let { objectMapper.writeValueAsString(it) },
            taskUuid
        )
    }

    /**
     * 构建WHERE子句 (Build WHERE clause)
     */
    private fun buildWhereClause(criteria: ManualTaskQueryCriteria): String {
        val conditions = mutableListOf<String>()

        criteria.datasourceId?.let { conditions.add("mt.datasource_id = ?") }
        criteria.triggerUser?.let { conditions.add("mt.trigger_user = ?") }
        criteria.taskStatus?.let { conditions.add("mt.task_status = ?") }
        criteria.startDate?.let { conditions.add("mt.created_at >= ?") }
        criteria.endDate?.let { conditions.add("mt.created_at <= ?") }

        return if (conditions.isNotEmpty()) {
            "WHERE ${conditions.joinToString(" AND ")}"
        } else {
            ""
        }
    }

    /**
     * 构建ORDER BY子句 (Build ORDER BY clause)
     */
    private fun buildOrderClause(pageable: Pageable): String {
        if (pageable.sort.isUnsorted) {
            return "ORDER BY mt.created_at DESC"
        }

        val orderFields = pageable.sort.map { order ->
            val columnName = mapPropertyToColumn(order.property)
            "$columnName ${order.direction.name}"
        }.joinToString(", ")

        return "ORDER BY $orderFields"
    }

    /**
     * 将属性名映射到数据库列名 (Map property name to database column name)
     */
    private fun mapPropertyToColumn(propertyName: String): String {
        return when (propertyName) {
            "id" -> "mt.id"
            "taskUuid" -> "mt.task_uuid"
            "datasourceId" -> "mt.datasource_id"
            "triggerUser" -> "mt.trigger_user"
            "taskStatus" -> "mt.task_status"
            "startedAt" -> "mt.started_at"
            "completedAt" -> "mt.completed_at"
            "executionTimeMs" -> "mt.execution_time_ms"
            "successCount" -> "mt.success_count"
            "failureCount" -> "mt.failure_count"
            "totalCount" -> "mt.total_count"
            "createdAt" -> "mt.created_at"
            "updatedAt" -> "mt.updated_at"
            else -> "mt.$propertyName"
        }
    }

    /**
     * 构建查询参数 (Build query parameters)
     */
    private fun buildParameters(criteria: ManualTaskQueryCriteria): List<Any> {
        val params = mutableListOf<Any>()

        criteria.datasourceId?.let { params.add(it) }
        criteria.triggerUser?.let { params.add(it) }
        criteria.taskStatus?.let { params.add(it.name) }
        criteria.startDate?.let { params.add(it) }
        criteria.endDate?.let { params.add(it) }

        return params
    }

    /**
     * 手动任务详情行映射器 (Manual Task Detail Row Mapper)
     */
    private inner class ManualTaskDetailRowMapper : RowMapper<ManualTaskDetailResponse> {
        override fun mapRow(rs: ResultSet, rowNum: Int): ManualTaskDetailResponse {
            return ManualTaskDetailResponse(
                id = rs.getLong("id"),
                taskUuid = rs.getString("task_uuid"),
                datasourceId = rs.getLong("datasource_id"),
                datasourceName = rs.getString("datasource_name"),
                triggerUser = rs.getString("trigger_user"),
                taskStatus = ManualTaskStatus.valueOf(rs.getString("task_status")),
                startedAt = rs.getTimestamp("started_at")?.toLocalDateTime(),
                completedAt = rs.getTimestamp("completed_at")?.toLocalDateTime(),
                executionTimeMs = rs.getObject("execution_time_ms") as? Long,
                successCount = rs.getInt("success_count"),
                failureCount = rs.getInt("failure_count"),
                totalCount = rs.getInt("total_count"),
                errorMessage = rs.getString("error_message"),
                successResults = parseResultItems(rs.getString("success_results")),
                failureResults = parseResultItems(rs.getString("failure_results")),
                createdAt = rs.getTimestamp("created_at").toLocalDateTime(),
                updatedAt = rs.getTimestamp("updated_at").toLocalDateTime()
            )
        }

        private fun parseResultItems(jsonString: String?): List<TaskResultItem> {
            return if (jsonString.isNullOrBlank()) {
                emptyList()
            } else {
                try {
                    objectMapper.readValue(jsonString, objectMapper.typeFactory.constructCollectionType(List::class.java, TaskResultItem::class.java))
                } catch (e: Exception) {
                    emptyList()
                }
            }
        }
    }
}