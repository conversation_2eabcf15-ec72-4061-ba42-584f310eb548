# 脚本影响分析控制器端到端测试说明 (Script Impact Analysis Controller End-to-End Test Guide)

本目录包含 `ScriptImpactAnalysisController` 的端到端测试（end-to-end tests），用于验证脚本影响分析功能的完整工作流程。

## 测试文件概述 (Test Files Overview)

### 1. ScriptImpactAnalysisControllerIT.kt
完整的端到端测试套件，覆盖所有功能：
- ✅ UC-1: 脚本文件上传 (Script File Upload)
- ✅ UC-2: 脚本列表查询 (Script List Query)
- ✅ UC-4: 脚本删除 (Script Deletion)
- ✅ UC-5: 脚本文件下载 (Script File Download)
- ✅ 脚本详情查询 (Script Details Query)
- ✅ 参数验证测试 (Parameter Validation)
- ✅ 错误处理测试 (Error Handling)

### 2. ScriptImpactAnalysisControllerSimpleIT.kt
简化版本的端到端测试，专注于核心功能：
- ✅ 基本的CRUD操作（Create, Read, Update, Delete）
- ✅ 正面测试场景（Happy Path）
- ✅ 基本的错误处理

### 3. TestFileUtils.kt
测试工具类，提供：
- 临时测试文件创建方法
- 标准测试脚本内容模板
- 文件大小和格式测试支持

## 前提条件 (Prerequisites)

### 1. 应用程序必须运行 (Application Must Be Running)
```bash
# 方式1: 使用Maven启动
./mvnw spring-boot:run

# 方式2: 使用IDE启动主类
# 运行 com.datayes.App.main()
```

### 2. 数据库连接 (Database Connection)
确保应用程序能够连接到配置的数据库：
- MySQL 数据库可用
- 数据库表结构已创建
- 连接配置正确

### 3. 文件存储系统 (File Storage System)
确保文件存储功能正常：
- 临时文件目录可写
- 足够的磁盘空间

## 运行测试 (Running Tests)

### 运行完整的端到端测试 (Run Complete End-to-End Tests)
```bash
# 运行完整测试套件
./mvnw test -Dtest=ScriptImpactAnalysisControllerIT

# 运行特定的测试方法
./mvnw test -Dtest=ScriptImpactAnalysisControllerIT#should\ successfully\ upload\ SQL\ script\ file
```

### 运行简化版测试 (Run Simplified Tests)
```bash
# 运行简化测试套件（推荐用于快速验证）
./mvnw test -Dtest=ScriptImpactAnalysisControllerSimpleIT
```

### 运行所有集成测试 (Run All Integration Tests)
```bash
# 运行integration包下的所有测试
./mvnw test -Dtest=com.datayes.integration.**IT
```

## 配置选项 (Configuration Options)

### 1. 测试目标服务器配置 (Test Target Server Configuration)
可以通过系统属性或环境变量配置测试目标：

```bash
# 使用系统属性
./mvnw test -Dtest=ScriptImpactAnalysisControllerSimpleIT \
  -Dtest.api.host=localhost \
  -Dtest.api.port=9503

# 使用环境变量
export TEST_API_HOST=localhost
export TEST_API_PORT=9503
./mvnw test -Dtest=ScriptImpactAnalysisControllerSimpleIT
```

### 2. 默认配置 (Default Configuration)
- **主机 (Host)**: `**********`
- **端口 (Port)**: `9503`
- **基础路径 (Base Path)**: `/api`

## 测试流程说明 (Test Flow Description)

### 典型的测试执行流程 (Typical Test Execution Flow)
1. **上传测试脚本** - 创建临时文件并上传SQL/Shell脚本
2. **查询脚本列表** - 验证上传的脚本出现在列表中
3. **获取脚本详情** - 验证能够获取完整的脚本信息
4. **下载脚本文件** - 验证文件下载功能
5. **删除脚本** - 清理测试数据
6. **验证删除结果** - 确认脚本已被成功删除

### 测试数据隔离 (Test Data Isolation)
- 每次测试使用唯一的用户名标识 (`simple-test-user`, `integration-test-user`)
- 测试结束后自动清理创建的数据
- 使用临时文件，测试结束后自动删除

## 常见问题与解决方案 (Common Issues and Solutions)

### 1. 连接被拒绝错误 (Connection Refused Error)
```
java.net.ConnectException: Connection refused
```
**解决方案**: 确保应用程序正在运行并监听配置的端口

### 2. 404 Not Found 错误
```
Expected status code <200> but was <404>
```
**解决方案**: 
- 检查API路径是否正确
- 确认控制器的`@RequestMapping`配置

### 3. 数据库连接错误
**解决方案**:
- 检查数据库服务是否运行
- 验证连接配置（用户名、密码、URL）
- 确认数据库表结构已正确创建

### 4. 文件上传错误
**解决方案**:
- 检查临时目录权限
- 确认文件大小限制配置
- 验证多部分文件上传配置

## 测试覆盖范围 (Test Coverage)

### 功能覆盖 (Functional Coverage)
- ✅ 文件上传（SQL和Shell脚本）
- ✅ 脚本列表查询（含分页和过滤）
- ✅ 脚本详情查询
- ✅ 脚本文件下载
- ✅ 脚本删除
- ✅ 参数验证
- ✅ 错误处理

### 测试场景 (Test Scenarios)
- ✅ 正常流程测试（Happy Path）
- ✅ 边界条件测试（Edge Cases）
- ✅ 错误处理测试（Error Handling）
- ✅ 数据验证测试（Data Validation）

## 性能考虑 (Performance Considerations)

### 测试执行时间 (Test Execution Time)
- 简化测试套件：约 30-60 秒
- 完整测试套件：约 2-5 分钟

### 资源使用 (Resource Usage)
- 内存使用：主要用于临时文件创建
- 磁盘使用：测试文件自动清理
- 网络使用：HTTP请求到测试目标服务器

## 扩展测试 (Extending Tests)

### 添加新的测试场景
1. 在测试类中添加新的`@Test`方法
2. 使用适当的`@Order`注解保证执行顺序
3. 遵循现有的命名约定和断言模式

### 添加新的测试数据
1. 在`TestFileUtils`中添加新的内容模板
2. 创建新的文件生成方法
3. 在测试中使用新的测试数据

### 参考示例
参考现有的测试方法了解如何：
- 使用RestAssured进行HTTP测试
- 验证JSON响应结构
- 处理文件上传和下载
- 管理测试数据生命周期 