-- ====================================================================
-- 血缘目录查询示例 (Sample Queries for Lineage Catalog)
-- ====================================================================

-- 1. 查询指定表的上游血缘关系 (Query upstream lineage for a specific table)
-- 以指定表为中心，查找其所有上游依赖的表
SELECT 
    st.table_name as source_table,
    st.schema_name as source_schema,
    sds.datasource_name as source_datasource,
    sys.system_name as source_system,
    lr.lineage_type,
    lr.transformation_description,
    lr.source_system as lineage_source,
    lr.created_at
FROM lineage_relationships lr
JOIN lineage_tables st ON lr.source_table_id = st.id
JOIN lineage_tables tt ON lr.target_table_id = tt.id
JOIN lineage_datasources sds ON st.datasource_id = sds.id
JOIN lineage_systems sys ON sds.system_id = sys.id
WHERE tt.table_name = 'target_table_name' 
  AND tt.schema_name = 'target_schema'
  AND lr.relationship_type = 'TABLE_LEVEL'
  AND lr.is_active = true
ORDER BY lr.created_at DESC;

-- 2. 查询指定表的下游血缘关系 (Query downstream lineage for a specific table)
-- 以指定表为中心，查找所有依赖于它的下游表
SELECT 
    tt.table_name as target_table,
    tt.schema_name as target_schema,
    tds.datasource_name as target_datasource,
    sys.system_name as target_system,
    lr.lineage_type,
    lr.transformation_description,
    lr.source_system as lineage_source,
    lr.created_at
FROM lineage_relationships lr
JOIN lineage_tables st ON lr.source_table_id = st.id
JOIN lineage_tables tt ON lr.target_table_id = tt.id
JOIN lineage_datasources tds ON tt.datasource_id = tds.id
JOIN lineage_systems sys ON tds.system_id = sys.id
WHERE st.table_name = 'source_table_name' 
  AND st.schema_name = 'source_schema'
  AND lr.relationship_type = 'TABLE_LEVEL'
  AND lr.is_active = true
ORDER BY lr.created_at DESC;

-- 3. 查询列级血缘关系 (Query column-level lineage)
-- 查询指定列的血缘关系
SELECT 
    sc.column_name as source_column,
    st.table_name as source_table,
    tc.column_name as target_column,
    tt.table_name as target_table,
    lr.transformation_type,
    lr.transformation_description,
    lr.transformation_expression,
    lr.confidence_score
FROM lineage_relationships lr
JOIN lineage_columns sc ON lr.source_column_id = sc.id
JOIN lineage_columns tc ON lr.target_column_id = tc.id
JOIN lineage_tables st ON sc.table_id = st.id
JOIN lineage_tables tt ON tc.table_id = tt.id
WHERE lr.relationship_type = 'COLUMN_LEVEL'
  AND lr.is_active = true
  AND (st.table_name = 'source_table' OR tt.table_name = 'target_table')
ORDER BY sc.column_name, tc.column_name;

-- 4. 按系统分组统计血缘关系 (Statistics by system)
-- 血缘目录首页展示用的统计信息
SELECT 
    sys.system_name,
    sys.system_code,
    COUNT(DISTINCT ds.id) as datasource_count,
    COUNT(DISTINCT t.id) as table_count,
    COUNT(DISTINCT c.id) as column_count,
    COUNT(DISTINCT CASE WHEN lr.relationship_type = 'TABLE_LEVEL' THEN lr.id END) as table_lineage_count,
    COUNT(DISTINCT CASE WHEN lr.relationship_type = 'COLUMN_LEVEL' THEN lr.id END) as column_lineage_count,
    MAX(lr.created_at) as last_update_time
FROM lineage_systems sys
LEFT JOIN lineage_datasources ds ON sys.id = ds.system_id
LEFT JOIN lineage_tables t ON ds.id = t.datasource_id
LEFT JOIN lineage_columns c ON t.id = c.table_id
LEFT JOIN lineage_relationships lr ON (t.id = lr.source_table_id OR t.id = lr.target_table_id)
WHERE sys.status = 'ACTIVE'
  AND (ds.status IS NULL OR ds.status = 'ACTIVE')
  AND (t.status IS NULL OR t.status = 'ACTIVE')
  AND (lr.is_active IS NULL OR lr.is_active = true)
GROUP BY sys.id, sys.system_name, sys.system_code
ORDER BY sys.system_name;

-- 5. 查询多层血缘关系 (Multi-level lineage query)
-- 递归查询血缘关系链条（需要支持WITH RECURSIVE的数据库）
WITH RECURSIVE lineage_chain AS (
    -- 基础查询：直接血缘关系
    SELECT 
        lr.source_table_id,
        lr.target_table_id,
        st.table_name as source_table,
        tt.table_name as target_table,
        1 as level,
        CAST(CONCAT(st.table_name, ' -> ', tt.table_name) AS CHAR(1000)) as lineage_path
    FROM lineage_relationships lr
    JOIN lineage_tables st ON lr.source_table_id = st.id
    JOIN lineage_tables tt ON lr.target_table_id = tt.id
    WHERE st.table_name = 'starting_table_name'
      AND lr.relationship_type = 'TABLE_LEVEL'
      AND lr.is_active = true
    
    UNION ALL
    
    -- 递归查询：扩展血缘链条
    SELECT 
        lr.source_table_id,
        lr.target_table_id,
        st.table_name as source_table,
        tt.table_name as target_table,
        lc.level + 1,
        CAST(CONCAT(lc.lineage_path, ' -> ', tt.table_name) AS CHAR(1000))
    FROM lineage_relationships lr
    JOIN lineage_tables st ON lr.source_table_id = st.id
    JOIN lineage_tables tt ON lr.target_table_id = tt.id
    JOIN lineage_chain lc ON lc.target_table_id = lr.source_table_id
    WHERE lc.level < 5  -- 限制递归深度
      AND lr.relationship_type = 'TABLE_LEVEL'
      AND lr.is_active = true
)
SELECT * FROM lineage_chain
ORDER BY level, source_table, target_table;

-- 6. 查询血缘任务执行状态 (Query lineage task execution status)
-- 血缘任务管理页面用的查询
SELECT 
    lt.task_name,
    lt.task_type,
    lt.source_type,
    lt.task_status,
    lt.is_enabled,
    lt.executed_at,
    lt.completed_at,
    TIMESTAMPDIFF(MINUTE, lt.executed_at, lt.completed_at) as duration_minutes,
    COUNT(DISTINCT lr.id) as generated_relationships,
    lt.error_message
FROM lineage_tasks lt
LEFT JOIN lineage_relationships lr ON lt.id = lr.task_id AND lr.is_active = true
WHERE lt.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY lt.id
ORDER BY lt.executed_at DESC;

-- 7. 查询表的完整信息 (Query complete table information)
-- 点击表节点显示详细属性时使用
SELECT 
    t.table_name,
    t.schema_name,
    t.chinese_name,
    t.description,
    t.sync_frequency,
    t.requirement_id,
    ds.datasource_name,
    ds.db_type,
    ds.host,
    ds.port,
    ds.database_name,
    sys.system_name,
    COUNT(DISTINCT CASE WHEN lr_up.target_table_id = t.id THEN lr_up.id END) as upstream_count,
    COUNT(DISTINCT CASE WHEN lr_down.source_table_id = t.id THEN lr_down.id END) as downstream_count,
    COUNT(DISTINCT c.id) as column_count
FROM lineage_tables t
JOIN lineage_datasources ds ON t.datasource_id = ds.id
LEFT JOIN lineage_systems sys ON ds.system_id = sys.id
LEFT JOIN lineage_columns c ON t.id = c.table_id AND c.status = 'ACTIVE'
LEFT JOIN lineage_relationships lr_up ON t.id = lr_up.target_table_id 
    AND lr_up.relationship_type = 'TABLE_LEVEL' AND lr_up.is_active = true
LEFT JOIN lineage_relationships lr_down ON t.id = lr_down.source_table_id 
    AND lr_down.relationship_type = 'TABLE_LEVEL' AND lr_down.is_active = true
WHERE t.table_name = ? AND t.schema_name = ?
GROUP BY t.id;

-- 8. 查询脚本影响分析结果 (Query script impact analysis results)
-- 脚本影响分析页面使用
SELECT 
    us.script_name,
    us.script_type,
    us.upload_user,
    us.analysis_status,
    us.created_at,
    JSON_EXTRACT(us.analysis_result, '$.affected_tables') as affected_tables,
    JSON_EXTRACT(us.analysis_result, '$.potential_impacts') as potential_impacts
FROM uploaded_scripts us
WHERE us.analysis_status = 'COMPLETED'
  AND us.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
ORDER BY us.created_at DESC;

-- 9. 血缘关系搜索 (Lineage relationship search)
-- 支持模糊搜索表名的血缘关系查询
SELECT DISTINCT
    st.table_name as source_table,
    st.schema_name as source_schema,
    tt.table_name as target_table,
    tt.schema_name as target_schema,
    sds.datasource_name as source_datasource,
    tds.datasource_name as target_datasource,
    lr.lineage_type,
    lr.source_system
FROM lineage_relationships lr
JOIN lineage_tables st ON lr.source_table_id = st.id
JOIN lineage_tables tt ON lr.target_table_id = tt.id
JOIN lineage_datasources sds ON st.datasource_id = sds.id
JOIN lineage_datasources tds ON tt.datasource_id = tds.id
WHERE lr.relationship_type = 'TABLE_LEVEL'
  AND lr.is_active = true
  AND (st.table_name LIKE CONCAT('%', ?, '%') 
       OR tt.table_name LIKE CONCAT('%', ?, '%')
       OR st.chinese_name LIKE CONCAT('%', ?, '%')
       OR tt.chinese_name LIKE CONCAT('%', ?, '%'))
ORDER BY st.table_name, tt.table_name
LIMIT 100;