package com.datayes.integration

import io.restassured.RestAssured.given
import io.restassured.path.json.JsonPath
import org.assertj.core.api.Assertions.assertThat
import org.hamcrest.Matchers.*
import org.hamcrest.collection.IsCollectionWithSize.hasSize
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test

/**
 * 任务执行日志 REST API 集成测试 (Task Execution Log REST API Integration Test)
 *
 * 测试 /api/v1/lineage/tasks/{taskId}/execution-log 和相关端点的功能。
 *
 * 前提条件:
 * - 应用程序必须已经启动并运行在配置的端口上
 * - 数据库中应该存在 lineage_tasks 表和 lineage_execution_logs 表
 * - 测试遵循只读操作原则，不修改数据库数据
 */
@DisplayName("任务执行日志 REST API 集成测试")
class TaskExecutionLogIT : RestApiIntegrationTestBase() {

    @Test
    @DisplayName("应该成功查询存在的任务的执行日志信息")
    fun `should successfully query execution log for existing task`() {
        // 先查询任务列表，获取一个存在的任务ID
        val tasksResponse = given()
            .`when`()
            .get("/v1/lineage/tasks?size=1")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .body("data.content", hasSize<Any>(greaterThanOrEqualTo(0)))
            .extract()
            .response()

        val jsonPath = JsonPath.from(tasksResponse.asString())
        val tasks = jsonPath.getList<Map<String, Any>>("data.content")
        
        if (tasks.isNotEmpty()) {
            val taskId = tasks[0]["id"] as Number
            
            println("6f2a8e3d | 测试任务ID: $taskId")

            // 查询任务的执行日志信息
            val response = given()
                .`when`()
                .get("/v1/lineage/tasks/$taskId/execution-log")
                .then()
                .statusCode(200)
                .contentType("application/json")
                .body("success", equalTo(true))
                .body("message", equalTo("查询成功"))
                .body("data", notNullValue())
                .body("data.task_id", equalTo(taskId.toInt()))
                .body("data.task_name", notNullValue())
                .body("data.task_type", notNullValue())
                .body("data.execution_count", greaterThanOrEqualTo(0))
                .extract()
                .response()

            println("9c5b7f1a | 任务执行日志查询响应: ${response.asString()}")

            // 验证响应数据结构
            val responseData = JsonPath.from(response.asString())
            val data = responseData.getMap<String, Any>("data")
            
            assertThat(data).containsKeys("task_id", "task_name", "task_type", "execution_count")
            assertThat(data["task_id"]).isEqualTo(taskId.toInt())
            assertThat((data["execution_count"] as Number).toInt()).isGreaterThanOrEqualTo(0)
            
        } else {
            println("4a8e2d9f | 没有找到可用于测试的任务，跳过测试")
        }
    }

    @Test
    @DisplayName("查询不存在的任务时应该返回200状态码和错误信息")
    fun `should return 200 with error message for non-existent task`() {
        val nonExistentTaskId = 999999L

        val response = given()
            .`when`()
            .get("/v1/lineage/tasks/$nonExistentTaskId/execution-log")
            .then()
            .statusCode(200)
            .contentType("application/json")
            .body("success", equalTo(false))
            .body("message", containsString("任务不存在"))
            .extract()
            .response()

        println("2e7d4c9a | 不存在任务查询响应: ${response.asString()}")
    }

    @Test
    @DisplayName("应该成功查询任务的所有执行日志")
    fun `should successfully query all execution logs for task`() {
        // 先查询任务列表，获取一个存在的任务ID
        val tasksResponse = given()
            .`when`()
            .get("/v1/lineage/tasks?size=1")
            .then()
            .statusCode(200)
            .body("success", equalTo(true))
            .extract()
            .response()

        val jsonPath = JsonPath.from(tasksResponse.asString())
        val tasks = jsonPath.getList<Map<String, Any>>("data.content")
        
        if (tasks.isNotEmpty()) {
            val taskId = tasks[0]["id"] as Number
            
            println("1f8e5c2b | 测试任务ID (所有日志): $taskId")

            // 查询任务的所有执行日志
            val response = given()
                .`when`()
                .get("/v1/lineage/tasks/$taskId/execution-logs")
                .then()
                .statusCode(200)
                .contentType("application/json")
                .body("success", equalTo(true))
                .body("message", equalTo("查询成功"))
                .body("data", notNullValue())
                .extract()
                .response()

            println("7a3f9e4d | 任务所有执行日志查询响应: ${response.asString()}")

            // 验证响应数据结构
            val responseData = JsonPath.from(response.asString())
            val logs = responseData.getList<Map<String, Any>>("data")
            
            // 验证每个日志条目的结构（如果有日志的话）
            logs.forEach { log ->
                assertThat(log).containsKeys("id", "task_id", "execution_id", "log_level", "log_message", "created_at")
                assertThat(log["task_id"]).isEqualTo(taskId.toInt())
                assertThat(log["execution_id"]).isNotNull()
                assertThat(log["log_level"]).isNotNull()
                assertThat(log["log_message"]).isNotNull()
            }
            
        } else {
            println("8e4a6f2c | 没有找到可用于测试的任务，跳过测试")
        }
    }

    @Test
    @DisplayName("查询所有执行日志时，不存在的任务应该返回200状态码和错误信息")
    fun `should return 200 with error message when querying all execution logs for non-existent task`() {
        val nonExistentTaskId = 999999L

        val response = given()
            .`when`()
            .get("/v1/lineage/tasks/$nonExistentTaskId/execution-logs")
            .then()
            .statusCode(200)
            .contentType("application/json")
            .body("success", equalTo(false))
            .body("message", containsString("任务不存在"))
            .extract()
            .response()

        println("5d9c2f7e | 不存在任务的所有日志查询响应: ${response.asString()}")
    }

    @Test
    @DisplayName("应该验证执行日志响应的JSON结构")
    fun `should validate execution log response JSON structure`() {
        // 先查询任务列表，获取一个存在的任务ID
        val tasksResponse = given()
            .`when`()
            .get("/v1/lineage/tasks?size=1")
            .then()
            .statusCode(200)
            .extract()
            .response()

        val jsonPath = JsonPath.from(tasksResponse.asString())
        val tasks = jsonPath.getList<Map<String, Any>>("data.content")
        
        if (tasks.isNotEmpty()) {
            val taskId = tasks[0]["id"] as Number
            
            println("3b7e4f9c | 验证JSON结构的任务ID: $taskId")

            // 查询任务的执行日志信息并验证JSON结构
            val response = given()
                .`when`()
                .get("/v1/lineage/tasks/$taskId/execution-log")
                .then()
                .statusCode(200)
                .contentType("application/json")
                // 验证顶层响应结构
                .body("success", equalTo(true))
                .body("message", equalTo("查询成功"))
                .body("timestamp", notNullValue())
                .body("data", notNullValue())
                // 验证数据字段结构
                .body("data.task_id", notNullValue())
                .body("data.task_name", notNullValue())
                .body("data.task_type", notNullValue())
                .body("data.execution_count", notNullValue())
                .extract()
                .response()

            println("6a2d8e5f | JSON结构验证响应: ${response.asString()}")

            // 使用JsonPath进行更详细的验证
            val responseData = JsonPath.from(response.asString())
            val data = responseData.getMap<String, Any>("data")
            
            // 验证必需字段存在
            assertThat(data).containsKeys("task_id", "task_name", "task_type", "execution_count")
            
            // 验证数据类型
            assertThat(data["task_id"]).isInstanceOf(Number::class.java)
            assertThat(data["task_name"]).isInstanceOf(String::class.java)
            assertThat(data["task_type"]).isInstanceOf(String::class.java)
            assertThat(data["execution_count"]).isInstanceOf(Number::class.java)
            
            // 如果有最新执行日志，验证其结构
            if (data["latest_execution"] != null) {
                val latestExecution = data["latest_execution"] as Map<String, Any>
                assertThat(latestExecution).containsKeys("id", "task_id", "execution_id", "log_level", "log_message")
            }
            
        } else {
            println("9f3a5e7d | 没有找到可用于测试的任务，跳过JSON结构验证")
        }
    }
}