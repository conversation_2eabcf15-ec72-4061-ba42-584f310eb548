# REST API End-to-End Testing Guide

This guide explains how to write comprehensive end-to-end tests for REST APIs in this Spring Boot application, based on the ManualLineageController test implementation.

## Table of Contents

- [Overview](#overview)
- [Test Configuration](#test-configuration)
- [Test Structure](#test-structure)
- [Testing Patterns](#testing-patterns)
- [Data Management](#data-management)
- [Error Handling](#error-handling)
- [Best Practices](#best-practices)
- [Example Implementation](#example-implementation)

## Overview

End-to-end tests validate the complete request-response cycle through actual HTTP calls to running application endpoints. This approach tests the full integration stack including controllers, services, repositories, and database interactions.

### When to Use E2E Tests

- Testing complete CRUD operations
- Validating complex business workflows
- Ensuring proper integration between layers
- Testing actual HTTP request/response handling
- Validating database transactions and rollbacks

## Test Configuration

### 1. SpringBootTest Setup

```kotlin
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class YourControllerE2ETest {
    
    @LocalServerPort
    private var port: Int = 0
    
    @BeforeEach
    fun setupRestAssured() {
        val host = System.getProperty("test.api.host") 
            ?: System.getenv("TEST_API_HOST") 
            ?: "localhost"
        RestAssured.baseURI = "http://$host"
        RestAssured.port = port
        RestAssured.basePath = "/api"
    }
}
```

**Key Points:**
- `RANDOM_PORT` ensures tests don't conflict with running applications
- `@LocalServerPort` injects the dynamically assigned port
- Support environment variables for different test environments
- Configure RestAssured in `@BeforeEach` for each test method

### 2. Required Dependencies

Ensure your test dependencies include:
```xml
<dependency>
    <groupId>io.rest-assured</groupId>
    <artifactId>rest-assured</artifactId>
    <scope>test</scope>
</dependency>
<dependency>
    <groupId>org.assertj</groupId>
    <artifactId>assertj-core</artifactId>
    <scope>test</scope>
</dependency>
```

## Test Structure

### 1. Test Class Organization

```kotlin
@DisplayName("YourController E2E Tests")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class YourControllerE2ETest {

    companion object {
        // Test constants
        private const val TEST_USER = "e2e-test-user"
        private const val TEST_DATA_PREFIX = "test_data_${System.currentTimeMillis()}"
    }

    // Test data tracking
    private var createdResourceIds: MutableList<Long> = mutableListOf()

    @BeforeEach
    fun setup() {
        // RestAssured configuration
        // Prerequisites verification
    }

    @AfterEach
    fun cleanup() {
        // Clean up created test data
    }

    // Test methods for each operation
}
```

### 2. Test Method Naming

Use descriptive test method names that clearly indicate the expected behavior:

```kotlin
@Test
@DisplayName("Should create resource successfully with valid data")
fun `should create resource successfully with valid data`() { }

@Test
@DisplayName("Should return 404 when resource not found")
fun `should return 404 when resource not found`() { }

@Test
@DisplayName("Should handle invalid requests with proper error responses")
fun `should handle invalid requests with proper error responses`() { }
```

## Testing Patterns

### 1. CRUD Operations Testing

#### Create Operation
```kotlin
@Test
fun `should create resource successfully`() {
    val createRequest = CreateResourceRequest(
        name = "test-resource-${System.currentTimeMillis()}",
        description = "Test description",
        // ... other required fields
    )

    val response = given()
        .contentType(ContentType.JSON)
        .body(createRequest)
        .`when`()
        .post("/resources")
        .then()
        .extract()
        .response()

    // Log response for debugging
    println("ab12cd34 | Create response status: ${response.statusCode}")
    println("ef56gh78 | Create response body: ${response.asString()}")

    // Validate response
    response.then()
        .statusCode(201)
        .contentType(ContentType.JSON)
        .body("success", equalTo(true))
        .body("id", notNullValue())
        .body("name", equalTo(createRequest.name))

    // Track for cleanup
    val resourceId = JsonPath.from(response.asString()).getLong("id")
    createdResourceIds.add(resourceId)
}
```

#### Read Operation
```kotlin
@Test
fun `should get resource details by id`() {
    // First create a resource
    val resourceId = createTestResource()

    // Then retrieve it
    given()
        .pathParam("resourceId", resourceId)
        .`when`()
        .get("/resources/{resourceId}")
        .then()
        .statusCode(200)
        .contentType(ContentType.JSON)
        .body("id", equalTo(resourceId.toInt()))
        .body("name", notNullValue())
        .body("description", notNullValue())
}
```

#### Update Operation
```kotlin
@Test
fun `should update resource successfully`() {
    // Create resource first
    val resourceId = createTestResource()

    // Update it
    val updateRequest = UpdateResourceRequest(
        name = "updated-name-${System.currentTimeMillis()}",
        description = "Updated description"
    )

    given()
        .contentType(ContentType.JSON)
        .pathParam("resourceId", resourceId)
        .body(updateRequest)
        .`when`()
        .put("/resources/{resourceId}")
        .then()
        .statusCode(200)
        .body("success", equalTo(true))

    // Verify the update
    given()
        .pathParam("resourceId", resourceId)
        .`when`()
        .get("/resources/{resourceId}")
        .then()
        .statusCode(200)
        .body("name", equalTo(updateRequest.name))
        .body("description", equalTo(updateRequest.description))
}
```

#### Delete Operation
```kotlin
@Test
fun `should delete resource successfully`() {
    // Create resource first
    val resourceId = createTestResource()

    // Delete it
    given()
        .pathParam("resourceId", resourceId)
        .`when`()
        .delete("/resources/{resourceId}")
        .then()
        .statusCode(200)
        .body("success", equalTo(true))

    // Verify deletion
    given()
        .pathParam("resourceId", resourceId)
        .`when`()
        .get("/resources/{resourceId}")
        .then()
        .statusCode(404)

    // Remove from cleanup list since it's already deleted
    createdResourceIds.remove(resourceId)
}
```

### 2. Error Handling Testing

```kotlin
@Test
fun `should handle invalid requests with proper error responses`() {
    val invalidRequest = CreateResourceRequest(
        name = "", // Invalid empty name
        description = null
    )

    given()
        .contentType(ContentType.JSON)
        .body(invalidRequest)
        .`when`()
        .post("/resources")
        .then()
        .statusCode(400)
        .contentType(ContentType.JSON)
        .body("success", equalTo(false))
        .body("message", notNullValue())
        .body("errors", not(empty<String>()))
}

@Test
fun `should handle non-existent resource requests`() {
    val nonExistentId = 999999L

    given()
        .pathParam("resourceId", nonExistentId)
        .`when`()
        .get("/resources/{resourceId}")
        .then()
        .statusCode(404)
}
```

## Data Management

### 1. Test Data Setup

```kotlin
private fun createTestResource(): Long {
    val createRequest = CreateResourceRequest(
        name = "test-resource-${System.currentTimeMillis()}",
        description = "Test description for E2E testing"
    )

    val response = given()
        .contentType(ContentType.JSON)
        .body(createRequest)
        .`when`()
        .post("/resources")
        .then()
        .statusCode(201)
        .extract()
        .response()

    val resourceId = JsonPath.from(response.asString()).getLong("id")
    createdResourceIds.add(resourceId)
    return resourceId
}
```

### 2. Test Data Cleanup

```kotlin
@AfterEach
fun cleanup() {
    if (createdResourceIds.isNotEmpty()) {
        println("ij90kl12 | Cleaning up ${createdResourceIds.size} created resources")
        try {
            // Use API for cleanup when possible
            val deleteRequest = DeleteResourcesRequest(
                resourceIds = createdResourceIds.toList()
            )
            
            given()
                .contentType(ContentType.JSON)
                .body(deleteRequest)
                .`when`()
                .delete("/resources/batch")
                .then()
                .statusCode(anyOf(equalTo(200), equalTo(500))) // Allow cleanup to fail gracefully
                
            println("mn34op56 | Cleanup completed for resource IDs: $createdResourceIds")
        } catch (e: Exception) {
            println("qr78st90 | Cleanup failed: ${e.message}")
        } finally {
            createdResourceIds.clear()
        }
    }
}
```

### 3. Prerequisites Verification

```kotlin
@BeforeEach
fun verifyPrerequisites() {
    println("ef56gh78 | Verifying test prerequisites...")
    
    // Use JUnit Assumptions for environment checks
    assumeTrue(
        prerequisiteDataExists(), 
        "Required test data must exist in database"
    )
    
    assumeTrue(
        externalServiceAvailable(), 
        "External service must be available for integration tests"
    )
}

private fun prerequisiteDataExists(): Boolean {
    // Check for required reference data
    return try {
        given()
            .`when`()
            .get("/reference/required-data")
            .then()
            .statusCode(200)
            .extract()
            .jsonPath()
            .getBoolean("dataExists")
    } catch (e: Exception) {
        false
    }
}
```

## Error Handling

### 1. Response Debugging

```kotlin
private fun logResponseDetails(response: Response, context: String) {
    println("${generateUniqueId()} | $context response status: ${response.statusCode}")
    println("${generateUniqueId()} | $context response body: ${response.asString()}")
    
    if (response.statusCode >= 400) {
        println("${generateUniqueId()} | $context error headers: ${response.headers}")
    }
}

private fun generateUniqueId(): String {
    return UUID.randomUUID().toString().substring(0, 8)
}
```

### 2. Graceful Error Recovery

```kotlin
@Test
fun `should handle service failures gracefully`() {
    val createRequest = CreateResourceRequest(/* valid data */)

    val response = given()
        .contentType(ContentType.JSON)
        .body(createRequest)
        .`when`()
        .post("/resources")
        .then()
        .extract()
        .response()

    logResponseDetails(response, "Create operation")

    // Handle different failure scenarios
    when (response.statusCode) {
        200, 201 -> {
            // Success case
            val resourceId = JsonPath.from(response.asString()).getLong("id")
            createdResourceIds.add(resourceId)
            // Continue with test assertions
        }
        400 -> {
            // Validation error - might be expected
            val errors = JsonPath.from(response.asString()).getList<String>("errors")
            println("${generateUniqueId()} | Validation errors: $errors")
            // Decide whether to fail or skip test
        }
        500 -> {
            // Server error - log and potentially skip
            println("${generateUniqueId()} | Server error occurred, skipping dependent tests")
            assumeTrue(false, "Server error prevents test execution")
        }
    }
}
```

## Best Practices

### 1. Logging and Traceability

```kotlin
// Use unique log prefixes for each message
println("ab12cd34 | Starting resource creation test")
println("ef56gh78 | Validating response structure")
println("ij90kl12 | Cleaning up test data")

// Include meaningful context in log messages
println("mn34op56 | Created resource with ID: $resourceId for test: ${this::class.simpleName}")
```

### 2. Test Isolation

- Each test should be independent and not rely on other tests
- Use unique test data for each test run (timestamps, UUIDs)
- Clean up all test data in `@AfterEach`
- Use `@DirtiesContext` sparingly and only when necessary

### 3. Assertion Patterns

```kotlin
// Chain assertions for readability
response.then()
    .statusCode(200)
    .contentType(ContentType.JSON)
    .body("success", equalTo(true))
    .body("data", notNullValue())
    .body("data.items", hasSize<Any>(greaterThan(0)))

// Use AssertJ for complex assertions
val jsonPath = JsonPath.from(response.asString())
val items = jsonPath.getList<Map<String, Any>>("data.items")
assertThat(items)
    .isNotEmpty()
    .allSatisfy { item ->
        assertThat(item).containsKeys("id", "name", "createdAt")
        assertThat(item["id"]).isNotNull()
    }
```

### 4. Environment Configuration

```kotlin
// Support different test environments
@BeforeEach
fun setupRestAssured() {
    val host = System.getProperty("test.api.host") 
        ?: System.getenv("TEST_API_HOST") 
        ?: "localhost"
    val basePath = System.getProperty("test.api.basePath")
        ?: System.getenv("TEST_API_BASE_PATH")
        ?: "/api"
        
    RestAssured.baseURI = "http://$host"
    RestAssured.port = port
    RestAssured.basePath = basePath
    
    // Optional authentication setup
    setupAuthentication()
}

private fun setupAuthentication() {
    val apiKey = System.getenv("TEST_API_KEY")
    if (apiKey != null) {
        RestAssured.requestSpecification = RequestSpecBuilder()
            .addHeader("Authorization", "Bearer $apiKey")
            .build()
    }
}
```

## Example Implementation

Here's a complete example based on the ManualLineageController test:

```kotlin
@DisplayName("ManualLineageController E2E Tests")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class ManualLineageControllerE2ETest {

    companion object {
        private const val TEST_USER = "e2e-test-user"
        private const val TEST_SOURCE_DATASOURCE = "hive-hdfs-cluster-default"
        private const val TEST_TARGET_DATASOURCE = "mysql-**********-test"
        private val TEST_SOURCE_TABLE = "test_source_table_${System.currentTimeMillis()}"
        private val TEST_TARGET_TABLE = "test_target_table_${System.currentTimeMillis()}"
    }

    @LocalServerPort
    private var port: Int = 0

    private var createdRelationshipIds: MutableList<Long> = mutableListOf()

    @BeforeEach
    fun setupRestAssured() {
        val host = System.getProperty("test.api.host") ?: "localhost"
        RestAssured.baseURI = "http://$host"
        RestAssured.port = port
        RestAssured.basePath = "/api"
        
        println("ab12cd34 | Setting up E2E test on $host:$port")
    }

    @BeforeEach
    fun verifyPrerequisites() {
        println("ef56gh78 | Verifying test prerequisites...")
        assumeTrue(TEST_SOURCE_DATASOURCE.isNotEmpty(), "Source datasource must be configured")
        assumeTrue(TEST_TARGET_DATASOURCE.isNotEmpty(), "Target datasource must be configured")
    }

    @AfterEach
    fun cleanup() {
        if (createdRelationshipIds.isNotEmpty()) {
            println("ij90kl12 | Cleaning up ${createdRelationshipIds.size} created relationships")
            try {
                val deleteRequest = DeleteLineageRequest(
                    tableRelationshipIdList = createdRelationshipIds.toList()
                )
                
                given()
                    .contentType(ContentType.JSON)
                    .body(deleteRequest)
                    .`when`()
                    .delete("/manual-lineage/v2/delete")
                    .then()
                    .statusCode(anyOf(equalTo(200), equalTo(500)))
                    
                println("mn34op56 | Cleanup completed")
            } catch (e: Exception) {
                println("qr78st90 | Cleanup failed: ${e.message}")
            } finally {
                createdRelationshipIds.clear()
            }
        }
    }

    @Test
    @DisplayName("Should create lineage relationship successfully")
    fun `should create lineage relationship successfully`() {
        val createRequest = CreateLineageRequest(
            sourceDatasourceName = TEST_SOURCE_DATASOURCE,
            sourceDbType = "hive",
            sourceHost = "hdfs-cluster.example.com",
            sourcePort = 9083,
            sourceDatabaseName = "urp_dws",
            sourceTableName = TEST_SOURCE_TABLE,
            targetDatasourceName = TEST_TARGET_DATASOURCE,
            targetDbType = "mysql",
            targetHost = "**********",
            targetPort = 3306,
            targetDatabaseName = "test_db",
            targetTableName = TEST_TARGET_TABLE,
            columns = listOf(
                ColumnMappingRequest(
                    sourceColumnName = "id",
                    sourceDataType = "bigint",
                    targetColumnName = "target_id",
                    targetDataType = "bigint"
                )
            ),
            updateBy = TEST_USER
        )

        val response = given()
            .contentType(ContentType.JSON)
            .body(createRequest)
            .`when`()
            .post("/manual-lineage/v2/create")
            .then()
            .extract()
            .response()

        println("st11uv22 | Create response status: ${response.statusCode}")
        println("wx33yz44 | Create response body: ${response.asString()}")

        response.then()
            .statusCode(200)
            .contentType(ContentType.JSON)
            .body("success", equalTo(true))
            .body("tableRelationshipId", notNullValue())
            .body("affectedTableRelationships", greaterThan(0))

        val relationshipId = JsonPath.from(response.asString()).getLong("tableRelationshipId")
        createdRelationshipIds.add(relationshipId)

        assertThat(relationshipId).isGreaterThan(0)
        println("yz12ab34 | Successfully created lineage relationship with ID: $relationshipId")
    }

    // Additional test methods...
}
```

## Conclusion

This guide provides a comprehensive approach to writing REST API end-to-end tests that are:

- **Reliable**: Using real HTTP calls and proper cleanup
- **Maintainable**: Clear structure and reusable patterns
- **Debuggable**: Comprehensive logging and error handling
- **Isolated**: Independent tests with proper data management
- **Comprehensive**: Testing both happy paths and error scenarios

Follow these patterns to create robust end-to-end tests that provide confidence in your API's functionality and integration with the underlying system components.