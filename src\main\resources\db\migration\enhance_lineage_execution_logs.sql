-- ====================================================================
-- Enhancement to lineage_execution_logs table
-- 增强 lineage_execution_logs 表结构
-- ====================================================================
-- This migration adds fields to support comprehensive task execution logging
-- 本迁移添加字段以支持全面的任务执行日志记录

-- Add new columns to lineage_execution_logs table
ALTER TABLE lineage_execution_logs 
ADD COLUMN started_at TIMESTAMP NULL COMMENT '任务开始时间',
ADD COLUMN completed_at TIMESTAMP NULL COMMENT '任务完成时间',
ADD COLUMN task_status ENUM('PENDING', 'RUNNING', 'SUCCESS', 'FAILED', 'CANCELLED') NULL COMMENT '任务状态',
ADD COLUMN database_connection_string TEXT NULL COMMENT '数据库连接字符串',
ADD COLUMN sql_queries LONGTEXT NULL COMMENT 'SQL查询语句(JSON格式)',
ADD COLUMN source_job_id VARCHAR(255) NULL COMMENT '来源作业ID',
ADD COLUMN source_job_type ENUM('DATA_EXCHANGE_JOB', 'HDFS_SHELL_SCRIPT_JOB') NULL COMMENT '来源作业类型',
ADD COLUMN additional_info JSON NULL COMMENT '其他信息(JSON格式)';

-- Add indexes for better query performance
CREATE INDEX idx_lineage_execution_logs_started_at ON lineage_execution_logs(started_at);
CREATE INDEX idx_lineage_execution_logs_completed_at ON lineage_execution_logs(completed_at);
CREATE INDEX idx_lineage_execution_logs_task_status ON lineage_execution_logs(task_status);
CREATE INDEX idx_lineage_execution_logs_source_job_id ON lineage_execution_logs(source_job_id);
CREATE INDEX idx_lineage_execution_logs_source_job_type ON lineage_execution_logs(source_job_type);

-- Update table comment
ALTER TABLE lineage_execution_logs COMMENT = '血缘执行日志表 - 记录任务执行的完整生命周期';