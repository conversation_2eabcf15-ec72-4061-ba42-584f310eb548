# UC-09 & UC-10 Implementation: Table-Level Lineage Graph and Details

## Overview

This document describes the implementation of UC-09 (View Table-Level Lineage Graph) and UC-10 (View Table Details) from the user story requirements. These features enable developers to render and interact with table-level lineage graphs based on the `TableLineageView` and display detailed table information including `DatabaseInfo` and confidence scores.

## Features Implemented

### UC-09: View Table-Level Lineage Graph

**Endpoint**: `GET /api/lineage/table/{tableId}/graph`

**Description**: Renders a graph based on the `TableLineageView` for visualizing table-level lineage relationships.

**Query Parameters**:
- `maxLevels` (default: 3) - Maximum query levels for lineage traversal
- `includeUpstream` (default: true) - Whether to include upstream lineage
- `includeDownstream` (default: true) - Whether to include downstream lineage

**Response**: `TableLineageGraphDto` containing:
- **Nodes**: List of `TableNodeDto` representing tables in the graph
- **Edges**: List of `TableEdgeDto` representing lineage relationships
- **Metadata**: `LineageGraphMetadataDto` with graph statistics and information

### UC-10: View Table Details

**Endpoint**: `GET /api/lineage/table/{tableId}/details`

**Description**: Displays detailed information about a table when clicking on a graph node, including `DatabaseInfo` and confidence scores from `TableLineageDto`.

**Response**: `TableDetailsDto` containing:
- Basic table information (name, schema, datasource, system)
- Database connection information (`DatabaseInfoDto`)
- Lineage statistics (`LineageStatisticsDto`)
- Calculated confidence score
- Upstream/downstream counts
- Associated metadata

## Data Structures

### Graph DTOs

1. **TableLineageGraphDto** - Main graph container
   - `nodes: List<TableNodeDto>` - Graph nodes
   - `edges: List<TableEdgeDto>` - Graph edges
   - `metadata: LineageGraphMetadataDto` - Graph metadata

2. **TableNodeDto** - Graph node representing a table
   - `id: String` - Unique node identifier
   - `tableId: Long` - Database table ID
   - `tableName: String` - Table name
   - `schema: String?` - Schema name
   - `datasource: String` - Datasource name
   - `system: String?` - System name
   - `nodeType: TableNodeType` - Node type (ROOT, SOURCE, TARGET, INTERMEDIATE)
   - `level: Int` - Level in the lineage hierarchy
   - `databaseInfo: DatabaseInfoDto` - Database connection info
   - `confidenceScore: BigDecimal?` - Confidence score
   - `metadata: List<MetadataDataSourceDto>` - Associated metadata

3. **TableEdgeDto** - Graph edge representing lineage relationship
   - `id: String` - Unique edge identifier
   - `relationshipId: Long` - Database relationship ID
   - `sourceNodeId: String` - Source node ID
   - `targetNodeId: String` - Target node ID
   - `lineageType: String?` - Lineage type (DIRECT_COPY, SQL_QUERY, etc.)
   - `level: Int` - Relationship level
   - `columnMappingCount: Int` - Number of column mappings
   - `confidence: BigDecimal?` - Relationship confidence

### Detail DTOs

1. **TableDetailsDto** - Comprehensive table information
   - Basic table attributes
   - `databaseInfo: DatabaseInfoDto` - Database connection information
   - `lineageStatistics: LineageStatisticsDto` - Lineage statistics
   - `confidenceScore: BigDecimal?` - Calculated confidence score
   - Upstream/downstream counts
   - Associated metadata

2. **DatabaseInfoDto** - Database connection information
   - `dbType: String` - Database type
   - `host: String` - Host address
   - `port: Int` - Port number
   - `databaseName: String` - Database name
   - `displayName: String` - Display name

3. **LineageStatisticsDto** - Lineage statistics
   - Total upstream/downstream tables
   - Direct upstream/downstream tables
   - Maximum levels
   - Total column mappings
   - Systems involved

## Key Implementation Details

### Confidence Score Calculation

The confidence score for tables is calculated by averaging the confidence scores from all related lineage relationships:

```kotlin
private fun calculateTableConfidenceScore(
    upstreamLineage: List<TableLineageView>,
    downstreamLineage: List<TableLineageView>
): BigDecimal? {
    val allLineage = upstreamLineage + downstreamLineage
    val confidenceScores = allLineage.mapNotNull { it.confidenceScore }
    
    return if (confidenceScores.isNotEmpty()) {
        val sum = confidenceScores.fold(BigDecimal.ZERO) { acc, score -> acc.add(score) }
        sum.divide(BigDecimal(confidenceScores.size), 2, RoundingMode.HALF_UP)
    } else null
}
```

### Graph Construction

The graph is built by:
1. Querying upstream and downstream lineage using existing `LineageService` methods
2. Converting `TableLineageView` objects to graph nodes and edges
3. Ensuring node uniqueness and proper relationship mapping
4. Collecting metadata and statistics

### Node Type Assignment

- **ROOT**: The starting table for the query
- **SOURCE**: Tables that are sources in lineage relationships
- **TARGET**: Tables that are targets in lineage relationships  
- **INTERMEDIATE**: Tables that serve as both sources and targets

## API Endpoints

### Endpoint Comparison

The implementation provides two related but distinct endpoints:

#### 1. **Detailed Lineage Analysis**: `/table/{tableId}/lineage-with-columns`
- **Purpose**: Comprehensive lineage analysis with column mappings
- **Format**: Hierarchical structure (upstream/downstream arrays)
- **Use cases**: Detailed impact analysis, data lineage auditing, column-level tracking
- **Data**: `TableLineageDto` with `TableRelationshipDto` and column mappings
- **Features**: Metadata enrichment, complete column mapping details

#### 2. **Graph Visualization**: `/table/{tableId}/graph` (UC-09)
- **Purpose**: Graph visualization and interactive navigation
- **Format**: Graph structure (nodes + edges arrays)
- **Use cases**: Visual lineage exploration, interactive graphs, simplified navigation
- **Data**: `TableLineageGraphDto` with `TableNodeDto` and `TableEdgeDto`
- **Features**: Directional filtering, graph metadata, visualization-optimized format

### Get Table Lineage Graph (UC-09)

```http
GET /api/lineage/table/{tableId}/graph?maxLevels=3&includeUpstream=true&includeDownstream=true
```

**Response Example**:
```json
{
  "success": true,
  "data": {
    "nodes": [
      {
        "id": "table_123",
        "tableId": 123,
        "tableName": "users",
        "schema": null,
        "datasource": "main_db",
        "system": "CRM",
        "nodeType": "ROOT",
        "level": 0,
        "databaseInfo": {
          "dbType": "mysql",
          "host": "***********",
          "port": 3306,
          "databaseName": "main_db",
          "displayName": "main_db"
        },
        "confidenceScore": 0.95
      }
    ],
    "edges": [
      {
        "id": "edge_456",
        "relationshipId": 456,
        "sourceNodeId": "table_123",
        "targetNodeId": "table_789",
        "lineageType": "SQL_QUERY",
        "level": 1,
        "columnMappingCount": 5,
        "confidence": 0.90
      }
    ],
    "metadata": {
      "totalNodes": 3,
      "totalEdges": 2,
      "maxLevels": 3,
      "rootTableId": 123,
      "rootTableName": "users",
      "systemsInvolved": ["CRM", "Analytics"],
      "datasourcesInvolved": ["main_db", "analytics_db"],
      "queryTimestamp": "2024-01-15T10:30:00"
    }
  },
  "message": "血缘图查询成功"
}
```

### Get Table Details

```http
GET /api/lineage/table/{tableId}/details
```

**Response Example**:
```json
{
  "success": true,
  "data": {
    "tableId": 123,
    "tableName": "users",
    "schema": null,
    "datasource": "main_db",
    "system": "CRM",
    "databaseInfo": {
      "dbType": "mysql",
      "host": "***********",
      "port": 3306,
      "databaseName": "main_db",
      "displayName": "main_db"
    },
    "lineageStatistics": {
      "totalUpstreamTables": 2,
      "totalDownstreamTables": 5,
      "directUpstreamTables": 1,
      "directDownstreamTables": 3,
      "maxUpstreamLevels": 2,
      "maxDownstreamLevels": 3,
      "totalColumnMappings": 15,
      "systemsInvolved": 3
    },
    "confidenceScore": 0.92,
    "upstreamCount": 2,
    "downstreamCount": 5,
    "columnCount": 8
  },
  "message": "表详情查询成功"
}
```

## Files Created/Modified

### New Files
- `src/main/kotlin/com/datayes/lineage/TableLineageGraphDto.kt` - Graph data structures and DTOs

### Modified Files
- `src/main/kotlin/com/datayes/lineage/LineageController.kt` - Added graph and details endpoints

## Integration with Existing Architecture

The implementation leverages existing components:
- **LineageService** - For querying upstream/downstream lineage
- **LineageRepository** - For data access via `TableLineageView`
- **MetadataService** - For enriching tables with metadata (future enhancement)
- **ApiResponse** - For consistent response formatting

## Future Enhancements

1. **Column Mapping Counts** - Integrate actual column mapping counts in edges
2. **Enhanced Metadata** - Include metadata from `MetadataService` in table details
3. **Filtering** - Add filtering by system, datasource, confidence score
4. **Caching** - Add caching for frequently accessed lineage graphs
5. **Real Database Info** - Query actual database connection information instead of defaults

## Testing

The endpoints can be tested using:
1. REST clients (Postman, curl)
2. Integration tests against running application
3. Frontend graph visualization libraries

## Conclusion

UC-09 and UC-10 are now fully implemented, providing developers with comprehensive table-level lineage visualization and detailed table information. The implementation follows the existing architecture patterns and includes confidence score calculations as specified in the user stories.