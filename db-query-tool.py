#!/usr/bin/env python3
"""
Database Query Tool for DGP Lineage Collector

A simple command-line tool to query database schemas and data.
Only performs read-only operations on the main database.

Usage:
    python db-query-tool.py --help
    python db-query-tool.py --list-tables
    python db-query-tool.py --describe TABLE_NAME
    python db-query-tool.py --query "SELECT * FROM table LIMIT 10"
    python db-query-tool.py --interactive

Requirements:
    pip install mysql-connector-python tabulate
"""

import argparse
import sys
import mysql.connector
from mysql.connector import Error
from tabulate import tabulate
import re


class DatabaseConfig:
    """Database configuration"""
    
    MAIN_DB = {
        'host': '***********',
        'port': 3306,
        'database': 'dgp',
        'user': 'dbausr',
        'password': 'Qweasd23!',
        'charset': 'utf8',
        'use_unicode': True
    }


class DatabaseQueryTool:
    """Main database query tool class"""
    
    def __init__(self):
        self.connection = None
        self.cursor = None
        
    def connect(self):
        """Connect to the main database"""
        try:
            config = DatabaseConfig.MAIN_DB
            self.connection = mysql.connector.connect(**config)
            self.cursor = self.connection.cursor()
            print(f"✓ Connected to main database ({config['host']}:{config['port']}/{config['database']})")
            return True
        except Error as e:
            print(f"✗ Error connecting to main database: {e}")
            return False
    
    def disconnect(self):
        """Close database connection"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
            print("✓ Disconnected from main database")
    
    def is_read_only_query(self, sql):
        """Check if SQL query is read-only"""
        sql_clean = sql.strip().upper()
        # Allow SELECT, SHOW, DESCRIBE, EXPLAIN
        read_only_keywords = ['SELECT', 'SHOW', 'DESCRIBE', 'DESC', 'EXPLAIN']
        # Disallow INSERT, UPDATE, DELETE, DROP, CREATE, ALTER, TRUNCATE
        write_keywords = ['INSERT', 'UPDATE', 'DELETE', 'DROP', 'CREATE', 'ALTER', 'TRUNCATE', 'REPLACE']
        
        first_word = sql_clean.split()[0] if sql_clean.split() else ''
        
        if first_word in write_keywords:
            return False
        if first_word in read_only_keywords:
            return True
        
        # Additional check for any write operations in the query
        for keyword in write_keywords:
            if keyword in sql_clean:
                return False
        
        return True
    
    def execute_query(self, sql):
        """Execute a read-only SQL query"""
        if not self.is_read_only_query(sql):
            print("✗ Error: Only read-only queries are allowed (SELECT, SHOW, DESCRIBE, EXPLAIN)")
            return None
            
        try:
            self.cursor.execute(sql)
            
            # Get column names
            columns = [desc[0] for desc in self.cursor.description] if self.cursor.description else []
            
            # Fetch results
            rows = self.cursor.fetchall()
            
            return columns, rows
            
        except Error as e:
            print(f"✗ Query error: {e}")
            return None
    
    def list_tables(self):
        """List all tables in the database"""
        result = self.execute_query("SHOW TABLES")
        if result:
            columns, rows = result
            print(f"\n📋 Tables in main database:")
            print(tabulate(rows, headers=columns, tablefmt="grid"))
            print(f"\nTotal: {len(rows)} tables")
        
    def describe_table(self, table_name):
        """Describe table structure"""
        result = self.execute_query(f"DESCRIBE `{table_name}`")
        if result:
            columns, rows = result
            print(f"\n📊 Structure of table '{table_name}':")
            print(tabulate(rows, headers=columns, tablefmt="grid"))
            
            # Also show row count
            count_result = self.execute_query(f"SELECT COUNT(*) as row_count FROM `{table_name}`")
            if count_result:
                _, count_rows = count_result
                print(f"\nRow count: {count_rows[0][0]}")
        
    def query_data(self, sql):
        """Execute custom SQL query"""
        result = self.execute_query(sql)
        if result:
            columns, rows = result
            if rows:
                print(f"\n📊 Query results:")
                print(tabulate(rows, headers=columns, tablefmt="grid"))
                print(f"\nRows returned: {len(rows)}")
            else:
                print("✓ Query executed successfully, no results returned")
    
    def interactive_mode(self):
        """Interactive query mode"""
        print(f"\n🚀 Interactive mode for main database")
        print("Type 'help' for commands, 'exit' to quit")
        
        while True:
            try:
                query = input(f"\nmain> ").strip()
                
                if not query:
                    continue
                    
                if query.lower() == 'exit':
                    break
                elif query.lower() == 'help':
                    self.show_help()
                elif query.lower() == 'tables':
                    self.list_tables()
                elif query.lower().startswith('desc '):
                    table_name = query[5:].strip()
                    self.describe_table(table_name)
                else:
                    self.query_data(query)
                    
            except KeyboardInterrupt:
                print("\n✓ Interrupted by user")
                break
            except Exception as e:
                print(f"✗ Error: {e}")
    
    def show_help(self):
        """Show interactive help"""
        help_text = """
📖 Interactive Commands:
  desc <table>    - Describe table structure
  <SQL query>     - Execute any read-only SQL query
  help            - Show this help
  exit            - Exit interactive mode

💡 Examples:
  SELECT * FROM lineage_tables LIMIT 5
  SHOW CREATE TABLE lineage_tables
  SELECT COUNT(*) FROM lineage_tasks
"""
        print(help_text)


def main():
    parser = argparse.ArgumentParser(
        description="Database Query Tool for DGP Lineage Collector (Read-only)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s --list-tables
  %(prog)s --describe lineage_tables
  %(prog)s --query "SELECT * FROM lineage_tables LIMIT 5"
  %(prog)s --interactive
        """
    )
    
    
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--list-tables', action='store_true', help='List all tables')
    group.add_argument('--describe', metavar='TABLE', help='Describe table structure')
    group.add_argument('--query', metavar='SQL', help='Execute SQL query')
    group.add_argument('--interactive', action='store_true', help='Interactive query mode')
    
    args = parser.parse_args()
    
    # Create and connect to database
    tool = DatabaseQueryTool()
    if not tool.connect():
        sys.exit(1)
    
    try:
        if args.list_tables:
            tool.list_tables()
        elif args.describe:
            tool.describe_table(args.describe)
        elif args.query:
            tool.query_data(args.query)
        elif args.interactive:
            tool.interactive_mode()
            
    finally:
        tool.disconnect()


if __name__ == '__main__':
    main()