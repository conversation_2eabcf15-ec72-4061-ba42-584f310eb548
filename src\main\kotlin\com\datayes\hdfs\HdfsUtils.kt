package com.datayes.hdfs

import org.apache.hadoop.conf.Configuration
import org.apache.hadoop.fs.FileSystem
import org.apache.hadoop.fs.Path
import org.apache.hadoop.security.UserGroupInformation
import org.slf4j.LoggerFactory
import java.io.ByteArrayOutputStream
import java.util.zip.ZipInputStream

/**
 * Represents a shell script extracted from a ZIP file
 * 
 * @param name The name of the shell script file (e.g., "script.sh")
 * @param content The content of the shell script as a string
 * @param sizeBytes The size of the script content in bytes
 */
data class ShellScript(
    val name: String,
    val content: String,
    val sizeBytes: Int = content.toByteArray(Charsets.UTF_8).size
)

/**
 * Represents a ZIP file processed for shell script extraction
 * 
 * @param zipFilePath The full HDFS path to the ZIP file
 * @param shellScripts List of shell scripts found in this ZIP file
 * @param totalScriptCount Total number of shell scripts extracted
 * @param totalContentSize Total size of all script content in bytes
 */
data class ZipFileProcessingResult(
    val zipFilePath: String,
    val shellScripts: List<ShellScript>,
    val totalScriptCount: Int = shellScripts.size,
    val totalContentSize: Int = shellScripts.sumOf { it.sizeBytes }
)

/**
 * Represents the overall result of processing ZIP files for shell scripts
 * 
 * @param results List of individual ZIP file processing results
 * @param totalZipFilesProcessed Total number of ZIP files processed
 * @param totalShellScriptsFound Total number of shell scripts found across all ZIP files
 * @param totalContentSize Total size of all script content in bytes
 */
data class ZipProcessingResult(
    val results: List<ZipFileProcessingResult>,
    val totalZipFilesProcessed: Int = results.size,
    val totalShellScriptsFound: Int = results.sumOf { it.totalScriptCount },
    val totalContentSize: Int = results.sumOf { it.totalContentSize }
)

/**
 * HDFS Utility object for connecting to HDFS and performing file operations
 * 
 * Provides reusable functionality for HDFS connectivity, authentication,
 * and file listing operations with filtering capabilities.
 */
object HdfsUtils {
    
    private val logger = LoggerFactory.getLogger(HdfsUtils::class.java)
    
    /**
     * Create and configure HDFS connection with TBDS authentication
     */
    fun createHdfsConnection(): FileSystem {
        setupHadoopEnvironment()
        
        val configuration = Configuration()
        
        // TBDS authentication configuration
        configuration.set("hadoop_security_authentication_tbds_username", "ms_urp")
        configuration.set("hadoop_security_authentication_tbds_secureid", "ze2OFe78OHPrGnvc6WsdJ0Gruum1cpqLowhC")
        configuration.set("hadoop_security_authentication_tbds_securekey", "aYE90rxcOX01KhXgzFegiHKRAjsmUPYy")
        configuration.set("fs.hdfs.impl", "org.apache.hadoop.hdfs.DistributedFileSystem")
        
        // Load configuration files if available
        loadConfigurationFiles(configuration)
        
        // Set up authentication
        UserGroupInformation.setConfiguration(configuration)
        UserGroupInformation.loginUserFromSubject(null)
        
        return FileSystem.get(configuration)
    }
    
    /**
     * Recursively find all files with specified extensions in the given directory
     * 
     * @param fileSystem HDFS FileSystem instance
     * @param path Starting directory path
     * @param extensions File extensions to search for (e.g., ["sh", "zip"])
     * @return List of file paths matching the extensions
     */
    fun findFilesByExtensions(fileSystem: FileSystem, path: Path, extensions: List<String>): List<String> {
        val result = mutableListOf<String>()
        findFilesByExtensionsRecursive(fileSystem, path, extensions, result)
        return result
    }
    
    /**
     * Recursively find files with specified extensions, excluding those with date postfix
     * 
     * @param fileSystem HDFS FileSystem instance
     * @param path Starting directory path
     * @param extensions File extensions to search for (e.g., ["sh", "zip"])
     * @return List of file paths matching extensions but without 14-digit date postfix
     */
    fun findFilesByExtensionsWithoutDatePostfix(fileSystem: FileSystem, path: Path, extensions: List<String>): List<String> {
        val result = mutableListOf<String>()
        findFilesByExtensionsWithoutDatePostfixRecursive(fileSystem, path, extensions, result)
        return result
    }
    
    /**
     * List all files and directories in the specified path
     * 
     * @param fileSystem HDFS FileSystem instance
     * @param path Directory path to list
     * @return List of file/directory names
     */
    fun listDirectory(fileSystem: FileSystem, path: Path): List<String> {
        return try {
            val fileStatuses = fileSystem.listStatus(path)
            fileStatuses.map { it.path.name }
        } catch (e: Exception) {
            logger.warn("Failed to list directory: {}", path, e)
            emptyList()
        }
    }
    
    /**
     * Check if a path exists in HDFS
     * 
     * @param fileSystem HDFS FileSystem instance
     * @param path Path to check
     * @return true if path exists, false otherwise
     */
    fun pathExists(fileSystem: FileSystem, path: Path): Boolean {
        return try {
            fileSystem.exists(path)
        } catch (e: Exception) {
            logger.warn("Failed to check path existence: {}", path, e)
            false
        }
    }
    
    /**
     * Find all ZIP files in the specified HDFS directory
     * 
     * @param fileSystem HDFS FileSystem instance
     * @param path Directory path to search for ZIP files
     * @return List of ZIP file paths
     */
    fun findZipFiles(fileSystem: FileSystem, path: Path): List<String> {
        return findFilesByExtensions(fileSystem, path, listOf("zip"))
    }
    
    /**
     * Read file content from HDFS as String
     * 
     * @param fileSystem HDFS FileSystem instance
     * @param filePath Path to the file to read
     * @return File content as String
     */
    fun readFileContent(fileSystem: FileSystem, filePath: Path): String {
        return try {
            fileSystem.open(filePath).use { inputStream ->
                inputStream.readBytes().toString(Charsets.UTF_8)
            }
        } catch (e: Exception) {
            logger.warn("32a4f7b1 | Failed to read file content: {}", filePath, e)
            ""
        }
    }
    
    /**
     * Extract and read all .sh files from a ZIP file in HDFS
     * 
     * @param fileSystem HDFS FileSystem instance
     * @param zipFilePath Path to the ZIP file in HDFS
     * @return List of ShellScript objects extracted from the ZIP file
     */
    fun extractShellScriptsFromZip(fileSystem: FileSystem, zipFilePath: Path): List<ShellScript> {
        val result = mutableListOf<ShellScript>()
        
        try {
            fileSystem.open(zipFilePath).use { hdfsInputStream ->
                ZipInputStream(hdfsInputStream).use { zipInputStream ->
                    var entry = zipInputStream.nextEntry
                    
                    while (entry != null) {
                        if (!entry.isDirectory && entry.name.lowercase().endsWith(".sh")) {
                            val content = readZipEntryContent(zipInputStream)
                            val shellScript = ShellScript(
                                name = entry.name,
                                content = content
                            )
                            result.add(shellScript)
                            logger.debug("8f4e9a2c | Extracted shell script: {} ({} bytes) from {}", 
                                        entry.name, shellScript.sizeBytes, zipFilePath)
                        }
                        zipInputStream.closeEntry()
                        entry = zipInputStream.nextEntry
                    }
                }
            }
        } catch (e: Exception) {
            logger.warn("d7c3e8f5 | Failed to extract shell scripts from ZIP file: {}", zipFilePath, e)
        }
        
        return result
    }
    
    /**
     * Filter ZIP files to exclude backup copies with timestamps
     * 
     * This function identifies and filters out backup copies of ZIP files that have timestamps
     * added to their names. The patterns supported include:
     * - filename.20240313213459.zip (dot + 14-digit timestamp)
     * - filename20230914161419.zip (direct concatenation + 14-digit timestamp)
     * 
     * @param zipFilePaths List of ZIP file paths to filter
     * @return List of ZIP file paths excluding backup copies with timestamps
     */
    fun filterBackupZipFiles(zipFilePaths: List<String>): List<String> {
        val result = mutableListOf<String>()
        val backupPatterns = listOf(
            // Pattern 1: filename.YYYYMMDDHHMMSS.zip (dot separator)
            Regex(".*\\.\\d{14}\\.zip$"),
            // Pattern 2: filenameYYYYMMDDHHMMSS.zip (direct concatenation, 14 digits at end before .zip)
            Regex(".*\\d{14}\\.zip$")
        )
        
        for (zipFilePath in zipFilePaths) {
            val fileName = zipFilePath.substringAfterLast("/").lowercase()
            val isBackup = backupPatterns.any { pattern -> pattern.matches(fileName) }
            
            if (!isBackup) {
                result.add(zipFilePath)
                logger.debug("6a8e2f4c | Including ZIP file: {}", zipFilePath)
            } else {
                logger.debug("9d3b7e1a | Filtering out backup ZIP file: {}", zipFilePath)
            }
        }
        
        logger.info("f4c8e2a6 | Filtered {} ZIP files, excluded {} backup files", 
                    result.size, zipFilePaths.size - result.size)
        
        return result
    }
    
    /**
     * Process all ZIP files in a directory and extract shell scripts from each
     * Excludes backup ZIP files with timestamps from processing
     * 
     * @param fileSystem HDFS FileSystem instance
     * @param directoryPath Directory path containing ZIP files
     * @return ZipProcessingResult containing all extracted shell scripts organized by ZIP file
     */
    fun processZipFilesForShellScripts(fileSystem: FileSystem, directoryPath: Path): ZipProcessingResult {
        val zipFileResults = mutableListOf<ZipFileProcessingResult>()
        
        val allZipFiles = findZipFiles(fileSystem, directoryPath)
        logger.info("b2e5f9a8 | Found {} ZIP files in directory: {}", allZipFiles.size, directoryPath)
        
        // Filter out backup ZIP files with timestamps
        val filteredZipFiles = filterBackupZipFiles(allZipFiles)
        logger.info("3e7f9b2d | Processing {} ZIP files after filtering out backups", filteredZipFiles.size)
        
        for (zipFilePath in filteredZipFiles) {
            val shellScripts = extractShellScriptsFromZip(fileSystem, Path(zipFilePath))
            if (shellScripts.isNotEmpty()) {
                val zipResult = ZipFileProcessingResult(
                    zipFilePath = zipFilePath,
                    shellScripts = shellScripts
                )
                zipFileResults.add(zipResult)
                logger.info("4d6c8e1a | Extracted {} shell scripts ({} bytes total) from: {}", 
                           zipResult.totalScriptCount, zipResult.totalContentSize, zipFilePath)
            } else {
                logger.debug("e8f2a6c4 | No shell scripts found in ZIP file: {}", zipFilePath)
            }
        }
        
        val finalResult = ZipProcessingResult(results = zipFileResults)
        logger.info("c9e4f7b2 | Processing complete: {} ZIP files processed, {} shell scripts found, {} bytes total",
                   finalResult.totalZipFilesProcessed, finalResult.totalShellScriptsFound, finalResult.totalContentSize)
        
        return finalResult
    }
    
    private fun readZipEntryContent(zipInputStream: ZipInputStream): String {
        val buffer = ByteArray(8192)
        val outputStream = ByteArrayOutputStream()
        
        var bytesRead = zipInputStream.read(buffer)
        while (bytesRead != -1) {
            outputStream.write(buffer, 0, bytesRead)
            bytesRead = zipInputStream.read(buffer)
        }
        
        return outputStream.toString(Charsets.UTF_8.name())
    }
    
    private fun findFilesByExtensionsRecursive(
        fileSystem: FileSystem, 
        path: Path, 
        extensions: List<String>, 
        result: MutableList<String>
    ) {
        try {
            val fileStatuses = fileSystem.listStatus(path)
            
            for (fileStatus in fileStatuses) {
                if (fileStatus.isDirectory) {
                    findFilesByExtensionsRecursive(fileSystem, fileStatus.path, extensions, result)
                } else {
                    val fileName = fileStatus.path.name.lowercase()
                    if (extensions.any { fileName.endsWith(".$it") }) {
                        result.add(fileStatus.path.toString())
                    }
                }
            }
        } catch (e: Exception) {
            logger.warn("Failed to list files in directory: {}", path, e)
        }
    }
    
    private fun findFilesByExtensionsWithoutDatePostfixRecursive(
        fileSystem: FileSystem, 
        path: Path, 
        extensions: List<String>, 
        result: MutableList<String>
    ) {
        try {
            val fileStatuses = fileSystem.listStatus(path)
            
            for (fileStatus in fileStatuses) {
                if (fileStatus.isDirectory) {
                    findFilesByExtensionsWithoutDatePostfixRecursive(fileSystem, fileStatus.path, extensions, result)
                } else {
                    val fileName = fileStatus.path.name.lowercase()
                    if (extensions.any { fileName.endsWith(".$it") }) {
                        // Filter out files with date postfix (14 digits before file extension)
                        val extensionPattern = extensions.joinToString("|")
                        val datePostfixPattern = Regex(".*\\d{14}\\.($extensionPattern)$")
                        if (!datePostfixPattern.matches(fileName)) {
                            result.add(fileStatus.path.toString())
                        }
                    }
                }
            }
        } catch (e: Exception) {
            logger.warn("Failed to list files in directory: {}", path, e)
        }
    }
    
    private fun setupHadoopEnvironment() {
        val osName = System.getProperty("os.name").lowercase()
        val isWindows = osName.contains("windows")
        
        if (isWindows) {
            val projectRoot = System.getProperty("user.dir")
            val hadoopHome = "$projectRoot/hadoop"
            
            System.setProperty("hadoop.home.dir", hadoopHome)
            System.setProperty("HADOOP_HOME", hadoopHome)
            
            val hadoopBin = "$hadoopHome/bin"
            System.setProperty("java.library.path", "$hadoopBin;${System.getProperty("java.library.path", "")}")
            
            logger.info("Configured Hadoop for Windows environment")
            logger.info("HADOOP_HOME: {}", hadoopHome)
        } else {
            logger.info("Running on Linux/Unix environment - using system Hadoop configuration")
        }
        
        System.setProperty("java.security.krb5.realm", "")
        System.setProperty("java.security.krb5.kdc", "")
    }
    
    private fun loadConfigurationFiles(configuration: Configuration) {
        val coreResource = this::class.java.classLoader.getResource("hdfs/core-site-sit.xml")
        val hdfsResource = this::class.java.classLoader.getResource("hdfs/hdfs-site-sit.xml")
        
        if (coreResource != null) {
            configuration.addResource(coreResource)
            logger.info("Loaded core-site-sit.xml from classpath")
        } else {
            logger.warn("core-site-sit.xml not found in classpath")
        }
        
        if (hdfsResource != null) {
            configuration.addResource(hdfsResource)
            logger.info("Loaded hdfs-site-sit.xml from classpath")
        } else {
            logger.warn("hdfs-site-sit.xml not found in classpath")
        }
    }
}