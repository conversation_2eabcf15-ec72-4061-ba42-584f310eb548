-- Enhanced lineage_job_processing_history table schema
-- Supports both Data Exchange jobs and HDFS shell script jobs
-- Simplified version without JSON metadata and minimal HDFS-specific fields

CREATE TABLE lineage_job_processing_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    job_key VARCHAR(255) NOT NULL COMMENT '作业唯一标识',
    job_type ENUM('DATA_EXCHANGE', 'HDFS_SHELL_SCRIPT') NOT NULL COMMENT '作业类型',
    
    -- Data Exchange specific fields (nullable for HDFS jobs)
    reader_job_id VARCHAR(255) NULL COMMENT '数据交换读取作业ID (仅用于DATA_EXCHANGE)',
    write_job_id VARCHAR(255) NULL COMMENT '数据交换写入作业ID (仅用于DATA_EXCHANGE)',
    
    -- Common fields for all job types
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processing_result ENUM('NO_CHANGE', 'UPDATED', 'FAILED') NOT NULL,
    changes_detected BOOLEAN DEFAULT FALSE,
    processing_duration_ms BIGINT,
    lineage_hash VARCHAR(64) COMMENT '血缘内容哈希',
    error_message TEXT,
    
    INDEX idx_job_key (job_key),
    INDEX idx_job_type (job_type),
    INDEX idx_processed_at (processed_at),
    INDEX idx_lineage_hash (lineage_hash),
    INDEX idx_job_type_processed_at (job_type, processed_at)
) COMMENT='作业处理历史记录 - 支持数据交换和HDFS Shell脚本作业';

-- Add constraints to ensure proper field usage based on job type
ALTER TABLE lineage_job_processing_history 
ADD CONSTRAINT chk_data_exchange_fields 
CHECK (
    (job_type = 'DATA_EXCHANGE' AND reader_job_id IS NOT NULL AND write_job_id IS NOT NULL) OR
    (job_type = 'HDFS_SHELL_SCRIPT' AND reader_job_id IS NULL AND write_job_id IS NULL)
);