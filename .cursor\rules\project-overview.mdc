---
description:
globs:
alwaysApply: false
---
# DGP 血缘收集器项目概览 (DGP Lineage Collector Project Overview)

## 项目简介 (Project Introduction)
这是一个基于 Spring Boot 和 Kotlin 的数据血缘收集器 (data lineage collector)，用于收集、解析和管理数据血缘关系 (data lineage relationships)。

## 核心架构 (Core Architecture)
采用数据为中心的编程模式 (data-oriented programming)，功能核心，命令式外壳 (functional core, imperative shell) 架构。

### 主要模块 (Main Modules)
1. **lineagetask** - 血缘任务管理 (lineage task management)
2. **lineagecatalog** - 血缘目录服务 (lineage catalog service)  
3. **parser** - SQL 解析器 (SQL parser)

## 主要入口点 (Main Entry Points)
- 应用主类: [App.kt](mdc:src/main/kotlin/com/datayes/App.kt)
- Maven 配置: [pom.xml](mdc:pom.xml)
- 应用配置: [application.properties](mdc:src/main/resources/application.properties)

## 技术栈 (Technology Stack)
- **框架**: Spring Boot 3.4.4
- **语言**: Kotlin 1.9.25
- **数据库**: MySQL 
- **构建工具**: Maven
- **SQL 解析**: JSQLParser
- **JSON**: Jackson with Kotlin module

## 编译运行 (Build & Run)
```bash
# 编译项目 (compile project)
mvn clean compile

# 运行应用 (run application)
mvn spring-boot:run

# 运行测试 (run tests)
mvn test
```
