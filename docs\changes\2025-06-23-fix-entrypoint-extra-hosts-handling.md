# Fix entrypoint.sh EXTRA_HOSTS handling

## What

Replaced the `echo -e` command in `src/main/docker/entrypoint.sh` with `printf '%b\n'` when appending the contents of the `EXTRA_HOSTS` environment variable to `/etc/hosts`.

## Why

Using `echo -e` is **non-portable** across different shells (e.g., BusyBox `sh` inside Alpine). On shells that do **not** recognise the `-e` flag, the literal text `-e` is written to `/etc/hosts`, resulting in lines such as:

```
-e *********** tbds-10-9-112-26
```

This breaks host name resolution inside the container.

## How

1. Replaced the comment to reflect the new approach.
2. Substituted
   ```sh
   echo -e "$EXTRA_HOSTS" >> /etc/hosts
   ```
   with the portable alternative
   ```sh
   printf '%b\n' "$EXTRA_HOSTS" >> /etc/hosts
   ```
   `printf` with the `%b` format specifier safely interprets `\n` escape sequences while remaining POSIX-compliant.

## Follow-up Tasks

- None at the moment. Monitor future container images to ensure `/etc/hosts` remains correctly formatted. 