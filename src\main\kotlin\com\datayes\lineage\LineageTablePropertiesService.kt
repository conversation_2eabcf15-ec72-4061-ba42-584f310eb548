package com.datayes.lineage

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

/**
 * 血缘表属性业务逻辑服务 (Lineage Table Properties Business Logic Service)
 * 
 * 提供血缘表属性的CRUD操作和业务逻辑处理，直接操作lineage_tables表
 */
@Service
class LineageTablePropertiesService(
    private val propertiesRepository: LineageTablePropertiesRepository
) {
    
    companion object {
        private val log = LoggerFactory.getLogger(LineageTablePropertiesService::class.java)
    }

    /**
     * 根据血缘表ID查询表信息和属性
     * 
     * @param tableId 血缘表ID
     * @return 血缘表信息，如果不存在则返回null
     */
    fun findTableInfoById(tableId: Long): LineageTableInfo? {
        log.debug("a1b2c3d4 | Querying table info for table ID: {}", tableId)
        
        val tableInfo = propertiesRepository.findById(tableId)
        
        if (tableInfo != null) {
            log.debug("e5f6g7h8 | Found table info for ID {}: tableName={}, syncFrequency={}, requirementId={}", 
                tableId, tableInfo.tableName, tableInfo.syncFrequency, tableInfo.requirementId)
        } else {
            log.debug("i9j0k1l2 | No table found for ID: {}", tableId)
        }
        
        return tableInfo
    }

    /**
     * 根据查询条件搜索血缘表信息
     * 
     * @param request 查询请求参数
     * @return 匹配的血缘表信息列表
     */
    fun queryTableInfo(request: QueryLineageTablePropertiesRequest): List<LineageTableInfo> {
        log.debug("m3n4o5p6 | Querying table info with request: {}", request)
        
        val results = propertiesRepository.findByQuery(request)
        
        log.debug("q7r8s9t0 | Query returned {} table records", results.size)
        
        return results
    }

    /**
     * 更新血缘表属性信息
     * 
     * @param tableId 血缘表ID
     * @param command 更新命令
     * @return 更新后的表信息
     * @throws IllegalArgumentException 如果血缘表ID不存在
     */
    @Transactional
    fun updateTableProperties(tableId: Long, command: UpdateLineageTablePropertiesCommand): LineageTableInfo {
        log.info("u1v2w3x4 | Updating properties for table ID {}: {}", tableId, command)
        
        // 验证血缘表是否存在
        if (!propertiesRepository.existsTable(tableId)) {
            log.error("y5z6a7b8 | Table with ID {} does not exist", tableId)
            throw IllegalArgumentException("Table with ID $tableId does not exist")
        }
        
        // 验证输入参数
        validateUpdateCommand(command)
        
        // 执行更新操作
        val success = propertiesRepository.updateProperties(tableId, command)
        
        if (!success) {
            log.error("c9d0e1f2 | Failed to update properties for table ID {}", tableId)
            throw RuntimeException("Failed to update properties for table ID $tableId")
        }
        
        log.info("g3h4i5j6 | Successfully updated properties for table ID {}", tableId)
        
        // 返回更新后的表信息
        return propertiesRepository.findById(tableId)
            ?: throw RuntimeException("Failed to retrieve updated table info")
    }

    /**
     * 清空血缘表的属性信息（将属性字段设为null）
     * 
     * @param tableId 血缘表ID
     * @return 清空成功返回true，表不存在返回false
     */
    @Transactional
    fun clearTableProperties(tableId: Long): Boolean {
        log.info("k7l8m9n0 | Clearing properties for table ID {}", tableId)
        
        // 验证血缘表是否存在
        if (!propertiesRepository.existsTable(tableId)) {
            log.warn("s5t6u7v8 | Table with ID {} does not exist", tableId)
            return false
        }
        
        // 清空所有属性字段
        val clearCommand = UpdateLineageTablePropertiesCommand(
            syncFrequency = null,
            dataSyncScope = null,
            requirementId = null
        )
        
        val success = propertiesRepository.updateProperties(tableId, clearCommand)
        
        if (success) {
            log.info("o1p2q3r4 | Successfully cleared properties for table ID {}", tableId)
        } else {
            log.error("w9x0y1z2 | Failed to clear properties for table ID {}", tableId)
        }
        
        return success
    }

    /**
     * 获取血缘表属性统计信息
     * 
     * @return 统计信息映射
     */
    fun getStatistics(): Map<String, Any> {
        log.debug("m5n6o7p8 | Retrieving lineage table properties statistics")
        
        val statistics = propertiesRepository.getStatistics()
        
        log.debug("q9r0s1t2 | Retrieved statistics: total records = {}", statistics["totalRecords"])
        
        return statistics
    }

    /**
     * 验证更新命令的有效性
     * 
     * @param command 更新命令
     * @throws IllegalArgumentException 如果参数无效
     */
    private fun validateUpdateCommand(command: UpdateLineageTablePropertiesCommand) {
        // 验证同步频率格式（如果有值）
        command.syncFrequency?.let { freq ->
            // if (freq.isBlank()) {
            //     throw IllegalArgumentException("Sync frequency cannot be blank")
            // }
            if (freq.length > 50) {
                throw IllegalArgumentException("Sync frequency cannot exceed 50 characters")
            }
        }
        
        // 验证需求编号格式（如果有值）
        command.requirementId?.let { reqId ->
            // if (reqId.isBlank()) {
            //     throw IllegalArgumentException("Requirement ID cannot be blank")
            // }
            if (reqId.length > 100) {
                throw IllegalArgumentException("Requirement ID cannot exceed 100 characters")
            }
        }
        
        // 验证数据同步范围（如果有值）
        command.dataSyncScope?.let { scope ->
            // if (scope.isBlank()) {
            //     throw IllegalArgumentException("Data sync scope cannot be blank")
            // }
        }
        
        // 至少要有一个非空字段
        // if (command.syncFrequency == null &&
        //     command.dataSyncScope == null &&
        //     command.requirementId == null) {
        //     throw IllegalArgumentException("At least one property field must be provided")
        // }
    }
}