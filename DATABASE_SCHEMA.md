# Database Schema Documentation

## DGP Lineage Collector - Database Tables

This document contains the current database schema for tables used in the DGP Lineage Collector project.

---

## Core Lineage Tables

### 1. lineage_systems
**Purpose**: System management and configuration
**Row Count**: 4

| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | bigint(20) | NO | PRI | | auto_increment |
| system_name | varchar(100) | NO | UNI | | |
| system_code | varchar(50) | NO | UNI | | |
| description | text | YES | | | |
| contact_person | varchar(100) | YES | | | |
| status | enum('ACTIVE','INACTIVE') | NO | MUL | ACTIVE | |
| cron_expression | varchar(100) | YES | | | |
| created_at | timestamp | NO | | CURRENT_TIMESTAMP | |
| updated_at | timestamp | NO | | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |
| latest_collection_result | enum('SUCCESS','COLLECTING','FAILED') | YES | | | |

### 2. lineage_datasources
**Purpose**: Data source definitions and connection information
**Row Count**: 14

| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | bigint(20) | NO | PRI | | auto_increment |
| datasource_name | varchar(100) | NO | | | |
| db_type | varchar(20) | NO | MUL | | |
| host | varchar(255) | NO | | | |
| port | int(11) | NO | | | |
| database_name | varchar(100) | NO | | | |
| connection_string | text | NO | | | |
| system_id | bigint(20) | YES | MUL | | |
| status | enum('ACTIVE','INACTIVE') | NO | MUL | ACTIVE | |
| created_at | timestamp | NO | | CURRENT_TIMESTAMP | |
| updated_at | timestamp | NO | | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |

### 3. lineage_tables
**Purpose**: Table metadata and information
**Row Count**: 149

| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | bigint(20) | NO | PRI | | auto_increment |
| datasource_id | bigint(20) | NO | MUL | | |
| schema_name | varchar(100) | YES | MUL | | |
| table_name | varchar(100) | NO | MUL | | |
| table_type | enum('TABLE','VIEW','TEMP_TABLE','EXTERNAL_TABLE') | YES | | TABLE | |
| chinese_name | varchar(200) | YES | MUL | | |
| description | text | YES | | | |
| sync_frequency | varchar(50) | YES | | | |
| requirement_id | varchar(100) | YES | | | |
| status | enum('ACTIVE','INACTIVE') | NO | MUL | ACTIVE | |
| created_at | timestamp | NO | | CURRENT_TIMESTAMP | |
| updated_at | timestamp | NO | | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |
| reference_count | int(11) | YES | | 0 | |
| last_referenced_at | timestamp | NO | | CURRENT_TIMESTAMP | |

### 4. lineage_columns
**Purpose**: Column-level metadata and lineage information
**Row Count**: 2,843

| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | bigint(20) | NO | PRI | | auto_increment |
| table_id | bigint(20) | NO | MUL | | |
| column_name | varchar(100) | NO | MUL | | |
| data_type | varchar(50) | NO | MUL | | |
| column_comment | text | YES | | | |
| is_primary_key | tinyint(1) | YES | | 0 | |
| is_nullable | tinyint(1) | YES | | 1 | |
| default_value | varchar(500) | YES | | | |
| column_order | int(11) | YES | | | |
| status | enum('ACTIVE','INACTIVE') | NO | MUL | ACTIVE | |
| created_at | timestamp | NO | | CURRENT_TIMESTAMP | |
| updated_at | timestamp | NO | | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |
| reference_count | int(11) | YES | | 0 | |
| last_referenced_at | timestamp | NO | | CURRENT_TIMESTAMP | |

---

## Task Management Tables

### 5. lineage_job_processing_history
**Purpose**: Job processing history and results tracking
**Row Count**: 38

| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | bigint(20) | NO | PRI | | auto_increment |
| job_key | varchar(255) | NO | MUL | | |
| reader_job_id | varchar(255) | YES | | | |
| write_job_id | varchar(255) | YES | | | |
| processed_at | timestamp | NO | MUL | CURRENT_TIMESTAMP | |
| processing_result | enum('NO_CHANGE','UPDATED','FAILED') | NO | | | |
| changes_detected | tinyint(1) | YES | | 0 | |
| processing_duration_ms | bigint(20) | YES | | | |
| lineage_hash | varchar(64) | YES | MUL | | |
| error_message | text | YES | | | |
| job_type | enum('DATA_EXCHANGE','HDFS_SHELL_SCRIPT') | YES | | | |

### 6. lineage_execution_logs
**Purpose**: Detailed execution logging for tasks
**Row Count**: 0

| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | bigint(20) | NO | PRI | | auto_increment |
| task_id | bigint(20) | NO | MUL | | |
| execution_id | varchar(100) | NO | MUL | | |
| log_level | enum('DEBUG','INFO','WARN','ERROR') | NO | MUL | | |
| log_message | text | NO | | | |
| exception_stack | text | YES | | | |
| execution_step | varchar(100) | YES | MUL | | |
| processing_time_ms | bigint(20) | YES | | | |
| created_at | timestamp | NO | MUL | CURRENT_TIMESTAMP | |

---

## Statistics and Monitoring Tables

### 7. lineage_statistics
**Purpose**: System statistics and health monitoring
**Row Count**: 0

| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | bigint(20) | NO | PRI | | auto_increment |
| system_id | bigint(20) | YES | MUL | | |
| datasource_id | bigint(20) | YES | | | |
| table_count | int(11) | YES | | 0 | |
| column_count | int(11) | YES | | 0 | |
| relationship_count | int(11) | YES | | 0 | |
| last_collection_time | timestamp | NO | | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |
| health_status | enum('HEALTHY','WARNING','ERROR') | YES | MUL | HEALTHY | |
| statistics_date | date | NO | MUL | | |
| created_at | timestamp | NO | | CURRENT_TIMESTAMP | |
| updated_at | timestamp | NO | | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |

---

## Missing Tables (Connection Issues)

The following tables are referenced in the codebase but could not be queried due to database connection timeouts:

- **lineage_tasks**: Task management and scheduling (referenced in LineageTask.kt)
- **lineage_relationships**: Core blood lineage relationships (referenced in LineageRepository.kt)

---

## Table Relationships

### Primary Relationships:
- `lineage_systems` → `lineage_datasources` (system_id)
- `lineage_datasources` → `lineage_tables` (datasource_id) 
- `lineage_tables` → `lineage_columns` (table_id)
- `lineage_systems` → `lineage_statistics` (system_id)
- `lineage_execution_logs` → `lineage_tasks` (task_id) *[table not accessible]*

### Key Features:
- **Audit Trail**: All tables include `created_at` and `updated_at` timestamps
- **Status Management**: Most tables include ACTIVE/INACTIVE status enumeration
- **Reference Tracking**: Tables and columns track usage with `reference_count` and `last_referenced_at`
- **Multilingual Support**: Tables support both English names and Chinese names (`chinese_name`)

---

## Database Configuration

- **Host**: ***********:3306
- **Database**: dgp
- **Character Set**: utf8
- **Timezone**: Asia/Shanghai

*Generated on: 2025-06-16*
*Tool Used: db-query-tool.py*