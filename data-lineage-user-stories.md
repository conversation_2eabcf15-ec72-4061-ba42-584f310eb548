### **Aligned User Stories based on Domain Concepts**

#### **1. Core View & Navigation**

* **UC-01: System Catalog Navigation**
  As a Data Governance Manager, I want to view all registered **`LineageSystem`** entities, filterable by **`SystemStatus`** (e.g., `ACTIVE`, `INACTIVE`), and searchable by name, so that I can quickly locate and manage a specific lineage source.

* **UC-02 & UC-03: Viewing System Contents (Data Sources & Lineage)**
  As a user, after selecting a **`LineageSystem`**, I want to view its associated **`MetadataDataSourceDto`** records (enriched from external metadata) and the resulting **`TableLineageView`**, so I can analyze both the physical data sources and the logical data flows for that system.

* **UC-04: Quick Navigation from Data Source to Tables**
  As a Developer, when I click on a **`MetadataDataSourceDto`** entry, I want the UI to filter the **`TableLineageView`** to only show **`TableInfo`** records matching that data source's **`DatabaseInfo`**, so that I can quickly isolate tables from a specific database.

#### **2. Task & Collection Management**

* **UC-05: Filtering**
  As a Data Governance Manager, I want to filter the list of lineage tasks based on properties within the **`LineageTask`** model, such as the **`TaskType`** (`DATA_EXCHANGE_PLATFORM`, `BASH_SCRIPT`, `MANUAL_IMPORT`) and the latest **`TaskStatus`** (`SUCCESS`, `FAILED`, `RUNNING`).

* **UC-06: Manual Trigger of Lineage Collection**
  As a Data Governance Manager, I want to initiate a **`SystemLineageCollectionRequest`** with a **`ScheduleType`** of `MANUAL`, which will create and execute a **`LineageTask`**, so I can refresh lineage on-demand and review the outcome in the **`JobProcessingHistory`**.

* **UC-07: View Collection Logs**
  As a Data Governance Manager, I want to view the **`JobProcessingHistory`** for a **`LineageTask`**, and in case of failure, inspect the **`FailedTaskInfo`** record to get detailed error messages for diagnostics.

* **UC-08: Manual Lineage Import**
  As a Data Governance Manager, I want to upload a file to trigger a **`LineageTask`** with a **`TaskType`** of `MANUAL_IMPORT`, where the **`SourceType`** is `MANUAL_INPUT`, so that I can populate **`DataLineage`** records for systems that cannot be parsed automatically.

#### **3. Lineage Graph & Visualization**

* **UC-09 & UC-10: View Table-Level Lineage Graph and Details**
  As a Developer, I want to render a graph based on the **`TableLineageView`**. Clicking a node representing a **`TableInfo`** object should display its associated metadata, including its **`DatabaseInfo`** and the `confidence score` from the **`TableLineageDto`**.

* **UC-11: View Field-Level Lineage Graph**
  As a Developer, I want to switch the visualization to render the **`ColumnLineageView`**. The links between **`ColumnInfo`** nodes must visually represent the **`LineageType`** (e.g., `DIRECT_COPY`, `SQL_QUERY`) and details from the **`DataTransformation`** (e.g., `FUNCTION`, `EXPRESSION`), with styling based on the originating task's **`SourceType`** (`SCRIPT_ANALYSIS` vs. `MANUAL_INPUT`).

* **UC-12 & UC-13: Graph Interaction (Search & Grouping)**
  As a user interacting with the lineage graph, I want to search for nodes by their **`TableInfo`** name and visually group nodes that belong to the same **`LineageSystem`** to better understand the data flow architecture.

#### **4. Manual Lineage Maintenance**

* **UC-14, UC-15, UC-16: Add, Edit, and Delete Manual Lineage**
  As a Developer, I need a UI to create, update, or delete **`TableLineage`** and **`ColumnLineage`** records where the associated **`LineageTask`** has a **`SourceType`** of `MANUAL_INPUT`. The system must prevent editing of lineage records generated from other source types like `SCRIPT_ANALYSIS`. New manual mappings will be stored as **`ColumnMappingDto`** with a high confidence score.