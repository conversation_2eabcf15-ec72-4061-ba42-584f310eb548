package com.datayes

import com.datayes.dataexchange.DataExchangeJobLineageConverter
import com.datayes.dataexchange.DataExchangeColumnMapping
import com.datayes.dataexchange.DataExchangeJob
import com.datayes.lineage.LineageType.FILTER
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*

class LineageConverterTest {

    @Test
    fun `should parse single source table from simple SQL`() {
        // 准备测试数据 (test data preparation)
        val dataExchangeJob = DataExchangeJob(
            readerJobId = "job1",
            readerJobName = "Reader Job",
            dbReader = "*************************************",
            readerTableName = "users",
            readerSql = "SELECT id, name, email FROM users WHERE active = 1",
            writeJobId = "job2",
            writeJobName = "Writer Job",
            dbWriter = "*************************************",
            writerTableName = "active_users",
            columns = listOf(
                DataExchangeColumnMapping(0, "用户ID", "user_id", "bigint", "id", "int"),
                DataExchangeColumnMapping(1, "用户名", "username", "varchar(100)", "name", "varchar(50)"),
                DataExchangeColumnMapping(2, "邮箱", "email", "varchar(255)", "email", "varchar(255)")
            )
        )

        // 执行转换 (perform conversion)
        val result = DataExchangeJobLineageConverter.convertToLineage(dataExchangeJob)

        // 验证结果 (verify results)
        assertTrue(result.success, "转换应该成功")
        assertNotNull(result.lineage, "应该生成血缘信息")
        
        val lineage = result.lineage!!
        
        // 验证表级血缘关系 (verify table-level lineage)
        assertEquals(1, lineage.tableLineage.sourceTables.size, "应该有一个源表")
        assertEquals("users", lineage.tableLineage.sourceTables[0].tableName, "源表名应该是 users")
        assertEquals("active_users", lineage.tableLineage.targetTable.tableName, "目标表名应该是 active_users")
        assertEquals(FILTER, lineage.tableLineage.lineageType, "血缘类型应该是过滤")
        
        // 验证列级血缘关系 (verify column-level lineage)
        assertEquals(3, lineage.columnLineages.size, "应该有3个列映射")
        
        val firstColumn = lineage.columnLineages[0]
        assertEquals("id", firstColumn.sourceColumn.columnName, "第一个源列应该是 id")
        assertEquals("user_id", firstColumn.targetColumn.columnName, "第一个目标列应该是 user_id")
        assertEquals("users", firstColumn.sourceColumn.table.tableName, "源列应该属于 users 表")
    }

    @Test
    fun `should parse multiple source tables from JOIN SQL`() {
        // 准备测试数据 (test data preparation)
        val dataExchangeJob = DataExchangeJob(
            readerJobId = "job1",
            readerJobName = "Reader Job",
            dbReader = "*************************************",
            readerTableName = "users", // 这个会被忽略，因为我们从SQL解析
            readerSql = """
                SELECT u.id, u.name, p.title, d.department_name 
                FROM users u 
                JOIN profiles p ON u.id = p.user_id 
                JOIN departments d ON p.department_id = d.id
                WHERE u.active = 1
            """.trimIndent(),
            writeJobId = "job2",
            writeJobName = "Writer Job",
            dbWriter = "*************************************",
            writerTableName = "user_details",
            columns = listOf(
                DataExchangeColumnMapping(0, "用户ID", "user_id", "bigint", "id", "int"),
                DataExchangeColumnMapping(1, "用户名", "username", "varchar(100)", "name", "varchar(50)"),
                DataExchangeColumnMapping(2, "职位", "position", "varchar(100)", "title", "varchar(100)"),
                DataExchangeColumnMapping(3, "部门", "department", "varchar(100)", "department_name", "varchar(100)")
            )
        )

        // 执行转换 (perform conversion)
        val result = DataExchangeJobLineageConverter.convertToLineage(dataExchangeJob)

        // 验证结果 (verify results)
        assertTrue(result.success, "转换应该成功")
        assertNotNull(result.lineage, "应该生成血缘信息")
        
        val lineage = result.lineage!!
        
        // 验证表级血缘关系 (verify table-level lineage)
        assertEquals(3, lineage.tableLineage.sourceTables.size, "应该有三个源表")
        
        val sourceTableNames = lineage.tableLineage.sourceTables.map { it.tableName }.toSet()
        assertTrue(sourceTableNames.contains("users"), "应该包含 users 表")
        assertTrue(sourceTableNames.contains("profiles"), "应该包含 profiles 表")
        assertTrue(sourceTableNames.contains("departments"), "应该包含 departments 表")
        
        assertEquals("user_details", lineage.tableLineage.targetTable.tableName, "目标表名应该是 user_details")
        assertEquals(com.datayes.lineage.LineageType.JOIN, lineage.tableLineage.lineageType, "血缘类型应该是连接")
    }

    @Test
    fun `should fallback to config table name when SQL parsing fails`() {
        // 准备测试数据，使用无效的SQL (test data with invalid SQL)
        val dataExchangeJob = DataExchangeJob(
            readerJobId = "job1",
            readerJobName = "Reader Job",
            dbReader = "*************************************",
            readerTableName = "fallback_table",
            readerSql = "INVALID SQL STATEMENT",
            writeJobId = "job2",
            writeJobName = "Writer Job",
            dbWriter = "*************************************",
            writerTableName = "target_table",
            columns = listOf(
                DataExchangeColumnMapping(0, "列", "col1", "varchar(100)", "col1", "varchar(100)")
            )
        )

        // 执行转换 (perform conversion)
        val result = DataExchangeJobLineageConverter.convertToLineage(dataExchangeJob)

        // 验证结果 (verify results)
        assertTrue(result.success, "转换应该成功")
        assertNotNull(result.lineage, "应该生成血缘信息")
        assertTrue(result.warnings.isNotEmpty(), "应该有警告信息")
        
        val lineage = result.lineage!!
        
        // 验证降级方案 (verify fallback behavior)
        assertEquals(1, lineage.tableLineage.sourceTables.size, "应该有一个源表")
        assertEquals("fallback_table", lineage.tableLineage.sourceTables[0].tableName, "应该使用配置中的表名")
        
        // 验证警告信息 (verify warning messages)
        val hasParsingWarning = result.warnings.any { it.contains("SQL解析失败") }
        val hasFallbackWarning = result.warnings.any { it.contains("使用配置中的表名作为降级方案") }
        assertTrue(hasParsingWarning, "应该有SQL解析失败的警告")
        assertTrue(hasFallbackWarning, "应该有降级方案的警告")
    }
} 