package com.datayes.metadata

import com.datayes.ApiResponse
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter

/**
 * 元数据控制器 (Metadata Controller)
 * 
 * 提供元数据数据源和系统信息的 REST API
 */
@RestController
@CrossOrigin(origins = ["*"])
@RequestMapping("/api/v1/metadata")
@Tag(name = "Metadata", description = "元数据相关接口")
class MetadataController(private val metadataService: MetadataService) {
    
    private val logger = LoggerFactory.getLogger(MetadataController::class.java)
    
    /**
     * 根据血缘数据源ID查找匹配的元数据数据源
     * 
     * @param lineageDatasourceId 血缘数据源ID
     * @return 匹配的元数据数据源列表
     */
    @Operation(summary = "根据血缘数据源ID匹配元数据数据源", description = "根据血缘数据源ID查找并返回匹配的元数据数据源列表")
    @GetMapping("/datasources/match/{lineageDatasourceId}")
    fun findMatchedMetadataDataSources(
        @Parameter(description = "血缘数据源ID", example = "1")
        @PathVariable lineageDatasourceId: Long
    ): ResponseEntity<ApiResponse<MatchedMetadataResponse>> {
        return try {
            logger.info("2b4d6f8a | 接收到匹配元数据数据源请求: lineageDatasourceId=$lineageDatasourceId")
            
            val result = metadataService.findMatchedMetadataDataSources(lineageDatasourceId)
            
            logger.info("5c7e9f1b | 匹配完成: lineageDatasourceId=$lineageDatasourceId, 匹配数量=${result.totalCount}")
            
            ResponseEntity.ok(
                ApiResponse.success(
                    data = result,
                    message = "匹配成功"
                )
            )
            
        } catch (e: IllegalArgumentException) {
            logger.warn("8d0f2e4c | 请求参数错误: lineageDatasourceId=$lineageDatasourceId", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("请求参数错误: ${e.message}"))
                
        } catch (e: Exception) {
            logger.error("1a3b5c7d | 查找匹配元数据数据源时发生错误: lineageDatasourceId=$lineageDatasourceId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("查询失败: ${e.message}"))
        }
    }
    
    /**
     * 获取所有有效的元数据数据源
     * 
     * @return 所有有效的元数据数据源列表
     */
    @Operation(summary = "获取所有有效的元数据数据源", description = "返回所有状态为ACTIVE的元数据数据源列表")
    @GetMapping("/datasources")
    fun getAllActiveMetadataDataSources(): ResponseEntity<ApiResponse<List<MetadataDataSourceDto>>> {
        return try {
            logger.info("4e6g8i0j | 接收到获取所有元数据数据源请求")
            
            val result = metadataService.getAllActiveMetadataDataSources()
            
            logger.info("7f9h1k3l | 获取元数据数据源完成: 数量=${result.size}")
            
            ResponseEntity.ok(
                ApiResponse.success(
                    data = result,
                    message = "查询成功"
                )
            )
            
        } catch (e: Exception) {
            logger.error("0m2n4p6q | 获取所有元数据数据源时发生错误", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("查询失败: ${e.message}"))
        }
    }
    
    /**
     * 根据系统ID获取元数据数据源
     * 
     * @param systemId 系统ID
     * @return 该系统下的元数据数据源列表
     */
    @Operation(summary = "根据系统ID获取元数据数据源", description = "返回指定系统下所有有效的元数据数据源")
    @GetMapping("/datasources/system/{systemId}")
    fun getMetadataDataSourcesBySystemId(
        @Parameter(description = "系统ID", example = "100")
        @PathVariable systemId: Long
    ): ResponseEntity<ApiResponse<List<MetadataDataSourceDto>>> {
        return try {
            logger.info("3r5s7t9u | 接收到根据系统ID获取元数据数据源请求: systemId=$systemId")
            
            val result = metadataService.getMetadataDataSourcesBySystemId(systemId)
            
            logger.info("6v8w0x2y | 根据系统ID获取元数据数据源完成: systemId=$systemId, 数量=${result.size}")
            
            ResponseEntity.ok(
                ApiResponse.success(
                    data = result,
                    message = "查询成功"
                )
            )
            
        } catch (e: Exception) {
            logger.error("9z1a3b5c | 根据系统ID获取元数据数据源时发生错误: systemId=$systemId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("查询失败: ${e.message}"))
        }
    }
    
    /**
     * 获取所有有效的系统信息
     * 
     * @return 所有有效的系统信息列表
     */
    @Operation(summary = "获取所有有效的系统信息", description = "返回所有有效的系统信息列表")
    @GetMapping("/systems")
    fun getAllActiveSystemInfo(): ResponseEntity<ApiResponse<List<MetadataSystemInfoDto>>> {
        return try {
            logger.info("2d4f6h8j | 接收到获取所有系统信息请求")
            
            val result = metadataService.getAllActiveSystemInfo()
            
            logger.info("5k7m9o1q | 获取系统信息完成: 数量=${result.size}")
            
            ResponseEntity.ok(
                ApiResponse.success(
                    data = result,
                    message = "查询成功"
                )
            )
            
        } catch (e: Exception) {
            logger.error("8r0t2v4x | 获取所有系统信息时发生错误", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("查询失败: ${e.message}"))
        }
    }
    
    /**
     * 查询元数据数据源，支持分页和过滤
     * 
     * @param systemId 系统ID，可选，为null时查询所有数据源
     * @param host 主机地址模糊匹配，可选
     * @param databaseName 数据库名称模糊匹配，可选  
     * @param schema 模式名称模糊匹配（从source_name提取），可选
     * @param page 页码，从1开始，默认为1
     * @param size 每页大小，默认为20
     * @return 分页后的元数据数据源信息
     */
    @Operation(summary = "查询元数据数据源", description = "支持分页和过滤的元数据数据源查询接口")
    @GetMapping("/datasources/query")
    fun queryMetadataDataSources(
        @Parameter(description = "系统ID，为null时查询所有数据源", example = "1")
        @RequestParam(required = false) systemId: Long?,
        @Parameter(description = "主机地址模糊匹配", example = "10.0.12")
        @RequestParam(required = false) host: String?,
        @Parameter(description = "数据库名称模糊匹配", example = "test")
        @RequestParam(required = false) databaseName: String?,
        @Parameter(description = "模式名称模糊匹配（从source_name提取）", example = "dwd")
        @RequestParam(required = false) schema: String?,
        @Parameter(description = "页码，从1开始", example = "1")
        @RequestParam(defaultValue = "1") page: Int,
        @Parameter(description = "每页大小", example = "20")
        @RequestParam(defaultValue = "20") size: Int
    ): ResponseEntity<ApiResponse<PagedMetadataDataSourcesDto>> {
        return try {
            logger.info("c4d5e6f7 | 接收到元数据数据源查询请求: systemId=$systemId, host=$host, databaseName=$databaseName, schema=$schema, page=$page, size=$size")
            
            // 验证分页参数
            val validatedPage = maxOf(1, page)
            val validatedSize = minOf(maxOf(1, size), 100) // 限制最大页面大小为100
            
            val result = metadataService.queryMetadataDataSourcesWithPagination(
                systemId = systemId,
                host = host,
                databaseName = databaseName,
                schema = schema,
                page = validatedPage,
                size = validatedSize
            )
            
            logger.info("g8h9i0j1 | 元数据数据源查询完成: 返回${result.numberOfElements}条记录，总数${result.totalElements}")
            
            ResponseEntity.ok(
                ApiResponse.success(
                    data = result,
                    message = "查询成功"
                )
            )
            
        } catch (e: IllegalArgumentException) {
            logger.warn("k2l3m4n5 | 请求参数错误: systemId=$systemId, host=$host, databaseName=$databaseName, schema=$schema", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("请求参数错误: ${e.message}"))
                
        } catch (e: Exception) {
            logger.error("o6p7q8r9 | 查询元数据数据源时发生错误: systemId=$systemId, host=$host, databaseName=$databaseName, schema=$schema", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("查询失败: ${e.message}"))
        }
    }

    /**
     * 查询血缘数据源，支持分页和过滤，并匹配元数据数据源
     *
     * @param systemId 系统ID，可选，通过匹配的元数据数据源的系统ID过滤，为null时查询所有数据源
     * @param host 主机地址模糊匹配，可选
     * @param databaseName 数据库名称模糊匹配，可选
     * @param datasourceName 数据源名称模糊匹配，可选
     * @param page 页码，从1开始，默认为1
     * @param size 每页大小，默认为20
     * @return 分页后的血缘数据源信息，包含匹配的元数据数据源
     */
    @Operation(summary = "查询血缘数据源", description = "支持分页和过滤的血缘数据源查询接口，每个血缘数据源会尝试匹配对应的元数据数据源。systemId通过匹配的元数据数据源进行过滤。")
    @GetMapping("/lineage-datasources/query")
    fun queryLineageDatasources(
        @Parameter(description = "系统ID，通过匹配的元数据数据源的系统ID过滤，为null时查询所有数据源", example = "1")
        @RequestParam(required = false) systemId: Long?,
        @Parameter(description = "主机地址模糊匹配", example = "10.0.12")
        @RequestParam(required = false) host: String?,
        @Parameter(description = "数据库名称模糊匹配", example = "test")
        @RequestParam(required = false) databaseName: String?,
        @Parameter(description = "数据源名称模糊匹配", example = "mysql")
        @RequestParam(required = false) datasourceName: String?,
        @Parameter(description = "页码，从1开始", example = "1")
        @RequestParam(defaultValue = "1") page: Int,
        @Parameter(description = "每页大小", example = "20")
        @RequestParam(defaultValue = "20") size: Int
    ): ResponseEntity<ApiResponse<PagedLineageDatasourcesDto>> {
        return try {
            logger.info("ld4d5e6f7 | 接收到血缘数据源查询请求: systemId=$systemId, host=$host, databaseName=$databaseName, datasourceName=$datasourceName, page=$page, size=$size")

            // 验证分页参数
            val validatedPage = maxOf(1, page)
            val validatedSize = minOf(maxOf(1, size), 100) // 限制最大页面大小为100

            val result = metadataService.queryLineageDatasourcesWithPagination(
                systemId = systemId,
                host = host,
                databaseName = databaseName,
                datasourceName = datasourceName,
                page = validatedPage,
                size = validatedSize
            )

            logger.info("lg8h9i0j1 | 血缘数据源查询完成: 返回${result.numberOfElements}条记录，总数${result.totalElements}")

            ResponseEntity.ok(
                ApiResponse.success(
                    data = result,
                    message = "查询成功"
                )
            )

        } catch (e: IllegalArgumentException) {
            logger.warn("lk2l3m4n5 | 请求参数错误: systemId=$systemId, host=$host, databaseName=$databaseName, datasourceName=$datasourceName", e)
            ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("请求参数错误: ${e.message}"))

        } catch (e: Exception) {
            logger.error("lo6p7q8r9 | 查询血缘数据源时发生错误: systemId=$systemId, host=$host, databaseName=$databaseName, datasourceName=$datasourceName", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("查询失败: ${e.message}"))
        }
    }

    /**
     * 获取系统间的血缘关系 (Get system-to-system lineage relationships)
     * 
     * 通过分析lineage_relationships中的表级关系，映射到系统级关系
     * 支持循环引用检测：当系统A->B且系统B->A时，标记为循环引用
     * 
     * @return 系统关系列表，包含源系统ID、目标系统ID和循环引用标识
     */
    @Operation(
        summary = "获取系统间血缘关系", 
        description = "分析表级血缘关系，构建系统级关系图。支持循环引用检测（直接双向引用）"
    )
    @GetMapping("/systems/relationships")
    fun getSystemRelationships(): ResponseEntity<ApiResponse<SystemRelationshipsResponseDto>> {
        return try {
            logger.info("s6t7u8v9 | 接收到系统间血缘关系查询请求")
            
            val result = metadataService.getSystemRelationships()
            
            logger.info("w0x1y2z3 | 系统间血缘关系查询完成: 总关系数=${result.totalRelationships}, 涉及系统数=${result.totalSystems}, 循环引用数=${result.cyclicReferencesCount}")
            
            ResponseEntity.ok(
                ApiResponse.success(
                    data = result,
                    message = "查询成功"
                )
            )
            
        } catch (e: Exception) {
            logger.error("a4b5c6d7 | 获取系统间血缘关系时发生错误", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("查询失败: ${e.message}"))
        }
    }

    /**
     * 获取参与血缘关系的系统信息 (Get systems that participate in lineage relationships)
     * 
     * 通过分析lineage_relationships表，找出所有参与血缘关系的系统
     * 返回系统的基本信息：ID、系统名称、系统简称
     * 
     * @return 参与血缘关系的系统信息列表
     */
    @Operation(
        summary = "获取参与血缘关系的系统", 
        description = "返回所有参与血缘关系的系统信息，包含系统ID、系统名称、系统简称"
    )
    @GetMapping("/systems/in-relationships")
    fun getSystemsInRelationships(): ResponseEntity<ApiResponse<List<MetadataSystemInfoDto>>> {
        return try {
            logger.info("f6g7h8i9 | 接收到获取参与血缘关系的系统信息请求")
            
            val result = metadataService.getSystemsInRelationships()
            
            logger.info("j0k1l2m3 | 获取参与血缘关系的系统信息完成: 系统数量=${result.size}")
            
            ResponseEntity.ok(
                ApiResponse.success(
                    data = result,
                    message = "查询成功"
                )
            )
            
        } catch (e: Exception) {
            logger.error("n4o5p6q7 | 获取参与血缘关系的系统信息时发生错误", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("查询失败: ${e.message}"))
        }
    }
}