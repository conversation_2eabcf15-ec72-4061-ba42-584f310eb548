package com.datayes.integration

import io.restassured.RestAssured.given
import io.restassured.http.ContentType
import io.restassured.path.json.JsonPath
import org.assertj.core.api.Assertions.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.*
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.*

/**
 * 手动触发血缘任务API高级集成测试 (Manual Lineage Trigger Task API Advanced Integration Tests)
 *
 * 测试更复杂的场景，包括边界条件、错误处理、并发场景等
 * 
 * 测试原则：
 * - 只进行只读操作，不删除或更新现有数据
 * - 测试边界条件和错误场景
 * - 验证API的健壮性和一致性
 * - 测试并发和性能相关场景
 */
@DisplayName("手动触发血缘任务API高级集成测试 - Manual Lineage Trigger Task API Advanced Integration Tests")
@TestMethodOrder(MethodOrderer.OrderAnnotation::class)
class ManualLineageTriggerTaskAdvancedIT : RestApiIntegrationTestBase() {

    companion object {
        private const val MANUAL_TASKS_BASE_PATH = "/v1/lineage/manual-tasks"
        private val dateTimeFormatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME
        private val testUsers = listOf("advanced-test-user-1", "advanced-test-user-2", "advanced-test-user-3")
        private val createdTaskUuids = mutableListOf<String>()
    }

    @BeforeAll
    fun setupAdvancedTests() {
        println("a1b2c3d4 | 开始高级集成测试设置")
        
        // 验证可用的数据源
        val datasourceId = ManualLineageTestDataUtils.getAvailableTestDatasourceId()
        assertThat(datasourceId)
            .`as`("至少需要一个可用的数据源ID进行高级测试")
            .isNotNull()
            
        println("e5f6g7h8 | 高级测试设置完成，使用数据源ID: $datasourceId")
    }

    @Test
    @Order(1)
    @DisplayName("应该处理并发触发请求 - Should handle concurrent trigger requests")
    fun `should handle concurrent trigger requests`() {
        println("i9j0k1l2 | 开始测试并发触发请求")

        val datasourceId = ManualLineageTestDataUtils.getAvailableTestDatasourceId()!!
        val concurrentRequests = 3

        // 并发创建多个任务
        (1..concurrentRequests).map { index ->
            val requestBody = ManualLineageTestDataUtils.createTriggerRequest(
                datasourceId = datasourceId,
                triggerUser = "${testUsers[0]}-concurrent-$index"
            )

            Thread {
                given()
                    .contentType(ContentType.JSON)
                    .body(requestBody)
                    .`when`()
                    .post("$MANUAL_TASKS_BASE_PATH/trigger")
                    .then()
                    .statusCode(200)
                    .extract()
                    .response()
            }.apply { start() }
        }

        // 等待所有请求完成
        Thread.sleep(2000)

        println("m3n4o5p6 | 并发触发请求测试完成")
    }

    @Test
    @Order(2)
    @DisplayName("应该验证分页边界条件 - Should validate pagination boundary conditions")
    fun `should validate pagination boundary conditions`() {
        println("q7r8s9t0 | 开始测试分页边界条件")

        // 测试页码为0
        given()
            .queryParam("page", 0)
            .queryParam("size", 1)
            .`when`()
            .get(MANUAL_TASKS_BASE_PATH)
            .then()
            .statusCode(200)
            .body("currentPage", equalTo(0))
            .body("size", equalTo(1))

        // 测试很大的页码
        given()
            .queryParam("page", 999999)
            .queryParam("size", 10)
            .`when`()
            .get(MANUAL_TASKS_BASE_PATH)
            .then()
            .statusCode(200)
            .body("currentPage", equalTo(999999))
            .body("tasks", hasSize<Any>(0))  // 应该返回空列表

        // 测试最小分页大小
        given()
            .queryParam("page", 0)
            .queryParam("size", 1)
            .`when`()
            .get(MANUAL_TASKS_BASE_PATH)
            .then()
            .statusCode(200)
            .body("size", equalTo(1))

        // 测试最大分页大小
        given()
            .queryParam("page", 0)
            .queryParam("size", 1000)
            .`when`()
            .get(MANUAL_TASKS_BASE_PATH)
            .then()
            .statusCode(200)
            .body("size", equalTo(1000))

        println("u1v2w3x4 | 分页边界条件测试完成")
    }

    @Test
    @Order(3)
    @DisplayName("应该处理无效的排序参数 - Should handle invalid sort parameters")
    fun `should handle invalid sort parameters`() {
        println("y5z6a7b8 | 开始测试无效的排序参数")

        // 测试无效的排序字段
        given()
            .queryParam("sortBy", "invalidField")
            .queryParam("sortDir", "asc")
            .`when`()
            .get(MANUAL_TASKS_BASE_PATH)
            .then()
            .statusCode(200)  // 应该使用默认排序而不是报错

        // 测试无效的排序方向
        given()
            .queryParam("sortBy", "createdAt")
            .queryParam("sortDir", "invalidDirection")
            .`when`()
            .get(MANUAL_TASKS_BASE_PATH)
            .then()
            .statusCode(200)  // 应该使用默认排序方向

        println("c9d0e1f2 | 无效排序参数测试完成")
    }

    @Test
    @Order(4)
    @DisplayName("应该验证日期范围查询 - Should validate date range queries")
    fun `should validate date range queries`() {
        println("g3h4i5j6 | 开始测试日期范围查询")

        val now = LocalDateTime.now()
        val futureDate = now.plusDays(1).format(dateTimeFormatter)
        val pastDate = now.minusDays(30).format(dateTimeFormatter)

        // 测试有效的日期范围
        given()
            .queryParam("startDate", pastDate)
            .queryParam("endDate", futureDate)
            .`when`()
            .get(MANUAL_TASKS_BASE_PATH)
            .then()
            .statusCode(200)

        // 测试反向日期范围（开始日期晚于结束日期）
        given()
            .queryParam("startDate", futureDate)
            .queryParam("endDate", pastDate)
            .`when`()
            .get(MANUAL_TASKS_BASE_PATH)
            .then()
            .statusCode(200)
            .body("tasks", hasSize<Any>(0))  // 应该返回空结果

        // 测试只有开始日期
        given()
            .queryParam("startDate", pastDate)
            .`when`()
            .get(MANUAL_TASKS_BASE_PATH)
            .then()
            .statusCode(200)

        // 测试只有结束日期
        given()
            .queryParam("endDate", futureDate)
            .`when`()
            .get(MANUAL_TASKS_BASE_PATH)
            .then()
            .statusCode(200)

        println("k7l8m9n0 | 日期范围查询测试完成")
    }

    @Test
    @Order(5)
    @DisplayName("应该处理极端数值参数 - Should handle extreme numeric parameters")
    fun `should handle extreme numeric parameters`() {
        println("o1p2q3r4 | 开始测试极端数值参数")

        // 测试极大的数据源ID
        val extremelyLargeDatasourceId = Long.MAX_VALUE

        given()
            .pathParam("datasourceId", extremelyLargeDatasourceId)
            .`when`()
            .get("$MANUAL_TASKS_BASE_PATH/datasource/{datasourceId}/statistics")
            .then()
            .statusCode(200)
            .body("datasourceId", equalTo(extremelyLargeDatasourceId))
            .body("totalTasks", equalTo(0))

        // 测试负数数据源ID
        given()
            .pathParam("datasourceId", -1)
            .`when`()
            .get("$MANUAL_TASKS_BASE_PATH/datasource/{datasourceId}/statistics")
            .then()
            .statusCode(200)

        // 测试零值数据源ID
        given()
            .pathParam("datasourceId", 0)
            .`when`()
            .get("$MANUAL_TASKS_BASE_PATH/datasource/{datasourceId}/statistics")
            .then()
            .statusCode(200)

        println("s5t6u7v8 | 极端数值参数测试完成")
    }

    @Test
    @Order(6)
    @DisplayName("应该验证字符串长度限制 - Should validate string length limits")
    fun `should validate string length limits`() {
        println("w9x0y1z2 | 开始测试字符串长度限制")

        val datasourceId = ManualLineageTestDataUtils.getAvailableTestDatasourceId()!!

        // 测试极长的用户名
        val veryLongUser = "a".repeat(500)
        val requestBody = mapOf(
            "datasourceId" to datasourceId,
            "triggerUser" to veryLongUser
        )

        given()
            .contentType(ContentType.JSON)
            .body(requestBody)
            .`when`()
            .post("$MANUAL_TASKS_BASE_PATH/trigger")
            .then()
            .statusCode(anyOf(equalTo(200), equalTo(400)))  // 可能成功或验证失败

        // 测试空字符串用户名
        val emptyUserRequestBody = mapOf(
            "datasourceId" to datasourceId,
            "triggerUser" to ""
        )

        given()
            .contentType(ContentType.JSON)
            .body(emptyUserRequestBody)
            .`when`()
            .post("$MANUAL_TASKS_BASE_PATH/trigger")
            .then()
            .statusCode(anyOf(equalTo(200), equalTo(400)))

        println("a3b4c5d6 | 字符串长度限制测试完成")
    }

    @Test
    @Order(7)
    @DisplayName("应该处理特殊字符输入 - Should handle special character input")
    fun `should handle special character input`() {
        println("e7f8g9h0 | 开始测试特殊字符输入")

        val datasourceId = ManualLineageTestDataUtils.getAvailableTestDatasourceId()!!

        val specialCharUsers = listOf(
            "<EMAIL>",
            "用户-中文",
            "user_with_underscores",
            "user-with-dashes",
            "user.with.dots",
            "user with spaces",
            "user!@#$%^&*()",
            "user🚀emoji",
            "user'quote",
            "user\"doublequote"
        )

        specialCharUsers.forEach { user ->
            val requestBody = mapOf(
                "datasourceId" to datasourceId,
                "triggerUser" to user
            )

            try {
                given()
                    .contentType(ContentType.JSON)
                    .body(requestBody)
                    .`when`()
                    .post("$MANUAL_TASKS_BASE_PATH/trigger")
                    .then()
                    .statusCode(anyOf(equalTo(200), equalTo(400)))

                println("i1j2k3l4 | 特殊字符用户测试完成: $user")
            } catch (e: Exception) {
                println("m5n6o7p8 | 特殊字符用户测试异常: $user, error=${e.message}")
            }
        }

        println("q9r0s1t2 | 特殊字符输入测试完成")
    }

    @Test
    @Order(8)
    @DisplayName("应该测试响应时间性能 - Should test response time performance")
    fun `should test response time performance`() {
        println("u3v4w5x6 | 开始测试响应时间性能")

        val maxAcceptableResponseTime = 5000L // 5秒

        // 测试触发任务的响应时间
        val datasourceId = ManualLineageTestDataUtils.getAvailableTestDatasourceId()!!
        val requestBody = ManualLineageTestDataUtils.createTriggerRequest(
            datasourceId = datasourceId,
            triggerUser = "performance-test-user"
        )

        val startTime = System.currentTimeMillis()

        val response = given()
            .contentType(ContentType.JSON)
            .body(requestBody)
            .`when`()
            .post("$MANUAL_TASKS_BASE_PATH/trigger")
            .then()
            .statusCode(200)
            .extract()
            .response()

        val responseTime = System.currentTimeMillis() - startTime

        assertThat(responseTime)
            .`as`("触发任务API响应时间应该在可接受范围内")
            .isLessThan(maxAcceptableResponseTime)

        val taskUuid = JsonPath.from(response.asString()).getString("taskUuid")
        createdTaskUuids.add(taskUuid)

        // 测试查询任务列表的响应时间
        val listStartTime = System.currentTimeMillis()

        given()
            .queryParam("page", 0)
            .queryParam("size", 20)
            .`when`()
            .get(MANUAL_TASKS_BASE_PATH)
            .then()
            .statusCode(200)

        val listResponseTime = System.currentTimeMillis() - listStartTime

        assertThat(listResponseTime)
            .`as`("查询任务列表API响应时间应该在可接受范围内")
            .isLessThan(maxAcceptableResponseTime)

        println("y7z8a9b0 | 性能测试完成: 触发任务响应时间=${responseTime}ms, 查询列表响应时间=${listResponseTime}ms")
    }

    @Test
    @Order(9)
    @DisplayName("应该验证JSON格式错误处理 - Should validate JSON format error handling")
    fun `should validate JSON format error handling`() {
        println("c1d2e3f4 | 开始测试JSON格式错误处理")

        // 测试无效的JSON格式
        val invalidJsons = listOf(
            "{invalid json}",
            "{\"datasourceId\": }",
            "{\"datasourceId\": \"not_a_number\"}",
            "not json at all",
            "",
            "null",
            "{\"datasourceId\": null}"
        )

        invalidJsons.forEach { invalidJson ->
            try {
                given()
                    .contentType(ContentType.JSON)
                    .body(invalidJson)
                    .`when`()
                    .post("$MANUAL_TASKS_BASE_PATH/trigger")
                    .then()
                    .statusCode(400)  // 应该返回400错误

                println("g5h6i7j8 | 无效JSON测试完成: $invalidJson")
            } catch (e: Exception) {
                println("k9l0m1n2 | 无效JSON测试异常: $invalidJson, error=${e.message}")
            }
        }

        println("o3p4q5r6 | JSON格式错误处理测试完成")
    }

    @Test
    @Order(10)
    @DisplayName("应该测试大量数据的分页性能 - Should test pagination performance with large datasets")
    fun `should test pagination performance with large datasets`() {
        println("s7t8u9v0 | 开始测试大量数据分页性能")

        val largePageSizes = listOf(100, 500, 1000)

        largePageSizes.forEach { pageSize ->
            val startTime = System.currentTimeMillis()

            given()
                .queryParam("page", 0)
                .queryParam("size", pageSize)
                .`when`()
                .get(MANUAL_TASKS_BASE_PATH)
                .then()
                .statusCode(200)
                .body("size", equalTo(pageSize))

            val responseTime = System.currentTimeMillis() - startTime

            assertThat(responseTime)
                .`as`("大分页查询响应时间应该在可接受范围内")
                .isLessThan(10000L)  // 10秒

            println("w1x2y3z4 | 大分页测试完成: pageSize=$pageSize, responseTime=${responseTime}ms")
        }

        println("a5b6c7d8 | 大量数据分页性能测试完成")
    }

    @Test
    @Order(11)
    @DisplayName("应该验证任务状态一致性 - Should validate task status consistency")
    fun `should validate task status consistency`() {
        println("e9f0g1h2 | 开始测试任务状态一致性")

        // 检查已创建的任务状态是否一致
        createdTaskUuids.forEach { taskUuid ->
            val detailResponse = given()
                .pathParam("taskUuid", taskUuid)
                .`when`()
                .get("$MANUAL_TASKS_BASE_PATH/{taskUuid}")
                .then()
                .statusCode(200)
                .extract()
                .response()

            val detailJson = JsonPath.from(detailResponse.asString())
            val taskStatus = detailJson.getString("taskStatus")
            val successCount = detailJson.getInt("successCount")
            val failureCount = detailJson.getInt("failureCount")
            val totalCount = detailJson.getInt("totalCount")

            // 验证计数一致性
            if (taskStatus in listOf("SUCCESS", "FAILED")) {
                assertThat(totalCount)
                    .`as`("总计数应该等于成功计数加失败计数")
                    .isEqualTo(successCount + failureCount)
            }

            ManualLineageTestDataUtils.validateTaskStatus(taskStatus)

            println("i3j4k5l6 | 任务状态一致性验证完成: taskUuid=$taskUuid, status=$taskStatus")
        }

        println("m7n8o9p0 | 任务状态一致性测试完成")
    }

    @Test
    @Order(99)
    @DisplayName("清理高级测试数据 - Cleanup advanced test data")
    fun `cleanup advanced test data`() {
        println("q1r2s3t4 | 开始清理高级测试数据")

        // 记录创建的任务UUID
        if (createdTaskUuids.isNotEmpty()) {
            println("u5v6w7x8 | 高级测试创建的任务UUID: ${createdTaskUuids.joinToString(", ")}")
            println("y9z0a1b2 | 注意：根据只读测试原则，不删除测试数据")
        }

        println("c3d4e5f6 | 手动触发血缘任务API高级集成测试全部完成")
    }
}