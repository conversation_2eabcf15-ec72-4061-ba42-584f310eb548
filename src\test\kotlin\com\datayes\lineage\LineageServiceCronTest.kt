package com.datayes.lineage

import com.datayes.scheduler.SystemScheduleInfo
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.*

/**
 * LineageService Cron 方法单元测试
 */
@DisplayName("LineageService Cron 方法测试")
class LineageServiceCronTest {

    @Mock
    private lateinit var lineageRepository: LineageRepository

    private lateinit var lineageService: LineageService

    @BeforeEach
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        lineageService = LineageService(lineageRepository)
    }

    @Test
    @DisplayName("应该成功查询具有cron表达式的系统")
    fun `should successfully find systems with cron expression`() {
        // Given
        val expectedSystems = listOf(
            SystemScheduleInfo(
                id = 1L,
                systemName = "数据交互平台",
                systemCode = "DATA_EXCHANGE_PLATFORM",
                status = "ACTIVE",
                cronExpression = "0 0 2 * * ?",
                description = "内部数据交互平台系统"
            ),
            SystemScheduleInfo(
                id = 2L,
                systemName = "外部系统",
                systemCode = "EXTERNAL_SYSTEM",
                status = "ACTIVE",
                cronExpression = "0 0 6 1 * ?",
                description = "外部第三方系统"
            )
        )

        whenever(lineageRepository.findSystemsWithCronExpression()).thenReturn(expectedSystems)

        // When
        val result = lineageService.findSystemsWithCronExpression()

        // Then
        assertThat(result).isNotNull
        assertThat(result).hasSize(2)
        assertThat(result[0].id).isEqualTo(1L)
        assertThat(result[0].systemName).isEqualTo("数据交互平台")
        assertThat(result[0].systemCode).isEqualTo("DATA_EXCHANGE_PLATFORM")
        assertThat(result[0].status).isEqualTo("ACTIVE")
        assertThat(result[0].cronExpression).isEqualTo("0 0 2 * * ?")
        assertThat(result[0].description).isEqualTo("内部数据交互平台系统")

        assertThat(result[1].id).isEqualTo(2L)
        assertThat(result[1].systemName).isEqualTo("外部系统")
        assertThat(result[1].systemCode).isEqualTo("EXTERNAL_SYSTEM")
        assertThat(result[1].status).isEqualTo("ACTIVE")
        assertThat(result[1].cronExpression).isEqualTo("0 0 6 1 * ?")
        assertThat(result[1].description).isEqualTo("外部第三方系统")

        verify(lineageRepository).findSystemsWithCronExpression()
    }

    @Test
    @DisplayName("当没有系统具有cron表达式时应该返回空列表")
    fun `should return empty list when no systems have cron expression`() {
        // Given
        whenever(lineageRepository.findSystemsWithCronExpression()).thenReturn(emptyList())

        // When
        val result = lineageService.findSystemsWithCronExpression()

        // Then
        assertThat(result).isNotNull
        assertThat(result).isEmpty()

        verify(lineageRepository).findSystemsWithCronExpression()
    }

    @Test
    @DisplayName("应该成功根据系统ID查询系统调度信息")
    fun `should successfully find system schedule info by system id`() {
        // Given
        val systemId = 1L
        val expectedSystemInfo = SystemScheduleInfo(
            id = systemId,
            systemName = "数据交互平台",
            systemCode = "DATA_EXCHANGE_PLATFORM",
            status = "ACTIVE",
            cronExpression = "0 0 2 * * ?",
            description = "内部数据交互平台系统"
        )

        whenever(lineageRepository.findSystemScheduleInfo(systemId)).thenReturn(expectedSystemInfo)

        // When
        val result = lineageService.findSystemScheduleInfo(systemId)

        // Then
        assertThat(result).isNotNull
        assertThat(result!!.id).isEqualTo(systemId)
        assertThat(result.systemName).isEqualTo("数据交互平台")
        assertThat(result.systemCode).isEqualTo("DATA_EXCHANGE_PLATFORM")
        assertThat(result.status).isEqualTo("ACTIVE")
        assertThat(result.cronExpression).isEqualTo("0 0 2 * * ?")
        assertThat(result.description).isEqualTo("内部数据交互平台系统")

        verify(lineageRepository).findSystemScheduleInfo(systemId)
    }

    @Test
    @DisplayName("当系统不存在时应该返回null")
    fun `should return null when system does not exist`() {
        // Given
        val nonExistentSystemId = 999L
        whenever(lineageRepository.findSystemScheduleInfo(nonExistentSystemId)).thenReturn(null)

        // When
        val result = lineageService.findSystemScheduleInfo(nonExistentSystemId)

        // Then
        assertThat(result).isNull()

        verify(lineageRepository).findSystemScheduleInfo(nonExistentSystemId)
    }

    @Test
    @DisplayName("应该正确处理repository异常")
    fun `should handle repository exceptions correctly`() {
        // Given
        whenever(lineageRepository.findSystemsWithCronExpression())
            .thenThrow(RuntimeException("数据库连接失败"))

        // When & Then
        try {
            lineageService.findSystemsWithCronExpression()
            assertThat(false).withFailMessage("应该抛出异常").isTrue()
        } catch (e: RuntimeException) {
            assertThat(e.message).isEqualTo("数据库连接失败")
        }

        verify(lineageRepository).findSystemsWithCronExpression()
    }

    @Test
    @DisplayName("应该正确传递参数到repository层")
    fun `should correctly pass parameters to repository layer`() {
        // Given
        val systemId = 42L
        whenever(lineageRepository.findSystemScheduleInfo(systemId)).thenReturn(null)

        // When
        lineageService.findSystemScheduleInfo(systemId)

        // Then
        verify(lineageRepository).findSystemScheduleInfo(eq(systemId))
    }

    @Test
    @DisplayName("应该处理具有null cron表达式的系统")
    fun `should handle systems with null cron expression`() {
        // Given
        val systemsWithNullCron = listOf(
            SystemScheduleInfo(
                id = 1L,
                systemName = "系统1",
                systemCode = "SYSTEM_1",
                status = "ACTIVE",
                cronExpression = "0 0 2 * * ?",
                description = "有cron表达式的系统"
            ),
            SystemScheduleInfo(
                id = 2L,
                systemName = "系统2",
                systemCode = "SYSTEM_2",
                status = "ACTIVE",
                cronExpression = null, // null cron表达式
                description = "没有cron表达式的系统"
            )
        )

        whenever(lineageRepository.findSystemsWithCronExpression()).thenReturn(systemsWithNullCron)

        // When
        val result = lineageService.findSystemsWithCronExpression()

        // Then
        assertThat(result).hasSize(2)
        assertThat(result[0].cronExpression).isEqualTo("0 0 2 * * ?")
        assertThat(result[1].cronExpression).isNull()

        verify(lineageRepository).findSystemsWithCronExpression()
    }

    @Test
    @DisplayName("应该处理具有空字符串cron表达式的系统")
    fun `should handle systems with empty string cron expression`() {
        // Given
        val systemsWithEmptyCron = listOf(
            SystemScheduleInfo(
                id = 1L,
                systemName = "系统1",
                systemCode = "SYSTEM_1",
                status = "ACTIVE",
                cronExpression = "",
                description = "空cron表达式的系统"
            )
        )

        whenever(lineageRepository.findSystemsWithCronExpression()).thenReturn(systemsWithEmptyCron)

        // When
        val result = lineageService.findSystemsWithCronExpression()

        // Then
        assertThat(result).hasSize(1)
        assertThat(result[0].cronExpression).isEmpty()

        verify(lineageRepository).findSystemsWithCronExpression()
    }

    @Test
    @DisplayName("应该正确处理不同状态的系统")
    fun `should correctly handle systems with different statuses`() {
        // Given
        val systemsWithDifferentStatuses = listOf(
            SystemScheduleInfo(
                id = 1L,
                systemName = "活跃系统",
                systemCode = "ACTIVE_SYSTEM",
                status = "ACTIVE",
                cronExpression = "0 0 2 * * ?",
                description = "活跃状态的系统"
            ),
            SystemScheduleInfo(
                id = 2L,
                systemName = "非活跃系统",
                systemCode = "INACTIVE_SYSTEM",
                status = "INACTIVE",
                cronExpression = "0 0 6 * * ?",
                description = "非活跃状态的系统"
            )
        )

        whenever(lineageRepository.findSystemsWithCronExpression()).thenReturn(systemsWithDifferentStatuses)

        // When
        val result = lineageService.findSystemsWithCronExpression()

        // Then
        assertThat(result).hasSize(2)
        assertThat(result[0].status).isEqualTo("ACTIVE")
        assertThat(result[1].status).isEqualTo("INACTIVE")

        verify(lineageRepository).findSystemsWithCronExpression()
    }
}