package com.datayes.lineage

import org.slf4j.LoggerFactory
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.jdbc.core.RowMapper
import org.springframework.stereotype.Service
import java.time.LocalDateTime

/**
 * 血缘表查询服务 (Lineage Table Query Service)
 *
 * 专门处理根据元数据系统信息查询血缘表的业务逻辑
 */
@Service
class LineageTableQueryService(
    private val jdbcTemplate: JdbcTemplate
) {

    private val logger = LoggerFactory.getLogger(LineageTableQueryService::class.java)

    /**
     * 根据元数据系统信息ID查询所有血缘表 (Query all lineage tables by metadata system info ID)
     * 
     * 当 metadataSystemId 为 null 时，返回所有血缘表
     * 支持分页查询，页码从1开始
     * 支持模糊匹配过滤条件：host、databaseName、schema、tableName
     * 
     * @param metadataSystemId 元数据系统信息ID，null时查询所有表
     * @param host 主机地址模糊匹配，可选
     * @param databaseName 数据库名称模糊匹配，可选
     * @param schema 模式名称模糊匹配，可选
     * @param tableName 表名称模糊匹配，可选
     * @param page 页码，从1开始
     * @param size 每页大小
     * @return 分页后的血缘表信息
     */
    fun queryLineageTablesBySystemId(
        metadataSystemId: Long?,
        host: String? = null,
        databaseName: String? = null,
        schema: String? = null,
        tableName: String? = null,
        page: Int = 1,
        size: Int = 20
    ): PagedLineageTablesDto {
        logger.info("a1b2c3d4 | 查询血缘表: metadataSystemId=$metadataSystemId, host=$host, databaseName=$databaseName, schema=$schema, tableName=$tableName, page=$page, size=$size")

        // 计算偏移量 (页码从1开始)
        val offset = (page - 1) * size

        val (whereClause, countWhereClause, parameters) = buildQueryConditions(
            metadataSystemId, host, databaseName, schema, tableName
        )

        // 查询总数
        val totalCountSql = """
            SELECT COUNT(DISTINCT lt.id)
            FROM lineage_tables lt
            JOIN lineage_datasources lds ON lt.datasource_id = lds.id
            $countWhereClause
        """.trimIndent()

        val totalCount = jdbcTemplate.queryForObject(
            totalCountSql, 
            Int::class.java, 
            *parameters.toTypedArray()
        ) ?: 0

        logger.debug("b5c6d7e8 | 查询到血缘表总数: $totalCount")

        // 查询分页数据
        val dataSql = """
            SELECT DISTINCT
                lt.id,
                lt.table_name,
                lt.schema_name,
                lt.table_type,
                lt.chinese_name,
                lt.description,
                lt.sync_frequency,
                lt.requirement_id,
                lt.status,
                lt.created_at,
                lt.updated_at,
                lt.reference_count,
                lt.last_referenced_at,
                lds.id as datasource_id,
                lds.datasource_name,
                lds.db_type,
                lds.host,
                lds.port,
                lds.database_name,
                lds.connection_string,
                lds.status as datasource_status,
                lds.system_id,
                msi.SYSTEM_NAME as metadata_system_name,
                msi.SYSTEM_ABBREVIATION as metadata_system_abbreviation,
                msi.SYSTEM_TYPE as metadata_system_type
            FROM lineage_tables lt
            JOIN lineage_datasources lds ON lt.datasource_id = lds.id
            $whereClause
            ORDER BY lt.updated_at DESC, lt.table_name ASC
            LIMIT ? OFFSET ?
        """.trimIndent()

        val queryParameters = parameters.toMutableList()
        queryParameters.add(size)
        queryParameters.add(offset)

        val tables = jdbcTemplate.query(
            dataSql,
            lineageTableRowMapper,
            *queryParameters.toTypedArray()
        )

        logger.info("f9g0h1i2 | 查询完成: 返回${tables.size}条记录，总数$totalCount")

        // 计算分页信息
        val totalPages = if (totalCount > 0) ((totalCount - 1) / size) + 1 else 0
        val hasNext = page < totalPages
        val hasPrevious = page > 1

        return PagedLineageTablesDto(
            content = tables,
            page = page,
            size = size,
            totalElements = totalCount,
            totalPages = totalPages,
            hasNext = hasNext,
            hasPrevious = hasPrevious,
            isFirst = page == 1,
            isLast = !hasNext,
            numberOfElements = tables.size
        )
    }

    /**
     * 构建查询条件 (Build query conditions)
     */
    private fun buildQueryConditions(
        metadataSystemId: Long?,
        host: String?,
        databaseName: String?,
        schema: String?,
        tableName: String?
    ): Triple<String, String, List<Any>> {
        val conditions = mutableListOf<String>()
        val parameters = mutableListOf<Any>()

        // 基础条件
        conditions.add("lt.status = 'ACTIVE'")
        conditions.add("lds.status = 'ACTIVE'")

        // 元数据系统ID过滤
        if (metadataSystemId != null) {
            conditions.add("msi.ID = ?")
            conditions.add("msi.ACTIVE_FLAG = 1")
            parameters.add(metadataSystemId)
        }

        // Host模糊匹配
        if (!host.isNullOrBlank()) {
            conditions.add("lds.host LIKE ?")
            parameters.add("%${host.trim()}%")
        }

        // DatabaseName模糊匹配
        if (!databaseName.isNullOrBlank()) {
            conditions.add("lds.database_name LIKE ?")
            parameters.add("%${databaseName.trim()}%")
        }

        // Schema模糊匹配
        if (!schema.isNullOrBlank()) {
            conditions.add("(lt.schema_name LIKE ? OR lt.schema_name IS NULL)")
            parameters.add("%${schema.trim()}%")
        }

        // TableName模糊匹配
        if (!tableName.isNullOrBlank()) {
            conditions.add("lt.table_name LIKE ?")
            parameters.add("%${tableName.trim()}%")
        }

        val joinClause = """
            LEFT JOIN metadata_data_source mds ON (
                LOWER(lds.db_type) = LOWER(mds.DB_TYPE)
                AND lds.database_name = mds.DB_NAME
                AND (
                    (mds.DB_URL IS NOT NULL AND lds.host LIKE CONCAT('%', SUBSTRING_INDEX(SUBSTRING_INDEX(mds.DB_URL, '://', -1), ':', 1), '%'))
                    OR (mds.CUSTOM_JDBC_URL IS NOT NULL AND mds.CUSTOM_JDBC_URL LIKE CONCAT('%', lds.host, '%'))
                    OR lds.host = SUBSTRING_INDEX(SUBSTRING_INDEX(COALESCE(mds.DB_URL, mds.CUSTOM_JDBC_URL, ''), '://', -1), ':', 1)
                )
                AND (mds.DB_PORT IS NULL OR lds.port = mds.DB_PORT)
                AND mds.ACTIVE_FLAG = 1
            )
            LEFT JOIN metadata_system_info msi ON mds.SYSTEM_ID = msi.ID
        """.trimIndent()

        val whereCondition = if (conditions.isNotEmpty()) {
            "WHERE ${conditions.joinToString(" AND ")}"
        } else {
            ""
        }

        val whereClause = "$joinClause $whereCondition"
        val countWhereClause = "$joinClause $whereCondition"

        logger.debug("w5x6y7z8 | 构建查询条件: conditions=${conditions.size}, parameters=${parameters.size}")

        return Triple(whereClause, countWhereClause, parameters)
    }

    /**
     * 血缘表行映射器 (Lineage Table Row Mapper)
     */
    private val lineageTableRowMapper = RowMapper<LineageTableDto> { rs, _ ->
        LineageTableDto(
            id = rs.getLong("id"),
            tableName = rs.getString("table_name"),
            schemaName = rs.getString("schema_name"),
            tableType = rs.getString("table_type"),
            chineseName = rs.getString("chinese_name"),
            description = rs.getString("description"),
            syncFrequency = rs.getString("sync_frequency"),
            requirementId = rs.getString("requirement_id"),
            status = rs.getString("status"),
            createdAt = rs.getTimestamp("created_at")?.toLocalDateTime(),
            updatedAt = rs.getTimestamp("updated_at")?.toLocalDateTime(),
            referenceCount = rs.getInt("reference_count"),
            lastReferencedAt = rs.getTimestamp("last_referenced_at")?.toLocalDateTime(),
            datasource = LineageDatasourceDto(
                id = rs.getLong("datasource_id"),
                datasourceName = rs.getString("datasource_name"),
                dbType = rs.getString("db_type"),
                host = rs.getString("host"),
                port = rs.getInt("port"),
                databaseName = rs.getString("database_name"),
                status = rs.getString("datasource_status"),
                systemId = rs.getObject("system_id") as? Long,
                connectionString = rs.getString("connection_string")
            ),
            metadataSystemInfo = if (rs.getString("metadata_system_name") != null) {
                MetadataSystemInfoDto(
                    systemName = rs.getString("metadata_system_name"),
                    systemAbbreviation = rs.getString("metadata_system_abbreviation"),
                    systemType = rs.getString("metadata_system_type")
                )
            } else null
        )
    }
}

/**
 * 分页血缘表DTO (Paged Lineage Tables DTO)
 */
data class PagedLineageTablesDto(
    val content: List<LineageTableDto>,
    val page: Int,
    val size: Int,
    val totalElements: Int,
    val totalPages: Int,
    val hasNext: Boolean,
    val hasPrevious: Boolean,
    val isFirst: Boolean,
    val isLast: Boolean,
    val numberOfElements: Int
)

/**
 * 血缘表DTO (Lineage Table DTO)
 */
data class LineageTableDto(
    val id: Long,
    val tableName: String,
    val schemaName: String?,
    val tableType: String?,
    val chineseName: String?,
    val description: String?,
    val syncFrequency: String?,
    val requirementId: String?,
    val status: String,
    val createdAt: LocalDateTime?,
    val updatedAt: LocalDateTime?,
    val referenceCount: Int,
    val lastReferencedAt: LocalDateTime?,
    val datasource: LineageDatasourceDto,
    val metadataSystemInfo: MetadataSystemInfoDto?
)

/**
 * 血缘数据源DTO (Lineage Datasource DTO)
 */
data class LineageDatasourceDto(
    val id: Long,
    val datasourceName: String,
    val dbType: String,
    val host: String,
    val port: Int,
    val databaseName: String,
    val status: String,
    val systemId: Long?,
    val connectionString: String
)

/**
 * 元数据系统信息DTO (Metadata System Info DTO)
 */
data class MetadataSystemInfoDto(
    val systemName: String,
    val systemAbbreviation: String?,
    val systemType: String?
)