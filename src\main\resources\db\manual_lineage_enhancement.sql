-- ====================================================================
-- 手动血缘管理数据库表结构增强 (Manual Lineage Management Database Enhancement)
-- 
-- UC-14, UC-15, UC-16: 支持手动血缘记录的创建、编辑和删除
-- ====================================================================

-- 1. 列映射表已移除 (Column Mappings Table Removed)
-- 列映射现在直接存储在 lineage_relationships 表中作为 COLUMN_LEVEL 关系
-- Column mappings are now stored directly in lineage_relationships table as COLUMN_LEVEL relationships
-- 
-- 迁移说明：
-- - 原 lineage_column_mappings 表的功能已合并到 lineage_relationships 表
-- - 使用 relationship_type = 'COLUMN_LEVEL' 来表示列级血缘关系
-- - 列映射的详细信息存储在 transformation_type, transformation_description, transformation_expression 字段中
-- - 要迁移现有数据，请运行 migrate_column_mappings_to_column_level.sql 脚本

-- 2. 手动血缘审核日志表 (Manual Lineage Audit Log)
-- 记录手动血缘的所有变更操作，用于审计和追踪
CREATE TABLE IF NOT EXISTS manual_lineage_audit_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    relationship_id BIGINT NOT NULL COMMENT '血缘关系ID',
    operation_type ENUM('CREATE', 'UPDATE', 'DELETE', 'RESTORE') NOT NULL COMMENT '操作类型',
    operation_details JSON COMMENT '操作详情JSON',
    old_values JSON COMMENT '变更前的值',
    new_values JSON COMMENT '变更后的值',
    performed_by VARCHAR(100) NOT NULL COMMENT '操作人',
    performed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    reason TEXT COMMENT '操作原因',
    client_ip VARCHAR(45) COMMENT '客户端IP',
    user_agent TEXT COMMENT '用户代理',
    
    INDEX idx_relationship_id (relationship_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_performed_by (performed_by),
    INDEX idx_performed_at (performed_at),
    -- 复合索引用于审计查询
    INDEX idx_audit_query (relationship_id, operation_type, performed_at),
    
    FOREIGN KEY (relationship_id) REFERENCES lineage_relationships(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='手动血缘审核日志表';

-- 3. 手动血缘模板表 (Manual Lineage Templates)
-- 存储常用的血缘模板，便于快速创建血缘关系
CREATE TABLE IF NOT EXISTS manual_lineage_templates (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    template_name VARCHAR(200) NOT NULL COMMENT '模板名称',
    template_description TEXT COMMENT '模板描述',
    source_system VARCHAR(100) COMMENT '源系统',
    target_system VARCHAR(100) COMMENT '目标系统',
    lineage_type ENUM('DIRECT_COPY', 'SQL_QUERY', 'AGGREGATION', 'JOIN', 'FILTER', 'COMPLEX_TRANSFORMATION') NOT NULL COMMENT '血缘类型',
    template_config JSON COMMENT '模板配置JSON',
    column_mapping_template JSON COMMENT '列映射模板JSON',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_by VARCHAR(100) NOT NULL COMMENT '创建者',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_template_name (template_name),
    INDEX idx_source_system (source_system),
    INDEX idx_target_system (target_system),
    INDEX idx_lineage_type (lineage_type),
    INDEX idx_created_by (created_by),
    INDEX idx_is_active (is_active),
    INDEX idx_usage_count (usage_count)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='手动血缘模板表';

-- ====================================================================
-- 对现有表结构的增强 (Enhancements to Existing Tables)
-- ====================================================================

-- 为 lineage_relationships 表添加手动血缘管理需要的字段
ALTER TABLE lineage_relationships 
ADD COLUMN IF NOT EXISTS source_system VARCHAR(50) DEFAULT 'SYSTEM_COLLECTED' COMMENT '血缘来源系统（MANUAL_INPUT, SCRIPT_ANALYSIS等）',
ADD COLUMN IF NOT EXISTS last_updated_by VARCHAR(100) COMMENT '最后更新人',
ADD COLUMN IF NOT EXISTS job_key VARCHAR(200) COMMENT '作业唯一标识',
ADD COLUMN IF NOT EXISTS content_hash VARCHAR(64) COMMENT '内容哈希值（用于变更检测）',
ADD COLUMN IF NOT EXISTS description TEXT COMMENT '血缘关系描述',
ADD COLUMN IF NOT EXISTS reference_count INT DEFAULT 0 COMMENT '引用计数',
ADD COLUMN IF NOT EXISTS last_referenced_at TIMESTAMP NULL COMMENT '最后引用时间';

-- 为 lineage_tables 表添加引用计数字段
ALTER TABLE lineage_tables 
ADD COLUMN IF NOT EXISTS reference_count INT DEFAULT 0 COMMENT '引用计数',
ADD COLUMN IF NOT EXISTS last_referenced_at TIMESTAMP NULL COMMENT '最后引用时间';

-- 为 lineage_columns 表添加引用计数字段  
ALTER TABLE lineage_columns 
ADD COLUMN IF NOT EXISTS reference_count INT DEFAULT 0 COMMENT '引用计数',
ADD COLUMN IF NOT EXISTS last_referenced_at TIMESTAMP NULL COMMENT '最后引用时间';

-- ====================================================================
-- 创建索引优化查询性能 (Create Indexes for Query Optimization)
-- ====================================================================

-- 手动血缘查询优化索引
CREATE INDEX IF NOT EXISTS idx_manual_lineage_query 
ON lineage_relationships (source_system, is_active, created_at);

-- 血缘内容哈希索引
CREATE INDEX IF NOT EXISTS idx_content_hash 
ON lineage_relationships (content_hash);

-- 作业键索引
CREATE INDEX IF NOT EXISTS idx_job_key 
ON lineage_relationships (job_key);

-- 引用计数索引
CREATE INDEX IF NOT EXISTS idx_table_reference_count 
ON lineage_tables (reference_count DESC);

CREATE INDEX IF NOT EXISTS idx_column_reference_count 
ON lineage_columns (reference_count DESC);

-- ====================================================================
-- 插入初始数据 (Insert Initial Data)
-- ====================================================================

-- 插入一些常用的手动血缘模板
INSERT INTO manual_lineage_templates (
    template_name, template_description, lineage_type, 
    template_config, created_by
) VALUES 
(
    '直接复制模板', 
    '用于表到表的直接数据复制，无复杂转换', 
    'DIRECT_COPY',
    '{"defaultConfidenceScore": 0.95, "requireColumnMapping": true, "allowBulkMapping": true}',
    'system'
),
(
    'ETL转换模板', 
    '用于包含聚合、连接等复杂转换的ETL过程', 
    'COMPLEX_TRANSFORMATION',
    '{"defaultConfidenceScore": 0.85, "requireColumnMapping": true, "allowBulkMapping": false, "requireDescription": true}',
    'system'
),
(
    '数据过滤模板', 
    '用于数据过滤或筛选场景', 
    'FILTER',
    '{"defaultConfidenceScore": 0.90, "requireColumnMapping": false, "allowBulkMapping": true}',
    'system'
);

-- ====================================================================
-- 创建视图用于快速查询 (Create Views for Quick Queries)
-- ====================================================================

-- 手动血缘关系概览视图
CREATE OR REPLACE VIEW manual_lineage_overview AS
SELECT 
    lr.id as relationship_id,
    lr.lineage_type,
    lr.description,
    lr.confidence_score,
    lr.created_by,
    lr.created_at,
    lr.last_updated_by,
    lr.updated_at,
    st.table_name as source_table,
    st.schema_name as source_schema,
    sds.datasource_name as source_datasource,
    ssys.system_name as source_system_name,
    tt.table_name as target_table,
    tt.schema_name as target_schema,
    tds.datasource_name as target_datasource,
    tsys.system_name as target_system_name,
    (SELECT COUNT(*) FROM lineage_relationships clr 
     WHERE clr.relationship_type = 'COLUMN_LEVEL' 
       AND clr.source_table_id = lr.source_table_id 
       AND clr.target_table_id = lr.target_table_id
       AND clr.source_system = 'MANUAL_INPUT' 
       AND clr.is_active = true) as column_mapping_count
FROM lineage_relationships lr
JOIN lineage_tables st ON lr.source_table_id = st.id
JOIN lineage_tables tt ON lr.target_table_id = tt.id
JOIN lineage_datasources sds ON st.datasource_id = sds.id
JOIN lineage_datasources tds ON tt.datasource_id = tds.id
LEFT JOIN lineage_systems ssys ON sds.system_id = ssys.id
LEFT JOIN lineage_systems tsys ON tds.system_id = tsys.id
WHERE lr.source_system = 'MANUAL_INPUT' AND lr.is_active = true;

-- 手动血缘统计视图
CREATE OR REPLACE VIEW manual_lineage_statistics AS
SELECT 
    COUNT(*) as total_manual_relationships,
    AVG(confidence_score) as avg_confidence_score,
    COUNT(DISTINCT created_by) as unique_creators,
    MAX(created_at) as latest_creation,
    (SELECT COUNT(*) FROM lineage_relationships lr 
     WHERE lr.relationship_type = 'COLUMN_LEVEL' 
       AND lr.source_system = 'MANUAL_INPUT' AND lr.is_active = true) as total_column_mappings
FROM lineage_relationships 
WHERE source_system = 'MANUAL_INPUT' AND is_active = true; 